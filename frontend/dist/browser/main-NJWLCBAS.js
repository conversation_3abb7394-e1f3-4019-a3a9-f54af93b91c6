var ku=Object.defineProperty,Fy=Object.defineProperties;var Ny=Object.getOwnPropertyDescriptors;var mu=Object.getOwnPropertySymbols;var Oy=Object.prototype.hasOwnProperty,Vy=Object.prototype.propertyIsEnumerable;var gu=(e,t,n)=>t in e?ku(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,A=(e,t)=>{for(var n in t||={})Oy.call(t,n)&&gu(e,n,t[n]);if(mu)for(var n of mu(t))Vy.call(t,n)&&gu(e,n,t[n]);return e},G=(e,t)=>Fy(e,Ny(t));var Sc=(e,t)=>{for(var n in t)ku(e,n,{get:t[n],enumerable:!0})};var O=(e,t,n)=>new Promise((r,i)=>{var o=c=>{try{s(n.next(c))}catch(d){i(d)}},a=c=>{try{s(n.throw(c))}catch(d){i(d)}},s=c=>c.done?r(c.value):Promise.resolve(c.value).then(o,a);s((n=n.apply(e,t)).next())});var _c;function Ec(){return _c}function Ft(e){let t=_c;return _c=e,t}var jy=Symbol("NotFound"),mo=class extends Error{name="\u0275NotFound";constructor(t){super(t)}};function sr(e){return e===jy||e?.name==="\u0275NotFound"}function wo(e,t){return Object.is(e,t)}var fe=null,go=!1,Lc=1,Hy=null,je=Symbol("SIGNAL");function T(e){let t=fe;return fe=e,t}function Co(){return fe}var Mn={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function xo(e){if(go)throw new Error("");if(fe===null)return;fe.consumerOnSignalRead(e);let t=fe.nextProducerIndex++;if(_o(fe),t<fe.producerNode.length&&fe.producerNode[t]!==e&&ni(fe)){let n=fe.producerNode[t];So(n,fe.producerIndexOfThis[t])}fe.producerNode[t]!==e&&(fe.producerNode[t]=e,fe.producerIndexOfThis[t]=ni(fe)?Mu(e,fe,t):0),fe.producerLastReadVersion[t]=e.version}function vu(){Lc++}function Io(e){if(!(ni(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Lc)){if(!e.producerMustRecompute(e)&&!bo(e)){Mo(e);return}e.producerRecomputeValue(e),Mo(e)}}function Ac(e){if(e.liveConsumerNode===void 0)return;let t=go;go=!0;try{for(let n of e.liveConsumerNode)n.dirty||By(n)}finally{go=t}}function Tc(){return fe?.consumerAllowSignalWrites!==!1}function By(e){e.dirty=!0,Ac(e),e.consumerMarkedDirty?.(e)}function Mo(e){e.dirty=!1,e.lastCleanEpoch=Lc}function cr(e){return e&&(e.nextProducerIndex=0),T(e)}function ri(e,t){if(T(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(ni(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)So(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function bo(e){_o(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Io(n),r!==n.version))return!0}return!1}function Do(e){if(_o(e),ni(e))for(let t=0;t<e.producerNode.length;t++)So(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Mu(e,t,n){if(wu(e),e.liveConsumerNode.length===0&&Cu(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Mu(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function So(e,t){if(wu(e),e.liveConsumerNode.length===1&&Cu(e))for(let r=0;r<e.producerNode.length;r++)So(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],i=e.liveConsumerNode[t];_o(i),i.producerIndexOfThis[r]=t}}function ni(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function _o(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function wu(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Cu(e){return e.producerNode!==void 0}function Eo(e){Hy?.(e)}function Lo(e,t){let n=Object.create(qy);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(Io(n),xo(n),n.value===ti)throw n.error;return n.value};return r[je]=n,Eo(n),r}var ko=Symbol("UNSET"),vo=Symbol("COMPUTING"),ti=Symbol("ERRORED"),qy=G(A({},Mn),{value:ko,dirty:!0,error:null,equal:wo,kind:"computed",producerMustRecompute(e){return e.value===ko||e.value===vo},producerRecomputeValue(e){if(e.value===vo)throw new Error("");let t=e.value;e.value=vo;let n=cr(e),r,i=!1;try{r=e.computation(),T(null),i=t!==ko&&t!==ti&&r!==ti&&e.equal(t,r)}catch(o){r=ti,e.error=o}finally{ri(e,n)}if(i){e.value=t;return}e.value=r,e.version++}});function zy(){throw new Error}var xu=zy;function Iu(e){xu(e)}function Rc(e){xu=e}var Uy=null;function Pc(e,t){let n=Object.create(Du);n.value=e,t!==void 0&&(n.equal=t);let r=()=>bu(n);return r[je]=n,Eo(n),[r,a=>Ao(n,a),a=>Fc(n,a)]}function bu(e){return xo(e),e.value}function Ao(e,t){Tc()||Iu(e),e.equal(e.value,t)||(e.value=t,Wy(e))}function Fc(e,t){Tc()||Iu(e),Ao(e,t(e.value))}var Du=G(A({},Mn),{equal:wo,value:void 0,kind:"signal"});function Wy(e){e.version++,vu(),Ac(e),Uy?.(e)}function U(e){return typeof e=="function"}function To(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Ro=To(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,i)=>`${i+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function wn(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var X=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let o of n)o.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(U(r))try{r()}catch(o){t=o instanceof Ro?o.errors:[o]}let{_finalizers:i}=this;if(i){this._finalizers=null;for(let o of i)try{Su(o)}catch(a){t=t??[],a instanceof Ro?t=[...t,...a.errors]:t.push(a)}}if(t)throw new Ro(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Su(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&wn(n,t)}remove(t){let{_finalizers:n}=this;n&&wn(n,t),t instanceof e&&t._removeParent(this)}};X.EMPTY=(()=>{let e=new X;return e.closed=!0,e})();var Nc=X.EMPTY;function Po(e){return e instanceof X||e&&"closed"in e&&U(e.remove)&&U(e.add)&&U(e.unsubscribe)}function Su(e){U(e)?e():e.unsubscribe()}var at={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var dr={setTimeout(e,t,...n){let{delegate:r}=dr;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=dr;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Fo(e){dr.setTimeout(()=>{let{onUnhandledError:t}=at;if(t)t(e);else throw e})}function Oc(){}var _u=Vc("C",void 0,void 0);function Eu(e){return Vc("E",void 0,e)}function Lu(e){return Vc("N",e,void 0)}function Vc(e,t,n){return{kind:e,value:t,error:n}}var Cn=null;function lr(e){if(at.useDeprecatedSynchronousErrorHandling){let t=!Cn;if(t&&(Cn={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Cn;if(Cn=null,n)throw r}}else e()}function Au(e){at.useDeprecatedSynchronousErrorHandling&&Cn&&(Cn.errorThrown=!0,Cn.error=e)}var xn=class extends X{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Po(t)&&t.add(this)):this.destination=Zy}static create(t,n,r){return new ur(t,n,r)}next(t){this.isStopped?Hc(Lu(t),this):this._next(t)}error(t){this.isStopped?Hc(Eu(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Hc(_u,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},$y=Function.prototype.bind;function jc(e,t){return $y.call(e,t)}var Bc=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){No(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){No(r)}else No(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){No(n)}}},ur=class extends xn{constructor(t,n,r){super();let i;if(U(t)||!t)i={next:t??void 0,error:n??void 0,complete:r??void 0};else{let o;this&&at.useDeprecatedNextContext?(o=Object.create(t),o.unsubscribe=()=>this.unsubscribe(),i={next:t.next&&jc(t.next,o),error:t.error&&jc(t.error,o),complete:t.complete&&jc(t.complete,o)}):i=t}this.destination=new Bc(i)}};function No(e){at.useDeprecatedSynchronousErrorHandling?Au(e):Fo(e)}function Gy(e){throw e}function Hc(e,t){let{onStoppedNotification:n}=at;n&&dr.setTimeout(()=>n(e,t))}var Zy={closed:!0,next:Oc,error:Gy,complete:Oc};var hr=typeof Symbol=="function"&&Symbol.observable||"@@observable";function In(e){return e}function Tu(e){return e.length===0?In:e.length===1?e[0]:function(n){return e.reduce((r,i)=>i(r),n)}}var Z=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,i){let o=Ky(n)?n:new ur(n,r,i);return lr(()=>{let{operator:a,source:s}=this;o.add(a?a.call(o,s):s?this._subscribe(o):this._trySubscribe(o))}),o}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Ru(r),new r((i,o)=>{let a=new ur({next:s=>{try{n(s)}catch(c){o(c),a.unsubscribe()}},error:o,complete:i});this.subscribe(a)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[hr](){return this}pipe(...n){return Tu(n)(this)}toPromise(n){return n=Ru(n),new n((r,i)=>{let o;this.subscribe(a=>o=a,a=>i(a),()=>r(o))})}}return e.create=t=>new e(t),e})();function Ru(e){var t;return(t=e??at.Promise)!==null&&t!==void 0?t:Promise}function Xy(e){return e&&U(e.next)&&U(e.error)&&U(e.complete)}function Ky(e){return e&&e instanceof xn||Xy(e)&&Po(e)}function Yy(e){return U(e?.lift)}function ke(e){return t=>{if(Yy(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function ae(e,t,n,r,i){return new qc(e,t,n,r,i)}var qc=class extends xn{constructor(t,n,r,i,o,a){super(t),this.onFinalize=o,this.shouldUnsubscribe=a,this._next=n?function(s){try{n(s)}catch(c){t.error(c)}}:super._next,this._error=i?function(s){try{i(s)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(s){t.error(s)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};var Pu=To(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var Nt=(()=>{class e extends Z{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Oo(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Pu}next(n){lr(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){lr(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){lr(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:i,observers:o}=this;return r||i?Nc:(this.currentObservers=null,o.push(n),new X(()=>{this.currentObservers=null,wn(o,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:i,isStopped:o}=this;r?n.error(i):o&&n.complete()}asObservable(){let n=new Z;return n.source=this,n}}return e.create=(t,n)=>new Oo(t,n),e})(),Oo=class extends Nt{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Nc}};var J=class extends Nt{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var zc={now(){return(zc.delegate||Date).now()},delegate:void 0};var Vo=class extends X{constructor(t,n){super()}schedule(t,n=0){return this}};var ii={setInterval(e,t,...n){let{delegate:r}=ii;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=ii;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var jo=class extends Vo{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let i=this.id,o=this.scheduler;return i!=null&&(this.id=this.recycleAsyncId(o,i,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(o,this.id,n),this}requestAsyncId(t,n,r=0){return ii.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&ii.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,i;try{this.work(t)}catch(o){r=!0,i=o||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),i}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,wn(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var pr=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};pr.now=zc.now;var Ho=class extends pr{constructor(t,n=pr.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var Qy=new Ho(jo),Fu=Qy;function Bo(e){return e&&U(e.schedule)}function Nu(e){return e[e.length-1]}function qo(e){return U(Nu(e))?e.pop():void 0}function zo(e){return Bo(Nu(e))?e.pop():void 0}function Vu(e,t,n,r){function i(o){return o instanceof n?o:new n(function(a){a(o)})}return new(n||(n=Promise))(function(o,a){function s(l){try{d(r.next(l))}catch(f){a(f)}}function c(l){try{d(r.throw(l))}catch(f){a(f)}}function d(l){l.done?o(l.value):i(l.value).then(s,c)}d((r=r.apply(e,t||[])).next())})}function Ou(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function bn(e){return this instanceof bn?(this.v=e,this):new bn(e)}function ju(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),i,o=[];return i=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),s("next"),s("throw"),s("return",a),i[Symbol.asyncIterator]=function(){return this},i;function a(y){return function(M){return Promise.resolve(M).then(y,f)}}function s(y,M){r[y]&&(i[y]=function(C){return new Promise(function(_,F){o.push([y,C,_,F])>1||c(y,C)})},M&&(i[y]=M(i[y])))}function c(y,M){try{d(r[y](M))}catch(C){g(o[0][3],C)}}function d(y){y.value instanceof bn?Promise.resolve(y.value.v).then(l,f):g(o[0][2],y)}function l(y){c("next",y)}function f(y){c("throw",y)}function g(y,M){y(M),o.shift(),o.length&&c(o[0][0],o[0][1])}}function Hu(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Ou=="function"?Ou(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(o){n[o]=e[o]&&function(a){return new Promise(function(s,c){a=e[o](a),i(s,c,a.done,a.value)})}}function i(o,a,s,c){Promise.resolve(c).then(function(d){o({value:d,done:s})},a)}}var Uo=e=>e&&typeof e.length=="number"&&typeof e!="function";function Wo(e){return U(e?.then)}function $o(e){return U(e[hr])}function Go(e){return Symbol.asyncIterator&&U(e?.[Symbol.asyncIterator])}function Zo(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Jy(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Xo=Jy();function Ko(e){return U(e?.[Xo])}function Yo(e){return ju(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:i}=yield bn(n.read());if(i)return yield bn(void 0);yield yield bn(r)}}finally{n.releaseLock()}})}function Qo(e){return U(e?.getReader)}function ve(e){if(e instanceof Z)return e;if(e!=null){if($o(e))return em(e);if(Uo(e))return tm(e);if(Wo(e))return nm(e);if(Go(e))return Bu(e);if(Ko(e))return rm(e);if(Qo(e))return im(e)}throw Zo(e)}function em(e){return new Z(t=>{let n=e[hr]();if(U(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function tm(e){return new Z(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function nm(e){return new Z(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Fo)})}function rm(e){return new Z(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Bu(e){return new Z(t=>{om(e,t).catch(n=>t.error(n))})}function im(e){return Bu(Yo(e))}function om(e,t){var n,r,i,o;return Vu(this,void 0,void 0,function*(){try{for(n=Hu(e);r=yield n.next(),!r.done;){let a=r.value;if(t.next(a),t.closed)return}}catch(a){i={error:a}}finally{try{r&&!r.done&&(o=n.return)&&(yield o.call(n))}finally{if(i)throw i.error}}t.complete()})}function He(e,t,n,r=0,i=!1){let o=t.schedule(function(){n(),i?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(o),!i)return o}function Jo(e,t=0){return ke((n,r)=>{n.subscribe(ae(r,i=>He(r,e,()=>r.next(i),t),()=>He(r,e,()=>r.complete(),t),i=>He(r,e,()=>r.error(i),t)))})}function ea(e,t=0){return ke((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function qu(e,t){return ve(e).pipe(ea(t),Jo(t))}function zu(e,t){return ve(e).pipe(ea(t),Jo(t))}function Uu(e,t){return new Z(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Wu(e,t){return new Z(n=>{let r;return He(n,t,()=>{r=e[Xo](),He(n,t,()=>{let i,o;try{({value:i,done:o}=r.next())}catch(a){n.error(a);return}o?n.complete():n.next(i)},0,!0)}),()=>U(r?.return)&&r.return()})}function ta(e,t){if(!e)throw new Error("Iterable cannot be null");return new Z(n=>{He(n,t,()=>{let r=e[Symbol.asyncIterator]();He(n,t,()=>{r.next().then(i=>{i.done?n.complete():n.next(i.value)})},0,!0)})})}function $u(e,t){return ta(Yo(e),t)}function Gu(e,t){if(e!=null){if($o(e))return qu(e,t);if(Uo(e))return Uu(e,t);if(Wo(e))return zu(e,t);if(Go(e))return ta(e,t);if(Ko(e))return Wu(e,t);if(Qo(e))return $u(e,t)}throw Zo(e)}function Kt(e,t){return t?Gu(e,t):ve(e)}function na(...e){let t=zo(e);return Kt(e,t)}function Uc(e,t){let n=U(e)?e:()=>e,r=i=>i.error(n());return new Z(t?i=>t.schedule(r,0,i):r)}function Zu(e){return e instanceof Date&&!isNaN(e)}function Ce(e,t){return ke((n,r)=>{let i=0;n.subscribe(ae(r,o=>{r.next(e.call(t,o,i++))}))})}var{isArray:am}=Array;function sm(e,t){return am(t)?e(...t):e(t)}function ra(e){return Ce(t=>sm(e,t))}var{isArray:cm}=Array,{getPrototypeOf:dm,prototype:lm,keys:um}=Object;function ia(e){if(e.length===1){let t=e[0];if(cm(t))return{args:t,keys:null};if(hm(t)){let n=um(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function hm(e){return e&&typeof e=="object"&&dm(e)===lm}function oa(e,t){return e.reduce((n,r,i)=>(n[r]=t[i],n),{})}function oi(...e){let t=zo(e),n=qo(e),{args:r,keys:i}=ia(e);if(r.length===0)return Kt([],t);let o=new Z(pm(r,t,i?a=>oa(i,a):In));return n?o.pipe(ra(n)):o}function pm(e,t,n=In){return r=>{Xu(t,()=>{let{length:i}=e,o=new Array(i),a=i,s=i;for(let c=0;c<i;c++)Xu(t,()=>{let d=Kt(e[c],t),l=!1;d.subscribe(ae(r,f=>{o[c]=f,l||(l=!0,s--),s||r.next(n(o.slice()))},()=>{--a||r.complete()}))},r)},r)}}function Xu(e,t,n){e?He(n,e,t):t()}function Ku(e,t,n,r,i,o,a,s){let c=[],d=0,l=0,f=!1,g=()=>{f&&!c.length&&!d&&t.complete()},y=C=>d<r?M(C):c.push(C),M=C=>{o&&t.next(C),d++;let _=!1;ve(n(C,l++)).subscribe(ae(t,F=>{i?.(F),o?y(F):t.next(F)},()=>{_=!0},void 0,()=>{if(_)try{for(d--;c.length&&d<r;){let F=c.shift();a?He(t,a,()=>M(F)):M(F)}g()}catch(F){t.error(F)}}))};return e.subscribe(ae(t,y,()=>{f=!0,g()})),()=>{s?.()}}function aa(e,t,n=1/0){return U(t)?aa((r,i)=>Ce((o,a)=>t(r,o,i,a))(ve(e(r,i))),n):(typeof t=="number"&&(n=t),ke((r,i)=>Ku(r,i,e,n)))}function Wc(...e){let t=qo(e),{args:n,keys:r}=ia(e),i=new Z(o=>{let{length:a}=n;if(!a){o.complete();return}let s=new Array(a),c=a,d=a;for(let l=0;l<a;l++){let f=!1;ve(n[l]).subscribe(ae(o,g=>{f||(f=!0,d--),s[l]=g},()=>c--,void 0,()=>{(!c||!f)&&(d||o.next(r?oa(r,s):s),o.complete())}))}});return t?i.pipe(ra(t)):i}function Yu(e=0,t,n=Fu){let r=-1;return t!=null&&(Bo(t)?n=t:r=t),new Z(i=>{let o=Zu(e)?+e-n.now():e;o<0&&(o=0);let a=0;return n.schedule(function(){i.closed||(i.next(a++),0<=r?this.schedule(void 0,r):i.complete())},o)})}function $c(e,t){return ke((n,r)=>{let i=0;n.subscribe(ae(r,o=>e.call(t,o,i++)&&r.next(o)))})}function sa(e){return ke((t,n)=>{let r=null,i=!1,o;r=t.subscribe(ae(n,void 0,void 0,a=>{o=ve(e(a,sa(e)(t))),r?(r.unsubscribe(),r=null,o.subscribe(n)):i=!0})),i&&(r.unsubscribe(),r=null,o.subscribe(n))})}function Gc(e,t){return U(t)?aa(e,t,1):aa(e,1)}function Zc(e){return ke((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Xc(e=1/0){let t;e&&typeof e=="object"?t=e:t={count:e};let{count:n=1/0,delay:r,resetOnSuccess:i=!1}=t;return n<=0?In:ke((o,a)=>{let s=0,c,d=()=>{let l=!1;c=o.subscribe(ae(a,f=>{i&&(s=0),a.next(f)},void 0,f=>{if(s++<n){let g=()=>{c?(c.unsubscribe(),c=null,d()):l=!0};if(r!=null){let y=typeof r=="number"?Yu(r):ve(r(f,s)),M=ae(a,()=>{M.unsubscribe(),g()},()=>{a.complete()});y.subscribe(M)}else g()}else a.error(f)})),l&&(c.unsubscribe(),c=null,d())};d()})}function Kc(e,t){return ke((n,r)=>{let i=null,o=0,a=!1,s=()=>a&&!i&&r.complete();n.subscribe(ae(r,c=>{i?.unsubscribe();let d=0,l=o++;ve(e(c,l)).subscribe(i=ae(r,f=>r.next(t?t(c,f,l,d++):f),()=>{i=null,s()}))},()=>{a=!0,s()}))})}function Qu(e){let t=T(null);try{return e()}finally{T(t)}}var dd="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",S=class extends Error{code;constructor(t,n){super(ha(t,n)),this.code=t}};function fm(e){return`NG0${Math.abs(e)}`}function ha(e,t){return`${fm(e)}${t?": "+t:""}`}var Ln=globalThis;function re(e){for(let t in e)if(e[t]===re)return t;throw Error("")}function nh(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function $e(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map($e).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function pa(e,t){return e?t?`${e} ${t}`:e:t||""}var ym=re({__forward_ref__:re});function st(e){return e.__forward_ref__=st,e.toString=function(){return $e(this())},e}function xe(e){return ld(e)?e():e}function ld(e){return typeof e=="function"&&e.hasOwnProperty(ym)&&e.__forward_ref__===st}function rh(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}function P(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function ct(e){return{providers:e.providers||[],imports:e.imports||[]}}function fa(e){return mm(e,ya)}function mm(e,t){return e.hasOwnProperty(t)&&e[t]||null}function gm(e){let t=e?.[ya]??null;return t||null}function Qc(e){return e&&e.hasOwnProperty(da)?e[da]:null}var ya=re({\u0275prov:re}),da=re({\u0275inj:re}),E=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=P({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function ud(e){return e&&!!e.\u0275providers}var hd=re({\u0275cmp:re}),pd=re({\u0275dir:re}),fd=re({\u0275pipe:re});var si=re({\u0275fac:re}),An=re({__NG_ELEMENT_ID__:re}),Ju=re({__NG_ENV_ID__:re});function ma(e){return typeof e=="string"?e:e==null?"":String(e)}function ih(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():ma(e)}function yd(e,t){throw new S(-200,e)}function ga(e,t){throw new S(-201,!1)}var Jc;function oh(){return Jc}function Fe(e){let t=Jc;return Jc=e,t}function md(e,t,n){let r=fa(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&8)return null;if(t!==void 0)return t;ga(e,"Injector")}var km={},Dn=km,ed="__NG_DI_FLAG__",td=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=Sn(n)||0;try{return this.injector.get(t,r&8?null:Dn,r)}catch(i){if(sr(i))return i;throw i}}},la="ngTempTokenPath",vm="ngTokenPath",Mm=/\n/gm,wm="\u0275",eh="__source";function Cm(e,t=0){let n=Ec();if(n===void 0)throw new S(-203,!1);if(n===null)return md(e,void 0,t);{let r=xm(t),i=n.retrieve(e,r);if(sr(i)){if(r.optional)return null;throw i}return i}}function R(e,t=0){return(oh()||Cm)(xe(e),t)}function L(e,t){return R(e,Sn(t))}function Sn(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function xm(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function nd(e){let t=[];for(let n=0;n<e.length;n++){let r=xe(e[n]);if(Array.isArray(r)){if(r.length===0)throw new S(900,!1);let i,o=0;for(let a=0;a<r.length;a++){let s=r[a],c=Im(s);typeof c=="number"?c===-1?i=s.token:o|=c:i=s}t.push(R(i,o))}else t.push(R(r))}return t}function gd(e,t){return e[ed]=t,e.prototype[ed]=t,e}function Im(e){return e[ed]}function bm(e,t,n,r){let i=e[la];throw t[eh]&&i.unshift(t[eh]),e.message=Dm(`
`+e.message,i,n,r),e[vm]=i,e[la]=null,e}function Dm(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==wm?e.slice(2):e;let i=$e(t);if(Array.isArray(t))i=t.map($e).join(" -> ");else if(typeof t=="object"){let o=[];for(let a in t)if(t.hasOwnProperty(a)){let s=t[a];o.push(a+":"+(typeof s=="string"?JSON.stringify(s):$e(s)))}i=`{${o.join(", ")}}`}return`${n}${r?"("+r+")":""}[${i}]: ${e.replace(Mm,`
  `)}`}function Yt(e,t){let n=e.hasOwnProperty(si);return n?e[si]:null}function ka(e,t){e.forEach(n=>Array.isArray(n)?ka(n,t):t(n))}function kd(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function di(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function ah(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function sh(e,t,n,r){let i=e.length;if(i==t)e.push(n,r);else if(i===1)e.push(r,e[0]),e[0]=n;else{for(i--,e.push(e[i-1],e[i]);i>t;){let o=i-2;e[i]=e[o],i--}e[t]=n,e[t+1]=r}}function va(e,t,n){let r=yr(e,t);return r>=0?e[r|1]=n:(r=~r,sh(e,r,t,n)),r}function Ma(e,t){let n=yr(e,t);if(n>=0)return e[n|1]}function yr(e,t){return Sm(e,t,1)}function Sm(e,t,n){let r=0,i=e.length>>n;for(;i!==r;){let o=r+(i-r>>1),a=e[o<<n];if(t===a)return o<<n;a>t?i=o:r=o+1}return~(i<<n)}var tn={},Ne=[],Tn=new E(""),vd=new E("",-1),Md=new E(""),ci=class{get(t,n=Dn){if(n===Dn)throw new mo(`NullInjectorError: No provider for ${$e(t)}!`);return n}};function Rn(e){return e[hd]||null}function wd(e){return e[pd]||null}function ch(e){return e[fd]||null}function wa(e){return{\u0275providers:e}}function dh(...e){return{\u0275providers:Cd(!0,e),\u0275fromNgModule:!0}}function Cd(e,...t){let n=[],r=new Set,i,o=a=>{n.push(a)};return ka(t,a=>{let s=a;ua(s,o,[],r)&&(i||=[],i.push(s))}),i!==void 0&&lh(i,o),n}function lh(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:i}=e[n];xd(i,o=>{t(o,r)})}}function ua(e,t,n,r){if(e=xe(e),!e)return!1;let i=null,o=Qc(e),a=!o&&Rn(e);if(!o&&!a){let c=e.ngModule;if(o=Qc(c),o)i=c;else return!1}else{if(a&&!a.standalone)return!1;i=e}let s=r.has(i);if(a){if(s)return!1;if(r.add(i),a.dependencies){let c=typeof a.dependencies=="function"?a.dependencies():a.dependencies;for(let d of c)ua(d,t,n,r)}}else if(o){if(o.imports!=null&&!s){r.add(i);let d;try{ka(o.imports,l=>{ua(l,t,n,r)&&(d||=[],d.push(l))})}finally{}d!==void 0&&lh(d,t)}if(!s){let d=Yt(i)||(()=>new i);t({provide:i,useFactory:d,deps:Ne},i),t({provide:Md,useValue:i,multi:!0},i),t({provide:Tn,useValue:()=>R(i),multi:!0},i)}let c=o.providers;if(c!=null&&!s){let d=e;xd(c,l=>{t(l,d)})}}else return!1;return i!==e&&e.providers!==void 0}function xd(e,t){for(let n of e)ud(n)&&(n=n.\u0275providers),Array.isArray(n)?xd(n,t):t(n)}var _m=re({provide:String,useValue:re});function uh(e){return e!==null&&typeof e=="object"&&_m in e}function Em(e){return!!(e&&e.useExisting)}function Lm(e){return!!(e&&e.useFactory)}function _n(e){return typeof e=="function"}function hh(e){return!!e.useClass}var li=new E(""),ca={},th={},Yc;function ui(){return Yc===void 0&&(Yc=new ci),Yc}var Be=class{},En=class extends Be{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,i){super(),this.parent=n,this.source=r,this.scopes=i,id(t,a=>this.processProvider(a)),this.records.set(vd,fr(void 0,this)),i.has("environment")&&this.records.set(Be,fr(void 0,this));let o=this.records.get(li);o!=null&&typeof o.value=="string"&&this.scopes.add(o.value),this.injectorDefTypes=new Set(this.get(Md,Ne,{self:!0}))}retrieve(t,n){let r=Sn(n)||0;try{return this.get(t,Dn,r)}catch(i){if(sr(i))return i;throw i}}destroy(){ai(this),this._destroyed=!0;let t=T(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),T(t)}}onDestroy(t){return ai(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){ai(this);let n=Ft(this),r=Fe(void 0),i;try{return t()}finally{Ft(n),Fe(r)}}get(t,n=Dn,r){if(ai(this),t.hasOwnProperty(Ju))return t[Ju](this);let i=Sn(r),o,a=Ft(this),s=Fe(void 0);try{if(!(i&4)){let d=this.records.get(t);if(d===void 0){let l=Fm(t)&&fa(t);l&&this.injectableDefInScope(l)?d=fr(rd(t),ca):d=null,this.records.set(t,d)}if(d!=null)return this.hydrate(t,d)}let c=i&2?ui():this.parent;return n=i&8&&n===Dn?null:n,c.get(t,n)}catch(c){if(sr(c)){if((c[la]=c[la]||[]).unshift($e(t)),a)throw c;return bm(c,t,"R3InjectorError",this.source)}else throw c}finally{Fe(s),Ft(a)}}resolveInjectorInitializers(){let t=T(null),n=Ft(this),r=Fe(void 0),i;try{let o=this.get(Tn,Ne,{self:!0});for(let a of o)a()}finally{Ft(n),Fe(r),T(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push($e(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=xe(t);let n=_n(t)?t:xe(t&&t.provide),r=Tm(t);if(!_n(t)&&t.multi===!0){let i=this.records.get(n);i||(i=fr(void 0,ca,!0),i.factory=()=>nd(i.multi),this.records.set(n,i)),n=t,i.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=T(null);try{return n.value===th?yd($e(t)):n.value===ca&&(n.value=th,n.value=n.factory()),typeof n.value=="object"&&n.value&&Pm(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{T(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=xe(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function rd(e){let t=fa(e),n=t!==null?t.factory:Yt(e);if(n!==null)return n;if(e instanceof E)throw new S(204,!1);if(e instanceof Function)return Am(e);throw new S(204,!1)}function Am(e){if(e.length>0)throw new S(204,!1);let n=gm(e);return n!==null?()=>n.factory(e):()=>new e}function Tm(e){if(uh(e))return fr(void 0,e.useValue);{let t=Id(e);return fr(t,ca)}}function Id(e,t,n){let r;if(_n(e)){let i=xe(e);return Yt(i)||rd(i)}else if(uh(e))r=()=>xe(e.useValue);else if(Lm(e))r=()=>e.useFactory(...nd(e.deps||[]));else if(Em(e))r=()=>R(xe(e.useExisting));else{let i=xe(e&&(e.useClass||e.provide));if(Rm(e))r=()=>new i(...nd(e.deps));else return Yt(i)||rd(i)}return r}function ai(e){if(e.destroyed)throw new S(205,!1)}function fr(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function Rm(e){return!!e.deps}function Pm(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Fm(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function id(e,t){for(let n of e)Array.isArray(n)?id(n,t):n&&ud(n)?id(n.\u0275providers,t):t(n)}function mr(e,t){let n;e instanceof En?(ai(e),n=e):n=new td(e);let r,i=Ft(n),o=Fe(void 0);try{return t()}finally{Ft(i),Fe(o)}}function ph(){return oh()!==void 0||Ec()!=null}var dt=0,j=1,V=2,Me=3,Ye=4,Oe=5,Pn=6,gr=7,ie=8,Fn=9,Vt=10,te=11,kr=12,bd=13,Nn=14,be=15,On=16,Vn=17,jn=18,hi=19,Dd=20,Ot=21,Ca=22,pi=23,Ge=24,xa=25,ye=26,fh=1,Sd=6,nn=7,fi=8,yi=9,Ie=10;function wt(e){return Array.isArray(e)&&typeof e[fh]=="object"}function lt(e){return Array.isArray(e)&&e[fh]===!0}function _d(e){return(e.flags&4)!==0}function rn(e){return e.componentOffset>-1}function Ia(e){return(e.flags&1)===1}function Ct(e){return!!e.template}function vr(e){return(e[V]&512)!==0}function Hn(e){return(e[V]&256)===256}var yh="svg",mh="math";function Qe(e){for(;Array.isArray(e);)e=e[dt];return e}function Ed(e,t){return Qe(t[e])}function ut(e,t){return Qe(t[e.index])}function mi(e,t){return e.data[t]}function Ld(e,t){return e[t]}function Ad(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function Je(e,t){let n=t[e];return wt(n)?n:n[dt]}function ba(e){return(e[V]&128)===128}function gh(e){return lt(e[Me])}function jt(e,t){return t==null?null:e[t]}function Td(e){e[Vn]=0}function Rd(e){e[V]&1024||(e[V]|=1024,ba(e)&&ki(e))}function kh(e,t){for(;e>0;)t=t[Nn],e--;return t}function gi(e){return!!(e[V]&9216||e[Ge]?.dirty)}function Da(e){e[Vt].changeDetectionScheduler?.notify(8),e[V]&64&&(e[V]|=1024),gi(e)&&ki(e)}function ki(e){e[Vt].changeDetectionScheduler?.notify(0);let t=Qt(e);for(;t!==null&&!(t[V]&8192||(t[V]|=8192,!ba(t)));)t=Qt(t)}function Pd(e,t){if(Hn(e))throw new S(911,!1);e[Ot]===null&&(e[Ot]=[]),e[Ot].push(t)}function vh(e,t){if(e[Ot]===null)return;let n=e[Ot].indexOf(t);n!==-1&&e[Ot].splice(n,1)}function Qt(e){let t=e[Me];return lt(t)?t[Me]:t}function Mh(e){return e[gr]??=[]}function wh(e){return e.cleanup??=[]}var $={lFrame:Ph(null),bindingsEnabled:!0,skipHydrationRootTNode:null},vi=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(vi||{}),Nm=0,od=!1;function Ch(){return $.lFrame.elementDepthCount}function xh(){$.lFrame.elementDepthCount++}function Ih(){$.lFrame.elementDepthCount--}function Fd(){return $.bindingsEnabled}function Nd(){return $.skipHydrationRootTNode!==null}function bh(e){return $.skipHydrationRootTNode===e}function Dh(){$.skipHydrationRootTNode=null}function q(){return $.lFrame.lView}function de(){return $.lFrame.tView}function b(e){return $.lFrame.contextLView=e,e[ie]}function D(e){return $.lFrame.contextLView=null,e}function qe(){let e=Od();for(;e!==null&&e.type===64;)e=e.parent;return e}function Od(){return $.lFrame.currentTNode}function Sh(){let e=$.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Mr(e,t){let n=$.lFrame;n.currentTNode=e,n.isParent=t}function Vd(){return $.lFrame.isParent}function jd(){$.lFrame.isParent=!1}function Hd(e){rh("Must never be called in production mode"),Nm=e}function Bd(){return od}function qd(e){let t=od;return od=e,t}function zd(){let e=$.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function _h(e){return $.lFrame.bindingIndex=e}function Bn(){return $.lFrame.bindingIndex++}function Ud(e){let t=$.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Eh(){return $.lFrame.inI18n}function Lh(e,t){let n=$.lFrame;n.bindingIndex=n.bindingRootIndex=e,Sa(t)}function Ah(){return $.lFrame.currentDirectiveIndex}function Sa(e){$.lFrame.currentDirectiveIndex=e}function Th(e){let t=$.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function Wd(e){$.lFrame.currentQueryIndex=e}function Om(e){let t=e[j];return t.type===2?t.declTNode:t.type===1?e[Oe]:null}function $d(e,t,n){if(n&4){let i=t,o=e;for(;i=i.parent,i===null&&!(n&1);)if(i=Om(o),i===null||(o=o[Nn],i.type&10))break;if(i===null)return!1;t=i,e=o}let r=$.lFrame=Rh();return r.currentTNode=t,r.lView=e,!0}function _a(e){let t=Rh(),n=e[j];$.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Rh(){let e=$.lFrame,t=e===null?null:e.child;return t===null?Ph(e):t}function Ph(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function Fh(){let e=$.lFrame;return $.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Gd=Fh;function Ea(){let e=Fh();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Nh(e){return($.lFrame.contextLView=kh(e,$.lFrame.contextLView))[ie]}function Ht(){return $.lFrame.selectedIndex}function on(e){$.lFrame.selectedIndex=e}function La(){let e=$.lFrame;return mi(e.tView,e.selectedIndex)}function Oh(){return $.lFrame.currentNamespace}var Vh=!0;function Aa(){return Vh}function Ta(e){Vh=e}function ad(e,t=null,n=null,r){let i=jh(e,t,n,r);return i.resolveInjectorInitializers(),i}function jh(e,t=null,n=null,r,i=new Set){let o=[n||Ne,dh(e)];return r=r||(typeof e=="object"?void 0:$e(e)),new En(o,t||ui(),r||null,i)}var Jt=class e{static THROW_IF_NOT_FOUND=Dn;static NULL=new ci;static create(t,n){if(Array.isArray(t))return ad({name:""},n,t,"");{let r=t.name??"";return ad({name:r},t.parent,t.providers,r)}}static \u0275prov=P({token:e,providedIn:"any",factory:()=>R(vd)});static __NG_ELEMENT_ID__=-1},Ze=new E(""),Mi=(()=>{class e{static __NG_ELEMENT_ID__=Vm;static __NG_ENV_ID__=n=>n}return e})(),sd=class extends Mi{_lView;constructor(t){super(),this._lView=t}get destroyed(){return Hn(this._lView)}onDestroy(t){let n=this._lView;return Pd(n,t),()=>vh(n,t)}};function Vm(){return new sd(q())}var Ke=class{_console=console;handleError(t){this._console.error("ERROR",t)}},ht=new E("",{providedIn:"root",factory:()=>{let e=L(Be),t;return n=>{e.destroyed&&!t?setTimeout(()=>{throw n}):(t??=e.get(Ke),t.handleError(n))}}}),Hh={provide:Tn,useValue:()=>void L(Ke),multi:!0};function Zd(e){return typeof e=="function"&&e[je]!==void 0}function qn(e,t){let[n,r,i]=Pc(e,t?.equal),o=n,a=o[je];return o.set=r,o.update=i,o.asReadonly=Bh.bind(o),o}function Bh(){let e=this[je];if(e.readonlyFn===void 0){let t=()=>this();t[je]=e,e.readonlyFn=t}return e.readonlyFn}function Xd(e){return Zd(e)&&typeof e.set=="function"}var en=class{},Ra=new E("",{providedIn:"root",factory:()=>!1});var Kd=new E(""),Yd=new E("");var zn=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new J(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new Z(n=>{n.next(!1),n.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=P({token:e,providedIn:"root",factory:()=>new e})}return e})(),Pa=(()=>{class e{internalPendingTasks=L(zn);scheduler=L(en);errorHandler=L(ht);add(){let n=this.internalPendingTasks.add();return()=>{this.internalPendingTasks.has(n)&&(this.scheduler.notify(11),this.internalPendingTasks.remove(n))}}run(n){let r=this.add();n().catch(this.errorHandler).finally(r)}static \u0275prov=P({token:e,providedIn:"root",factory:()=>new e})}return e})();function wi(...e){}var Qd=(()=>{class e{static \u0275prov=P({token:e,providedIn:"root",factory:()=>new cd})}return e})(),cd=class{dirtyEffectCount=0;queues=new Map;add(t){this.enqueue(t),this.schedule(t)}schedule(t){t.dirty&&this.dirtyEffectCount++}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),t.dirty&&this.dirtyEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||r.add(t)}flush(){for(;this.dirtyEffectCount>0;){let t=!1;for(let[n,r]of this.queues)n===null?t||=this.flushQueue(r):t||=n.run(()=>this.flushQueue(r));t||(this.dirtyEffectCount=0)}}flushQueue(t){let n=!1;for(let r of t)r.dirty&&(this.dirtyEffectCount--,n=!0,r.run());return n}};function Dr(e){return{toString:e}.toString()}var Fa="__parameters__";function Xm(e){return function(...n){if(e){let r=e(...n);for(let i in r)this[i]=r[i]}}}function m2(e,t,n){return Dr(()=>{let r=Xm(t);function i(...o){if(this instanceof i)return r.apply(this,o),this;let a=new i(...o);return s.annotation=a,s;function s(c,d,l){let f=c.hasOwnProperty(Fa)?c[Fa]:Object.defineProperty(c,Fa,{value:[]})[Fa];for(;f.length<=l;)f.push(null);return(f[l]=f[l]||[]).push(a),c}}return i.prototype.ngMetadataName=e,i.annotationCls=i,i})}var es=gd(m2("Optional"),8);var g2=gd(m2("SkipSelf"),4);function Km(e){return typeof e=="function"}var Ha=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function k2(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var Xn=(()=>{let e=()=>v2;return e.ngInherit=!0,e})();function v2(e){return e.type.prototype.ngOnChanges&&(e.setInput=Qm),Ym}function Ym(){let e=w2(this),t=e?.current;if(t){let n=e.previous;if(n===tn)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Qm(e,t,n,r,i){let o=this.declaredInputs[r],a=w2(e)||Jm(e,{previous:tn,current:null}),s=a.current||(a.current={}),c=a.previous,d=c[o];s[o]=new Ha(d&&d.currentValue,n,c===tn),k2(e,t,i,n)}var M2="__ngSimpleChanges__";function w2(e){return e[M2]||null}function Jm(e,t){return e[M2]=t}var qh=[];var Y=function(e,t=null,n){for(let r=0;r<qh.length;r++){let i=qh[r];i(e,t,n)}};function eg(e,t,n){let{ngOnChanges:r,ngOnInit:i,ngDoCheck:o}=t.type.prototype;if(r){let a=v2(t);(n.preOrderHooks??=[]).push(e,a),(n.preOrderCheckHooks??=[]).push(e,a)}i&&(n.preOrderHooks??=[]).push(0-e,i),o&&((n.preOrderHooks??=[]).push(e,o),(n.preOrderCheckHooks??=[]).push(e,o))}function C2(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let o=e.data[n].type.prototype,{ngAfterContentInit:a,ngAfterContentChecked:s,ngAfterViewInit:c,ngAfterViewChecked:d,ngOnDestroy:l}=o;a&&(e.contentHooks??=[]).push(-n,a),s&&((e.contentHooks??=[]).push(n,s),(e.contentCheckHooks??=[]).push(n,s)),c&&(e.viewHooks??=[]).push(-n,c),d&&((e.viewHooks??=[]).push(n,d),(e.viewCheckHooks??=[]).push(n,d)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function Oa(e,t,n){x2(e,t,3,n)}function Va(e,t,n,r){(e[V]&3)===n&&x2(e,t,n,r)}function Jd(e,t){let n=e[V];(n&3)===t&&(n&=16383,n+=1,e[V]=n)}function x2(e,t,n,r){let i=r!==void 0?e[Vn]&65535:0,o=r??-1,a=t.length-1,s=0;for(let c=i;c<a;c++)if(typeof t[c+1]=="number"){if(s=t[c],r!=null&&s>=r)break}else t[c]<0&&(e[Vn]+=65536),(s<o||o==-1)&&(tg(e,n,t,c),e[Vn]=(e[Vn]&**********)+c+2),c++}function zh(e,t){Y(4,e,t);let n=T(null);try{t.call(e)}finally{T(n),Y(5,e,t)}}function tg(e,t,n,r){let i=n[r]<0,o=n[r+1],a=i?-n[r]:n[r],s=e[a];i?e[V]>>14<e[Vn]>>16&&(e[V]&3)===t&&(e[V]+=16384,zh(s,o)):zh(s,o)}var Cr=-1,$n=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function ng(e){return(e.flags&8)!==0}function rg(e){return(e.flags&16)!==0}function ig(e,t,n){let r=0;for(;r<n.length;){let i=n[r];if(typeof i=="number"){if(i!==0)break;r++;let o=n[r++],a=n[r++],s=n[r++];e.setAttribute(t,a,s,o)}else{let o=i,a=n[++r];ag(o)?e.setProperty(t,o,a):e.setAttribute(t,o,a),r++}}return r}function og(e){return e===3||e===4||e===6}function ag(e){return e.charCodeAt(0)===64}function Ii(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let i=t[r];typeof i=="number"?n=i:n===0||(n===-1||n===2?Uh(e,n,i,null,t[++r]):Uh(e,n,i,null,null))}}return e}function Uh(e,t,n,r,i){let o=0,a=e.length;if(t===-1)a=-1;else for(;o<e.length;){let s=e[o++];if(typeof s=="number"){if(s===t){a=-1;break}else if(s>t){a=o-1;break}}}for(;o<e.length;){let s=e[o];if(typeof s=="number")break;if(s===n){i!==null&&(e[o+1]=i);return}o++,i!==null&&o++}a!==-1&&(e.splice(a,0,t),o=a+1),e.splice(o++,0,n),i!==null&&e.splice(o++,0,i)}function I2(e){return e!==Cr}function Ba(e){return e&32767}function sg(e){return e>>16}function qa(e,t){let n=sg(e),r=t;for(;n>0;)r=r[Nn],n--;return r}var ul=!0;function za(e){let t=ul;return ul=e,t}var cg=256,b2=cg-1,D2=5,dg=0,xt={};function lg(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(An)&&(r=n[An]),r==null&&(r=n[An]=dg++);let i=r&b2,o=1<<i;t.data[e+(i>>D2)]|=o}function Ua(e,t){let n=S2(e,t);if(n!==-1)return n;let r=t[j];r.firstCreatePass&&(e.injectorIndex=t.length,el(r.data,e),el(t,null),el(r.blueprint,null));let i=Vl(e,t),o=e.injectorIndex;if(I2(i)){let a=Ba(i),s=qa(i,t),c=s[j].data;for(let d=0;d<8;d++)t[o+d]=s[a+d]|c[a+d]}return t[o+8]=i,o}function el(e,t){e.push(0,0,0,0,0,0,0,0,t)}function S2(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Vl(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,i=t;for(;i!==null;){if(r=T2(i),r===null)return Cr;if(n++,i=i[Nn],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Cr}function hl(e,t,n){lg(e,t,n)}function _2(e,t,n){if(n&8||e!==void 0)return e;ga(t,"NodeInjector")}function E2(e,t,n,r){if(n&8&&r===void 0&&(r=null),(n&3)===0){let i=e[Fn],o=Fe(void 0);try{return i?i.get(t,r,n&8):md(t,r,n&8)}finally{Fe(o)}}return _2(r,t,n)}function L2(e,t,n,r=0,i){if(e!==null){if(t[V]&2048&&!(r&2)){let a=yg(e,t,n,r,xt);if(a!==xt)return a}let o=A2(e,t,n,r,xt);if(o!==xt)return o}return E2(t,n,r,i)}function A2(e,t,n,r,i){let o=pg(n);if(typeof o=="function"){if(!$d(t,e,r))return r&1?_2(i,n,r):E2(t,n,r,i);try{let a;if(a=o(r),a==null&&!(r&8))ga(n);else return a}finally{Gd()}}else if(typeof o=="number"){let a=null,s=S2(e,t),c=Cr,d=r&1?t[be][Oe]:null;for((s===-1||r&4)&&(c=s===-1?Vl(e,t):t[s+8],c===Cr||!$h(r,!1)?s=-1:(a=t[j],s=Ba(c),t=qa(c,t)));s!==-1;){let l=t[j];if(Wh(o,s,l.data)){let f=ug(s,t,n,a,r,d);if(f!==xt)return f}c=t[s+8],c!==Cr&&$h(r,t[j].data[s+8]===d)&&Wh(o,s,t)?(a=l,s=Ba(c),t=qa(c,t)):s=-1}}return i}function ug(e,t,n,r,i,o){let a=t[j],s=a.data[e+8],c=r==null?rn(s)&&ul:r!=a&&(s.type&3)!==0,d=i&1&&o===s,l=hg(s,a,n,c,d);return l!==null?Wa(t,a,l,s):xt}function hg(e,t,n,r,i){let o=e.providerIndexes,a=t.data,s=o&1048575,c=e.directiveStart,d=e.directiveEnd,l=o>>20,f=r?s:s+l,g=i?s+l:d;for(let y=f;y<g;y++){let M=a[y];if(y<c&&n===M||y>=c&&M.type===n)return y}if(i){let y=a[c];if(y&&Ct(y)&&y.type===n)return c}return null}function Wa(e,t,n,r){let i=e[n],o=t.data;if(i instanceof $n){let a=i;a.resolving&&yd(ih(o[n]));let s=za(a.canSeeViewProviders);a.resolving=!0;let c=o[n].type||o[n],d,l=a.injectImpl?Fe(a.injectImpl):null,f=$d(e,r,0);try{i=e[n]=a.factory(void 0,o,e,r),t.firstCreatePass&&n>=r.directiveStart&&eg(n,o[n],t)}finally{l!==null&&Fe(l),za(s),a.resolving=!1,Gd()}}return i}function pg(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(An)?e[An]:void 0;return typeof t=="number"?t>=0?t&b2:fg:t}function Wh(e,t,n){let r=1<<e;return!!(n[t+(e>>D2)]&r)}function $h(e,t){return!(e&2)&&!(e&1&&t)}var Wn=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return L2(this._tNode,this._lView,t,Sn(r),n)}};function fg(){return new Wn(qe(),q())}function Sr(e){return Dr(()=>{let t=e.prototype.constructor,n=t[si]||pl(t),r=Object.prototype,i=Object.getPrototypeOf(e.prototype).constructor;for(;i&&i!==r;){let o=i[si]||pl(i);if(o&&o!==n)return o;i=Object.getPrototypeOf(i)}return o=>new o})}function pl(e){return ld(e)?()=>{let t=pl(xe(e));return t&&t()}:Yt(e)}function yg(e,t,n,r,i){let o=e,a=t;for(;o!==null&&a!==null&&a[V]&2048&&!vr(a);){let s=A2(o,a,n,r|2,xt);if(s!==xt)return s;let c=o.parent;if(!c){let d=a[Dd];if(d){let l=d.get(n,xt,r);if(l!==xt)return l}c=T2(a),a=a[Nn]}o=c}return i}function T2(e){let t=e[j],n=t.type;return n===2?t.declTNode:n===1?e[Oe]:null}function mg(){return ts(qe(),q())}function ts(e,t){return new bt(ut(e,t))}var bt=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=mg}return e})();function R2(e){return(e.flags&128)===128}var jl=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(jl||{}),P2=new Map,gg=0;function kg(){return gg++}function vg(e){P2.set(e[hi],e)}function fl(e){P2.delete(e[hi])}var Gh="__ngContext__";function Ai(e,t){wt(t)?(e[Gh]=t[hi],vg(t)):e[Gh]=t}function F2(e){return O2(e[kr])}function N2(e){return O2(e[Ye])}function O2(e){for(;e!==null&&!lt(e);)e=e[Ye];return e}var yl;function Hl(e){yl=e}function V2(){if(yl!==void 0)return yl;if(typeof document<"u")return document;throw new S(210,!1)}var ns=new E("",{providedIn:"root",factory:()=>Mg}),Mg="ng",rs=new E(""),_r=new E("",{providedIn:"platform",factory:()=>"unknown"});var is=new E("",{providedIn:"root",factory:()=>V2().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var wg="h",Cg="b";var j2="r";var H2="di";var B2=!1,q2=new E("",{providedIn:"root",factory:()=>B2});var xg=(e,t,n,r)=>{};function Ig(e,t,n,r){xg(e,t,n,r)}var bg=()=>null;function z2(e,t,n=!1){return bg(e,t,n)}function U2(e,t){let n=e.contentQueries;if(n!==null){let r=T(null);try{for(let i=0;i<n.length;i+=2){let o=n[i],a=n[i+1];if(a!==-1){let s=e.data[a];Wd(o),s.contentQueries(2,t[a],a)}}}finally{T(r)}}}function ml(e,t,n){Wd(0);let r=T(null);try{t(e,n)}finally{T(r)}}function W2(e,t,n){if(_d(t)){let r=T(null);try{let i=t.directiveStart,o=t.directiveEnd;for(let a=i;a<o;a++){let s=e.data[a];if(s.contentQueries){let c=n[a];s.contentQueries(1,c,a)}}}finally{T(r)}}}var Bt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Bt||{});var gl=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${dd})`}};function Bl(e){return e instanceof gl?e.changingThisBreaksApplicationSecurity:e}function Dg(e,t,n){let r=e.length;for(;;){let i=e.indexOf(t,n);if(i===-1)return i;if(i===0||e.charCodeAt(i-1)<=32){let o=t.length;if(i+o===r||e.charCodeAt(i+o)<=32)return i}n=i+1}}var $2="ng-template";function Sg(e,t,n,r){let i=0;if(r){for(;i<t.length&&typeof t[i]=="string";i+=2)if(t[i]==="class"&&Dg(t[i+1].toLowerCase(),n,0)!==-1)return!0}else if(ql(e))return!1;if(i=t.indexOf(1,i),i>-1){let o;for(;++i<t.length&&typeof(o=t[i])=="string";)if(o.toLowerCase()===n)return!0}return!1}function ql(e){return e.type===4&&e.value!==$2}function _g(e,t,n){let r=e.type===4&&!n?$2:e.value;return t===r}function Eg(e,t,n){let r=4,i=e.attrs,o=i!==null?Tg(i):0,a=!1;for(let s=0;s<t.length;s++){let c=t[s];if(typeof c=="number"){if(!a&&!pt(r)&&!pt(c))return!1;if(a&&pt(c))continue;a=!1,r=c|r&1;continue}if(!a)if(r&4){if(r=2|r&1,c!==""&&!_g(e,c,n)||c===""&&t.length===1){if(pt(r))return!1;a=!0}}else if(r&8){if(i===null||!Sg(e,i,c,n)){if(pt(r))return!1;a=!0}}else{let d=t[++s],l=Lg(c,i,ql(e),n);if(l===-1){if(pt(r))return!1;a=!0;continue}if(d!==""){let f;if(l>o?f="":f=i[l+1].toLowerCase(),r&2&&d!==f){if(pt(r))return!1;a=!0}}}}return pt(r)||a}function pt(e){return(e&1)===0}function Lg(e,t,n,r){if(t===null)return-1;let i=0;if(r||!n){let o=!1;for(;i<t.length;){let a=t[i];if(a===e)return i;if(a===3||a===6)o=!0;else if(a===1||a===2){let s=t[++i];for(;typeof s=="string";)s=t[++i];continue}else{if(a===4)break;if(a===0){i+=4;continue}}i+=o?1:2}return-1}else return Rg(t,e)}function G2(e,t,n=!1){for(let r=0;r<t.length;r++)if(Eg(e,t[r],n))return!0;return!1}function Ag(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function Tg(e){for(let t=0;t<e.length;t++){let n=e[t];if(og(n))return t}return e.length}function Rg(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Pg(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let i=0;i<e.length;i++)if(e[i]!==r[i])continue e;return!0}}return!1}function Zh(e,t){return e?":not("+t.trim()+")":t}function Fg(e){let t=e[0],n=1,r=2,i="",o=!1;for(;n<e.length;){let a=e[n];if(typeof a=="string")if(r&2){let s=e[++n];i+="["+a+(s.length>0?'="'+s+'"':"")+"]"}else r&8?i+="."+a:r&4&&(i+=" "+a);else i!==""&&!pt(a)&&(t+=Zh(o,i),i=""),r=a,o=o||!pt(r);n++}return i!==""&&(t+=Zh(o,i)),t}function Ng(e){return e.map(Fg).join(",")}function Og(e){let t=[],n=[],r=1,i=2;for(;r<e.length;){let o=e[r];if(typeof o=="string")i===2?o!==""&&t.push(o,e[++r]):i===8&&n.push(o);else{if(!pt(i))break;i=o}r++}return n.length&&t.push(1,...n),t}var et={};function Vg(e,t){return e.createText(t)}function jg(e,t,n){e.setValue(t,n)}function Z2(e,t,n){return e.createElement(t,n)}function $a(e,t,n,r,i){e.insertBefore(t,n,r,i)}function X2(e,t,n){e.appendChild(t,n)}function Xh(e,t,n,r,i){r!==null?$a(e,t,n,r,i):X2(e,t,n)}function K2(e,t,n){e.removeChild(null,t,n)}function Hg(e,t,n){e.setAttribute(t,"style",n)}function Bg(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Y2(e,t,n){let{mergedAttrs:r,classes:i,styles:o}=n;r!==null&&ig(e,t,r),i!==null&&Bg(e,t,i),o!==null&&Hg(e,t,o)}function zl(e,t,n,r,i,o,a,s,c,d,l){let f=ye+r,g=f+i,y=qg(f,g),M=typeof d=="function"?d():d;return y[j]={type:e,blueprint:y,template:n,queries:null,viewQuery:s,declTNode:t,data:y.slice().fill(null,f),bindingStartIndex:f,expandoStartIndex:g,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof o=="function"?o():o,pipeRegistry:typeof a=="function"?a():a,firstChild:null,schemas:c,consts:M,incompleteFirstPass:!1,ssrId:l}}function qg(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:et);return n}function zg(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=zl(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Ul(e,t,n,r,i,o,a,s,c,d,l){let f=t.blueprint.slice();return f[dt]=i,f[V]=r|4|128|8|64|1024,(d!==null||e&&e[V]&2048)&&(f[V]|=2048),Td(f),f[Me]=f[Nn]=e,f[ie]=n,f[Vt]=a||e&&e[Vt],f[te]=s||e&&e[te],f[Fn]=c||e&&e[Fn]||null,f[Oe]=o,f[hi]=kg(),f[Pn]=l,f[Dd]=d,f[be]=t.type==2?e[be]:f,f}function Ug(e,t,n){let r=ut(t,e),i=zg(n),o=e[Vt].rendererFactory,a=Wl(e,Ul(e,i,null,Q2(n),r,t,null,o.createRenderer(r,n),null,null,null));return e[t.index]=a}function Q2(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function J2(e,t,n,r){if(n===0)return-1;let i=t.length;for(let o=0;o<n;o++)t.push(r),e.blueprint.push(r),e.data.push(null);return i}function Wl(e,t){return e[kr]?e[bd][Ye]=t:e[kr]=t,e[bd]=t,t}function p(e=1){e0(de(),q(),Ht()+e,!1)}function e0(e,t,n,r){if(!r)if((t[V]&3)===3){let o=e.preOrderCheckHooks;o!==null&&Oa(t,o,n)}else{let o=e.preOrderHooks;o!==null&&Va(t,o,0,n)}on(n)}var os=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(os||{});function kl(e,t,n,r){let i=T(null);try{let[o,a,s]=e.inputs[n],c=null;(a&os.SignalBased)!==0&&(c=t[o][je]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):s!==null&&(r=s.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,o):k2(t,c,o,r)}finally{T(i)}}function t0(e,t,n,r,i){let o=Ht(),a=r&2;try{on(-1),a&&t.length>ye&&e0(e,t,ye,!1),Y(a?2:0,i,n),n(r,i)}finally{on(o),Y(a?3:1,i,n)}}function $l(e,t,n){Yg(e,t,n),(n.flags&64)===64&&Qg(e,t,n)}function n0(e,t,n=ut){let r=t.localNames;if(r!==null){let i=t.index+1;for(let o=0;o<r.length;o+=2){let a=r[o+1],s=a===-1?n(t,e):e[a];e[i++]=s}}}function Wg(e,t,n,r){let o=r.get(q2,B2)||n===Bt.ShadowDom,a=e.selectRootElement(t,o);return $g(a),a}function $g(e){Gg(e)}var Gg=()=>null;function Zg(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function r0(e,t,n,r,i,o){let a=t[j];if(Gl(e,a,t,n,r)){rn(e)&&Kg(t,e.index);return}Xg(e,t,n,r,i,o)}function Xg(e,t,n,r,i,o){if(e.type&3){let a=ut(e,t);n=Zg(n),r=o!=null?o(r,e.value||"",n):r,i.setProperty(a,n,r)}else e.type&12}function Kg(e,t){let n=Je(t,e);n[V]&16||(n[V]|=64)}function Yg(e,t,n){let r=n.directiveStart,i=n.directiveEnd;rn(n)&&Ug(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Ua(n,t);let o=n.initialInputs;for(let a=r;a<i;a++){let s=e.data[a],c=Wa(t,e,a,n);if(Ai(c,t),o!==null&&nk(t,a-r,c,s,n,o),Ct(s)){let d=Je(n.index,t);d[ie]=Wa(t,e,a,n)}}}function Qg(e,t,n){let r=n.directiveStart,i=n.directiveEnd,o=n.index,a=Ah();try{on(o);for(let s=r;s<i;s++){let c=e.data[s],d=t[s];Sa(s),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&Jg(c,d)}}finally{on(-1),Sa(a)}}function Jg(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function i0(e,t){let n=e.directiveRegistry,r=null;if(n)for(let i=0;i<n.length;i++){let o=n[i];G2(t,o.selectors,!1)&&(r??=[],Ct(o)?r.unshift(o):r.push(o))}return r}function ek(e,t,n,r,i,o){let a=ut(e,t);tk(t[te],a,o,e.value,n,r,i)}function tk(e,t,n,r,i,o,a){if(o==null)e.removeAttribute(t,i,n);else{let s=a==null?ma(o):a(o,r||"",i);e.setAttribute(t,i,s,n)}}function nk(e,t,n,r,i,o){let a=o[t];if(a!==null)for(let s=0;s<a.length;s+=2){let c=a[s],d=a[s+1];kl(r,n,c,d)}}function rk(e,t){let n=e[Fn];if(!n)return;n.get(ht,null)?.(t)}function Gl(e,t,n,r,i){let o=e.inputs?.[r],a=e.hostDirectiveInputs?.[r],s=!1;if(a)for(let c=0;c<a.length;c+=2){let d=a[c],l=a[c+1],f=t.data[d];kl(f,n[d],l,i),s=!0}if(o)for(let c of o){let d=n[c],l=t.data[c];kl(l,d,r,i),s=!0}return s}function ik(e,t){let n=Je(t,e),r=n[j];ok(r,n);let i=n[dt];i!==null&&n[Pn]===null&&(n[Pn]=z2(i,n[Fn])),Y(18),Zl(r,n,n[ie]),Y(19,n[ie])}function ok(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Zl(e,t,n){_a(t);try{let r=e.viewQuery;r!==null&&ml(1,r,n);let i=e.template;i!==null&&t0(e,t,i,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[jn]?.finishViewCreation(e),e.staticContentQueries&&U2(e,t),e.staticViewQueries&&ml(2,e.viewQuery,n);let o=e.components;o!==null&&ak(t,o)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[V]&=-5,Ea()}}function ak(e,t){for(let n=0;n<t.length;n++)ik(e,t[n])}function Ti(e,t,n,r){let i=T(null);try{let o=t.tView,s=e[V]&4096?4096:16,c=Ul(e,o,n,s,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),d=e[t.index];c[On]=d;let l=e[jn];return l!==null&&(c[jn]=l.createEmbeddedView(o)),Zl(o,c,n),c}finally{T(i)}}function xr(e,t){return!t||t.firstChild===null||R2(e)}var Kh=!1,sk=new E(""),ck;function Xl(e,t){return ck(e,t)}var It=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(It||{});function as(e){return(e.flags&32)===32}function wr(e,t,n,r,i){if(r!=null){let o,a=!1;lt(r)?o=r:wt(r)&&(a=!0,r=r[dt]);let s=Qe(r);e===0&&n!==null?i==null?X2(t,n,s):$a(t,n,s,i||null,!0):e===1&&n!==null?$a(t,n,s,i||null,!0):e===2?K2(t,s,a):e===3&&t.destroyNode(s),o!=null&&kk(t,e,o,n,i)}}function dk(e,t){o0(e,t),t[dt]=null,t[Oe]=null}function lk(e,t,n,r,i,o){r[dt]=i,r[Oe]=t,cs(e,r,n,1,i,o)}function o0(e,t){t[Vt].changeDetectionScheduler?.notify(9),cs(e,t,t[te],2,null,null)}function uk(e){let t=e[kr];if(!t)return tl(e[j],e);for(;t;){let n=null;if(wt(t))n=t[kr];else{let r=t[Ie];r&&(n=r)}if(!n){for(;t&&!t[Ye]&&t!==e;)wt(t)&&tl(t[j],t),t=t[Me];t===null&&(t=e),wt(t)&&tl(t[j],t),n=t&&t[Ye]}t=n}}function Kl(e,t){let n=e[yi],r=n.indexOf(t);n.splice(r,1)}function ss(e,t){if(Hn(t))return;let n=t[te];n.destroyNode&&cs(e,t,n,3,null,null),uk(t)}function tl(e,t){if(Hn(t))return;let n=T(null);try{t[V]&=-129,t[V]|=256,t[Ge]&&Do(t[Ge]),pk(e,t),hk(e,t),t[j].type===1&&t[te].destroy();let r=t[On];if(r!==null&&lt(t[Me])){r!==t[Me]&&Kl(r,t);let i=t[jn];i!==null&&i.detachView(e)}fl(t)}finally{T(n)}}function hk(e,t){let n=e.cleanup,r=t[gr];if(n!==null)for(let a=0;a<n.length-1;a+=2)if(typeof n[a]=="string"){let s=n[a+3];s>=0?r[s]():r[-s].unsubscribe(),a+=2}else{let s=r[n[a+1]];n[a].call(s)}r!==null&&(t[gr]=null);let i=t[Ot];if(i!==null){t[Ot]=null;for(let a=0;a<i.length;a++){let s=i[a];s()}}let o=t[pi];if(o!==null){t[pi]=null;for(let a of o)a.destroy()}}function pk(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let i=t[n[r]];if(!(i instanceof $n)){let o=n[r+1];if(Array.isArray(o))for(let a=0;a<o.length;a+=2){let s=i[o[a]],c=o[a+1];Y(4,s,c);try{c.call(s)}finally{Y(5,s,c)}}else{Y(4,i,o);try{o.call(i)}finally{Y(5,i,o)}}}}}function a0(e,t,n){return fk(e,t.parent,n)}function fk(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[dt];if(rn(r)){let{encapsulation:i}=e.data[r.directiveStart+r.componentOffset];if(i===Bt.None||i===Bt.Emulated)return null}return ut(r,n)}function s0(e,t,n){return mk(e,t,n)}function yk(e,t,n){return e.type&40?ut(e,n):null}var mk=yk,Yh;function Yl(e,t,n,r){let i=a0(e,r,t),o=t[te],a=r.parent||t[Oe],s=s0(a,r,t);if(i!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)Xh(o,i,n[c],s,!1);else Xh(o,i,n,s,!1);Yh!==void 0&&Yh(o,r,t,n,i)}function Ci(e,t){if(t!==null){let n=t.type;if(n&3)return ut(t,e);if(n&4)return vl(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Ci(e,r);{let i=e[t.index];return lt(i)?vl(-1,i):Qe(i)}}else{if(n&128)return Ci(e,t.next);if(n&32)return Xl(t,e)()||Qe(e[t.index]);{let r=c0(e,t);if(r!==null){if(Array.isArray(r))return r[0];let i=Qt(e[be]);return Ci(i,r)}else return Ci(e,t.next)}}}return null}function c0(e,t){if(t!==null){let r=e[be][Oe],i=t.projection;return r.projection[i]}return null}function vl(e,t){let n=Ie+e+1;if(n<t.length){let r=t[n],i=r[j].firstChild;if(i!==null)return Ci(r,i)}return t[nn]}function Ql(e,t,n,r,i,o,a){for(;n!=null;){if(n.type===128){n=n.next;continue}let s=r[n.index],c=n.type;if(a&&t===0&&(s&&Ai(Qe(s),r),n.flags|=2),!as(n))if(c&8)Ql(e,t,n.child,r,i,o,!1),wr(t,e,i,s,o);else if(c&32){let d=Xl(n,r),l;for(;l=d();)wr(t,e,i,l,o);wr(t,e,i,s,o)}else c&16?d0(e,t,r,n,i,o):wr(t,e,i,s,o);n=a?n.projectionNext:n.next}}function cs(e,t,n,r,i,o){Ql(n,r,e.firstChild,t,i,o,!1)}function gk(e,t,n){let r=t[te],i=a0(e,n,t),o=n.parent||t[Oe],a=s0(o,n,t);d0(r,0,t,n,i,a)}function d0(e,t,n,r,i,o){let a=n[be],c=a[Oe].projection[r.projection];if(Array.isArray(c))for(let d=0;d<c.length;d++){let l=c[d];wr(t,e,i,l,o)}else{let d=c,l=a[Me];R2(r)&&(d.flags|=128),Ql(e,t,d,l,i,o,!0)}}function kk(e,t,n,r,i){let o=n[nn],a=Qe(n);o!==a&&wr(t,e,r,o,i);for(let s=Ie;s<n.length;s++){let c=n[s];cs(c[j],c,e,t,r,o)}}function vk(e,t,n,r,i){if(t)i?e.addClass(n,r):e.removeClass(n,r);else{let o=r.indexOf("-")===-1?void 0:It.DashCase;i==null?e.removeStyle(n,r,o):(typeof i=="string"&&i.endsWith("!important")&&(i=i.slice(0,-10),o|=It.Important),e.setStyle(n,r,i,o))}}function bi(e,t,n,r,i=!1){for(;n!==null;){if(n.type===128){n=i?n.projectionNext:n.next;continue}let o=t[n.index];o!==null&&r.push(Qe(o)),lt(o)&&l0(o,r);let a=n.type;if(a&8)bi(e,t,n.child,r);else if(a&32){let s=Xl(n,t),c;for(;c=s();)r.push(c)}else if(a&16){let s=c0(t,n);if(Array.isArray(s))r.push(...s);else{let c=Qt(t[be]);bi(c[j],c,s,r,!0)}}n=i?n.projectionNext:n.next}return r}function l0(e,t){for(let n=Ie;n<e.length;n++){let r=e[n],i=r[j].firstChild;i!==null&&bi(r[j],r,i,t)}e[nn]!==e[dt]&&t.push(e[nn])}function u0(e){if(e[xa]!==null){for(let t of e[xa])t.impl.addSequence(t);e[xa].length=0}}var h0=[];function Mk(e){return e[Ge]??wk(e)}function wk(e){let t=h0.pop()??Object.create(xk);return t.lView=e,t}function Ck(e){e.lView[Ge]!==e&&(e.lView=null,h0.push(e))}var xk=G(A({},Mn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{ki(e.lView)},consumerOnSignalRead(){this.lView[Ge]=this}});function Ik(e){let t=e[Ge]??Object.create(bk);return t.lView=e,t}var bk=G(A({},Mn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=Qt(e.lView);for(;t&&!p0(t[j]);)t=Qt(t);t&&Rd(t)},consumerOnSignalRead(){this.lView[Ge]=this}});function p0(e){return e.type!==2}function f0(e){if(e[pi]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[pi])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[V]&8192)}}var Dk=100;function Jl(e,t=0){let r=e[Vt].rendererFactory,i=!1;i||r.begin?.();try{Sk(e,t)}finally{i||r.end?.()}}function Sk(e,t){let n=Bd();try{qd(!0),Ml(e,t);let r=0;for(;gi(e);){if(r===Dk)throw new S(103,!1);r++,Ml(e,1)}}finally{qd(n)}}function y0(e,t){Hd(t?vi.Exhaustive:vi.OnlyDirtyViews);try{Jl(e)}finally{Hd(vi.Off)}}function _k(e,t,n,r){if(Hn(t))return;let i=t[V],o=!1,a=!1;_a(t);let s=!0,c=null,d=null;o||(p0(e)?(d=Mk(t),c=cr(d)):Co()===null?(s=!1,d=Ik(t),c=cr(d)):t[Ge]&&(Do(t[Ge]),t[Ge]=null));try{Td(t),_h(e.bindingStartIndex),n!==null&&t0(e,t,n,2,r);let l=(i&3)===3;if(!o)if(l){let y=e.preOrderCheckHooks;y!==null&&Oa(t,y,null)}else{let y=e.preOrderHooks;y!==null&&Va(t,y,0,null),Jd(t,0)}if(a||Ek(t),f0(t),m0(t,0),e.contentQueries!==null&&U2(e,t),!o)if(l){let y=e.contentCheckHooks;y!==null&&Oa(t,y)}else{let y=e.contentHooks;y!==null&&Va(t,y,1),Jd(t,1)}Ak(e,t);let f=e.components;f!==null&&k0(t,f,0);let g=e.viewQuery;if(g!==null&&ml(2,g,r),!o)if(l){let y=e.viewCheckHooks;y!==null&&Oa(t,y)}else{let y=e.viewHooks;y!==null&&Va(t,y,2),Jd(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Ca]){for(let y of t[Ca])y();t[Ca]=null}o||(u0(t),t[V]&=-73)}catch(l){throw o||ki(t),l}finally{d!==null&&(ri(d,c),s&&Ck(d)),Ea()}}function m0(e,t){for(let n=F2(e);n!==null;n=N2(n))for(let r=Ie;r<n.length;r++){let i=n[r];g0(i,t)}}function Ek(e){for(let t=F2(e);t!==null;t=N2(t)){if(!(t[V]&2))continue;let n=t[yi];for(let r=0;r<n.length;r++){let i=n[r];Rd(i)}}}function Lk(e,t,n){Y(18);let r=Je(t,e);g0(r,n),Y(19,r[ie])}function g0(e,t){ba(e)&&Ml(e,t)}function Ml(e,t){let r=e[j],i=e[V],o=e[Ge],a=!!(t===0&&i&16);if(a||=!!(i&64&&t===0),a||=!!(i&1024),a||=!!(o?.dirty&&bo(o)),a||=!1,o&&(o.dirty=!1),e[V]&=-9217,a)_k(r,e,r.template,e[ie]);else if(i&8192){let s=T(null);try{f0(e),m0(e,1);let c=r.components;c!==null&&k0(e,c,1),u0(e)}finally{T(s)}}}function k0(e,t,n){for(let r=0;r<t.length;r++)Lk(e,t[r],n)}function Ak(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let i=n[r];if(i<0)on(~i);else{let o=i,a=n[++r],s=n[++r];Lh(a,o);let c=t[o];Y(24,c),s(2,c),Y(25,c)}}}finally{on(-1)}}function e1(e,t){let n=Bd()?64:1088;for(e[Vt].changeDetectionScheduler?.notify(t);e;){e[V]|=n;let r=Qt(e);if(vr(e)&&!r)return e;e=r}return null}function v0(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function M0(e,t){let n=Ie+t;if(n<e.length)return e[n]}function Ri(e,t,n,r=!0){let i=t[j];if(Tk(i,t,e,n),r){let a=vl(n,e),s=t[te],c=s.parentNode(e[nn]);c!==null&&lk(i,e[Oe],s,t,c,a)}let o=t[Pn];o!==null&&o.firstChild!==null&&(o.firstChild=null)}function w0(e,t){let n=Di(e,t);return n!==void 0&&ss(n[j],n),n}function Di(e,t){if(e.length<=Ie)return;let n=Ie+t,r=e[n];if(r){let i=r[On];i!==null&&i!==e&&Kl(i,r),t>0&&(e[n-1][Ye]=r[Ye]);let o=di(e,Ie+t);dk(r[j],r);let a=o[jn];a!==null&&a.detachView(o[j]),r[Me]=null,r[Ye]=null,r[V]&=-129}return r}function Tk(e,t,n,r){let i=Ie+r,o=n.length;r>0&&(n[i-1][Ye]=t),r<o-Ie?(t[Ye]=n[i],kd(n,Ie+r,t)):(n.push(t),t[Ye]=null),t[Me]=n;let a=t[On];a!==null&&n!==a&&C0(a,t);let s=t[jn];s!==null&&s.insertView(e),Da(t),t[V]|=128}function C0(e,t){let n=e[yi],r=t[Me];if(wt(r))e[V]|=2;else{let i=r[Me][be];t[be]!==i&&(e[V]|=2)}n===null?e[yi]=[t]:n.push(t)}var an=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let t=this._lView,n=t[j];return bi(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n}get context(){return this._lView[ie]}set context(t){this._lView[ie]=t}get destroyed(){return Hn(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[Me];if(lt(t)){let n=t[fi],r=n?n.indexOf(this):-1;r>-1&&(Di(t,r),di(n,r))}this._attachedToViewContainer=!1}ss(this._lView[j],this._lView)}onDestroy(t){Pd(this._lView,t)}markForCheck(){e1(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[V]&=-129}reattach(){Da(this._lView),this._lView[V]|=128}detectChanges(){this._lView[V]|=1024,Jl(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[Fn].get(sk,Kh)}catch{this.exhaustive=Kh}}attachToViewContainerRef(){if(this._appRef)throw new S(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=vr(this._lView),n=this._lView[On];n!==null&&!t&&Kl(n,this._lView),o0(this._lView[j],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new S(902,!1);this._appRef=t;let n=vr(this._lView),r=this._lView[On];r!==null&&!n&&C0(r,this._lView),Da(this._lView)}};var Pi=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=Rk;constructor(n,r,i){this._declarationLView=n,this._declarationTContainer=r,this.elementRef=i}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,r){return this.createEmbeddedViewImpl(n,r)}createEmbeddedViewImpl(n,r,i){let o=Ti(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:r,dehydratedView:i});return new an(o)}}return e})();function Rk(){return Pk(qe(),q())}function Pk(e,t){return e.type&4?new Pi(t,e,ts(e,t)):null}function ds(e,t,n,r,i){let o=e.data[t];if(o===null)o=Fk(e,t,n,r,i),Eh()&&(o.flags|=32);else if(o.type&64){o.type=n,o.value=r,o.attrs=i;let a=Sh();o.injectorIndex=a===null?-1:a.injectorIndex}return Mr(o,!0),o}function Fk(e,t,n,r,i){let o=Od(),a=Vd(),s=a?o:o&&o.parent,c=e.data[t]=Ok(e,s,n,t,r,i);return Nk(e,c,o,a),c}function Nk(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function Ok(e,t,n,r,i,o){let a=t?t.injectorIndex:-1,s=0;return Nd()&&(s|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:a,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:s,providerIndexes:0,value:i,attrs:o,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var $I=new RegExp(`^(\\d+)*(${Cg}|${wg})*(.*)`);function Vk(e){let t=e[Sd]??[],r=e[Me][te],i=[];for(let o of t)o.data[H2]!==void 0?i.push(o):jk(o,r);e[Sd]=i}function jk(e,t){let n=0,r=e.firstChild;if(r){let i=e.data[j2];for(;n<i;){let o=r.nextSibling;K2(t,r,!1),r=o,n++}}}var Hk=()=>null,Bk=()=>null;function Ga(e,t){return Hk(e,t)}function x0(e,t,n){return Bk(e,t,n)}var I0=class{},ls=class{},wl=class{resolveComponentFactory(t){throw new S(917,!1)}},us=class{static NULL=new wl},Gn=class{},qt=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>qk()}return e})();function qk(){let e=q(),t=qe(),n=Je(t.index,e);return(wt(n)?n:e)[te]}var b0=(()=>{class e{static \u0275prov=P({token:e,providedIn:"root",factory:()=>null})}return e})();var ja={},Cl=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){let i=this.injector.get(t,ja,r);return i!==ja||n===ja?i:this.parentInjector.get(t,n,r)}};function Qh(e,t,n){let r=n?e.styles:null,i=n?e.classes:null,o=0;if(t!==null)for(let a=0;a<t.length;a++){let s=t[a];if(typeof s=="number")o=s;else if(o==1)i=pa(i,s);else if(o==2){let c=s,d=t[++a];r=pa(r,c+": "+d+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=i:e.classesWithoutHost=i}function x(e,t=0){let n=q();if(n===null)return R(e,t);let r=qe();return L2(r,n,xe(e),t)}function D0(e,t,n,r,i){let o=r===null?null:{"":-1},a=i(e,n);if(a!==null){let s=a,c=null,d=null;for(let l of a)if(l.resolveHostDirectives!==null){[s,c,d]=l.resolveHostDirectives(a);break}Wk(e,t,n,s,o,c,d)}o!==null&&r!==null&&zk(n,r,o)}function zk(e,t,n){let r=e.localNames=[];for(let i=0;i<t.length;i+=2){let o=n[t[i+1]];if(o==null)throw new S(-301,!1);r.push(t[i],o)}}function Uk(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function Wk(e,t,n,r,i,o,a){let s=r.length,c=!1;for(let g=0;g<s;g++){let y=r[g];!c&&Ct(y)&&(c=!0,Uk(e,n,g)),hl(Ua(n,t),e,y.type)}Yk(n,e.data.length,s);for(let g=0;g<s;g++){let y=r[g];y.providersResolver&&y.providersResolver(y)}let d=!1,l=!1,f=J2(e,t,s,null);s>0&&(n.directiveToIndex=new Map);for(let g=0;g<s;g++){let y=r[g];if(n.mergedAttrs=Ii(n.mergedAttrs,y.hostAttrs),Gk(e,n,t,f,y),Kk(f,y,i),a!==null&&a.has(y)){let[C,_]=a.get(y);n.directiveToIndex.set(y.type,[f,C+n.directiveStart,_+n.directiveStart])}else(o===null||!o.has(y))&&n.directiveToIndex.set(y.type,f);y.contentQueries!==null&&(n.flags|=4),(y.hostBindings!==null||y.hostAttrs!==null||y.hostVars!==0)&&(n.flags|=64);let M=y.type.prototype;!d&&(M.ngOnChanges||M.ngOnInit||M.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),d=!0),!l&&(M.ngOnChanges||M.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),l=!0),f++}$k(e,n,o)}function $k(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let i=e.data[r];if(n===null||!n.has(i))Jh(0,t,i,r),Jh(1,t,i,r),t2(t,r,!1);else{let o=n.get(i);e2(0,t,o,r),e2(1,t,o,r),t2(t,r,!0)}}}function Jh(e,t,n,r){let i=e===0?n.inputs:n.outputs;for(let o in i)if(i.hasOwnProperty(o)){let a;e===0?a=t.inputs??={}:a=t.outputs??={},a[o]??=[],a[o].push(r),S0(t,o)}}function e2(e,t,n,r){let i=e===0?n.inputs:n.outputs;for(let o in i)if(i.hasOwnProperty(o)){let a=i[o],s;e===0?s=t.hostDirectiveInputs??={}:s=t.hostDirectiveOutputs??={},s[a]??=[],s[a].push(r,o),S0(t,a)}}function S0(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function t2(e,t,n){let{attrs:r,inputs:i,hostDirectiveInputs:o}=e;if(r===null||!n&&i===null||n&&o===null||ql(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let a=null,s=0;for(;s<r.length;){let c=r[s];if(c===0){s+=4;continue}else if(c===5){s+=2;continue}else if(typeof c=="number")break;if(!n&&i.hasOwnProperty(c)){let d=i[c];for(let l of d)if(l===t){a??=[],a.push(c,r[s+1]);break}}else if(n&&o.hasOwnProperty(c)){let d=o[c];for(let l=0;l<d.length;l+=2)if(d[l]===t){a??=[],a.push(d[l+1],r[s+1]);break}}s+=2}e.initialInputs??=[],e.initialInputs.push(a)}function Gk(e,t,n,r,i){e.data[r]=i;let o=i.factory||(i.factory=Yt(i.type,!0)),a=new $n(o,Ct(i),x);e.blueprint[r]=a,n[r]=a,Zk(e,t,r,J2(e,n,i.hostVars,et),i)}function Zk(e,t,n,r,i){let o=i.hostBindings;if(o){let a=e.hostBindingOpCodes;a===null&&(a=e.hostBindingOpCodes=[]);let s=~t.index;Xk(a)!=s&&a.push(s),a.push(n,r,o)}}function Xk(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function Kk(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Ct(t)&&(n[""]=e)}}function Yk(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function _0(e,t,n,r,i,o,a,s){let c=t.consts,d=jt(c,a),l=ds(t,e,2,r,d);return o&&D0(t,n,l,jt(c,s),i),l.mergedAttrs=Ii(l.mergedAttrs,l.attrs),l.attrs!==null&&Qh(l,l.attrs,!1),l.mergedAttrs!==null&&Qh(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function E0(e,t){C2(e,t),_d(t)&&e.queries.elementEnd(t)}function t1(e){return A0(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function L0(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function A0(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function T0(e,t,n){return e[t]=n}function yt(e,t,n){if(n===et)return!1;let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Qk(e,t,n,r){let i=yt(e,t,n);return yt(e,t+1,r)||i}function nl(e,t,n){return function r(i){let o=rn(e)?Je(e.index,t):t;e1(o,5);let a=t[ie],s=n2(t,a,n,i),c=r.__ngNextListenerFn__;for(;c;)s=n2(t,a,c,i)&&s,c=c.__ngNextListenerFn__;return s}}function n2(e,t,n,r){let i=T(null);try{return Y(6,t,n),n(r)!==!1}catch(o){return rk(e,o),!1}finally{Y(7,t,n),T(i)}}function Jk(e,t,n,r,i,o,a,s){let c=Ia(e),d=!1,l=null;if(!r&&c&&(l=ev(t,n,o,e.index)),l!==null){let f=l.__ngLastListenerFn__||l;f.__ngNextListenerFn__=a,l.__ngLastListenerFn__=a,d=!0}else{let f=ut(e,n),g=r?r(f):f;Ig(n,g,o,s);let y=i.listen(g,o,s),M=r?C=>r(Qe(C[e.index])):e.index;R0(M,t,n,o,s,y,!1)}return d}function ev(e,t,n,r){let i=e.cleanup;if(i!=null)for(let o=0;o<i.length-1;o+=2){let a=i[o];if(a===n&&i[o+1]===r){let s=t[gr],c=i[o+2];return s&&s.length>c?s[c]:null}typeof a=="string"&&(o+=2)}return null}function R0(e,t,n,r,i,o,a){let s=t.firstCreatePass?wh(t):null,c=Mh(n),d=c.length;c.push(i,o),s&&s.push(r,e,d,(d+1)*(a?-1:1))}function r2(e,t,n,r,i,o){let a=t[n],s=t[j],d=s.data[n].outputs[r],f=a[d].subscribe(o);R0(e.index,s,t,i,o,f,!0)}var xl=Symbol("BINDING");var Il=class extends us{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Rn(t);return new Si(n,this.ngModule)}};function tv(e){return Object.keys(e).map(t=>{let[n,r,i]=e[t],o={propName:n,templateName:t,isSignal:(r&os.SignalBased)!==0};return i&&(o.transform=i),o})}function nv(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function rv(e,t,n){let r=t instanceof Be?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Cl(n,r):n}function iv(e){let t=e.get(Gn,null);if(t===null)throw new S(407,!1);let n=e.get(b0,null),r=e.get(en,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r,ngReflect:!1}}function ov(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Z2(t,n,n==="svg"?yh:n==="math"?mh:null)}var Si=class extends ls{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=tv(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=nv(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=Ng(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,i,o,a){Y(22);let s=T(null);try{let c=this.componentDef,d=av(r,c,a,o),l=rv(c,i||this.ngModule,t),f=iv(l),g=f.rendererFactory.createRenderer(null,c),y=r?Wg(g,r,c.encapsulation,l):ov(c,g),M=a?.some(i2)||o?.some(F=>typeof F!="function"&&F.bindings.some(i2)),C=Ul(null,d,null,512|Q2(c),null,null,f,g,l,null,z2(y,l,!0));C[ye]=y,_a(C);let _=null;try{let F=_0(ye,d,C,"#host",()=>d.directiveRegistry,!0,0);y&&(Y2(g,y,F),Ai(y,C)),$l(d,C,F),W2(d,F,C),E0(d,F),n!==void 0&&cv(F,this.ngContentSelectors,n),_=Je(F.index,C),C[ie]=_[ie],Zl(d,C,null)}catch(F){throw _!==null&&fl(_),fl(C),F}finally{Y(23),Ea()}return new Za(this.componentType,C,!!M)}finally{T(s)}}};function av(e,t,n,r){let i=e?["ng-version","20.0.6"]:Og(t.selectors[0]),o=null,a=null,s=0;if(n)for(let l of n)s+=l[xl].requiredVars,l.create&&(l.targetIdx=0,(o??=[]).push(l)),l.update&&(l.targetIdx=0,(a??=[]).push(l));if(r)for(let l=0;l<r.length;l++){let f=r[l];if(typeof f!="function")for(let g of f.bindings){s+=g[xl].requiredVars;let y=l+1;g.create&&(g.targetIdx=y,(o??=[]).push(g)),g.update&&(g.targetIdx=y,(a??=[]).push(g))}}let c=[t];if(r)for(let l of r){let f=typeof l=="function"?l:l.type,g=wd(f);c.push(g)}return zl(0,null,sv(o,a),1,s,c,null,null,null,[i],null)}function sv(e,t){return!e&&!t?null:n=>{if(n&1&&e)for(let r of e)r.create();if(n&2&&t)for(let r of t)r.update()}}function i2(e){let t=e[xl].kind;return t==="input"||t==="twoWay"}var Za=class extends I0{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n,r){super(),this._rootLView=n,this._hasInputBindings=r,this._tNode=mi(n[j],ye),this.location=ts(this._tNode,n),this.instance=Je(this._tNode.index,n)[ie],this.hostView=this.changeDetectorRef=new an(n,void 0),this.componentType=t}setInput(t,n){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let i=this._rootLView,o=Gl(r,i[j],i,t,n);this.previousInputValues.set(t,n);let a=Je(r.index,i);e1(a,1)}get injector(){return new Wn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function cv(e,t,n){let r=e.projection=[];for(let i=0;i<t.length;i++){let o=n[i];r.push(o!=null&&o.length?Array.from(o):null)}}var Fi=(()=>{class e{static __NG_ELEMENT_ID__=dv}return e})();function dv(){let e=qe();return uv(e,q())}var lv=Fi,P0=class extends lv{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return ts(this._hostTNode,this._hostLView)}get injector(){return new Wn(this._hostTNode,this._hostLView)}get parentInjector(){let t=Vl(this._hostTNode,this._hostLView);if(I2(t)){let n=qa(t,this._hostLView),r=Ba(t),i=n[j].data[r+8];return new Wn(i,n)}else return new Wn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=o2(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-Ie}createEmbeddedView(t,n,r){let i,o;typeof r=="number"?i=r:r!=null&&(i=r.index,o=r.injector);let a=Ga(this._lContainer,t.ssrId),s=t.createEmbeddedViewImpl(n||{},o,a);return this.insertImpl(s,i,xr(this._hostTNode,a)),s}createComponent(t,n,r,i,o,a,s){let c=t&&!Km(t),d;if(c)d=n;else{let _=n||{};d=_.index,r=_.injector,i=_.projectableNodes,o=_.environmentInjector||_.ngModuleRef,a=_.directives,s=_.bindings}let l=c?t:new Si(Rn(t)),f=r||this.parentInjector;if(!o&&l.ngModule==null){let F=(c?f:this.parentInjector).get(Be,null);F&&(o=F)}let g=Rn(l.componentType??{}),y=Ga(this._lContainer,g?.id??null),M=y?.firstChild??null,C=l.create(f,i,M,o,a,s);return this.insertImpl(C.hostView,d,xr(this._hostTNode,y)),C}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let i=t._lView;if(gh(i)){let s=this.indexOf(t);if(s!==-1)this.detach(s);else{let c=i[Me],d=new P0(c,c[Oe],c[Me]);d.detach(d.indexOf(t))}}let o=this._adjustIndex(n),a=this._lContainer;return Ri(a,i,o,r),t.attachToViewContainerRef(),kd(rl(a),o,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=o2(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Di(this._lContainer,n);r&&(di(rl(this._lContainer),n),ss(r[j],r))}detach(t){let n=this._adjustIndex(t,-1),r=Di(this._lContainer,n);return r&&di(rl(this._lContainer),n)!=null?new an(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function o2(e){return e[fi]}function rl(e){return e[fi]||(e[fi]=[])}function uv(e,t){let n,r=t[e.index];return lt(r)?n=r:(n=v0(r,t,null,e),t[e.index]=n,Wl(t,n)),pv(n,t,e,r),new P0(n,e,t)}function hv(e,t){let n=e[te],r=n.createComment(""),i=ut(t,e),o=n.parentNode(i);return $a(n,o,r,n.nextSibling(i),!1),r}var pv=mv,fv=()=>!1;function yv(e,t,n){return fv(e,t,n)}function mv(e,t,n,r){if(e[nn])return;let i;n.type&8?i=Qe(r):i=hv(t,n),e[nn]=i}var a2=new Set;function Er(e){a2.has(e)||(a2.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var _i=class{};var Ei=class extends _i{injector;componentFactoryResolver=new Il(this);instance=null;constructor(t){super();let n=new En([...t.providers,{provide:_i,useValue:this},{provide:us,useValue:this.componentFactoryResolver}],t.parent||ui(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function F0(e,t,n=null){return new Ei({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var gv=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Cd(!1,n.type),i=r.length>0?F0([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,i)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=P({token:e,providedIn:"environment",factory:()=>new e(R(Be))})}return e})();function me(e){return Dr(()=>{let t=N0(e),n=G(A({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===jl.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?i=>i.get(gv).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Bt.Emulated,styles:e.styles||Ne,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&Er("NgStandalone"),O0(n);let r=e.dependencies;return n.directiveDefs=s2(r,!1),n.pipeDefs=s2(r,!0),n.id=Cv(n),n})}function kv(e){return Rn(e)||wd(e)}function vv(e){return e!==null}function Dt(e){return Dr(()=>({type:e.type,bootstrap:e.bootstrap||Ne,declarations:e.declarations||Ne,imports:e.imports||Ne,exports:e.exports||Ne,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Mv(e,t){if(e==null)return tn;let n={};for(let r in e)if(e.hasOwnProperty(r)){let i=e[r],o,a,s,c;Array.isArray(i)?(s=i[0],o=i[1],a=i[2]??o,c=i[3]||null):(o=i,a=i,s=os.None,c=null),n[o]=[r,s,c],t[o]=a}return n}function wv(e){if(e==null)return tn;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function ge(e){return Dr(()=>{let t=N0(e);return O0(t),t})}function hs(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function N0(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||tn,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Ne,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:Mv(e.inputs,t),outputs:wv(e.outputs),debugInfo:null}}function O0(e){e.features?.forEach(t=>t(e))}function s2(e,t){if(!e)return null;let n=t?ch:kv;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(vv)}function Cv(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let o of r.join("|"))t=Math.imul(31,t)+o.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function xv(e){return Object.getPrototypeOf(e.prototype).constructor}function tt(e){let t=xv(e.type),n=!0,r=[e];for(;t;){let i;if(Ct(e))i=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new S(903,!1);i=t.\u0275dir}if(i){if(n){r.push(i);let a=e;a.inputs=il(e.inputs),a.declaredInputs=il(e.declaredInputs),a.outputs=il(e.outputs);let s=i.hostBindings;s&&_v(e,s);let c=i.viewQuery,d=i.contentQueries;if(c&&Dv(e,c),d&&Sv(e,d),Iv(e,i),nh(e.outputs,i.outputs),Ct(i)&&i.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(i.data.animation)}}let o=i.features;if(o)for(let a=0;a<o.length;a++){let s=o[a];s&&s.ngInherit&&s(e),s===tt&&(n=!1)}}t=Object.getPrototypeOf(t)}bv(r)}function Iv(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function bv(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let i=e[r];i.hostVars=t+=i.hostVars,i.hostAttrs=Ii(i.hostAttrs,n=Ii(n,i.hostAttrs))}}function il(e){return e===tn?{}:e===Ne?[]:e}function Dv(e,t){let n=e.viewQuery;n?e.viewQuery=(r,i)=>{t(r,i),n(r,i)}:e.viewQuery=t}function Sv(e,t){let n=e.contentQueries;n?e.contentQueries=(r,i,o)=>{t(r,i,o),n(r,i,o)}:e.contentQueries=t}function _v(e,t){let n=e.hostBindings;n?e.hostBindings=(r,i)=>{t(r,i),n(r,i)}:e.hostBindings=t}function Ev(e,t,n,r,i,o,a,s,c){let d=t.consts,l=ds(t,e,4,a||null,s||null);Fd()&&D0(t,n,l,jt(d,c),i0),l.mergedAttrs=Ii(l.mergedAttrs,l.attrs),C2(t,l);let f=l.tView=zl(2,l,r,i,o,t.directiveRegistry,t.pipeRegistry,null,t.schemas,d,null);return t.queries!==null&&(t.queries.template(t,l),f.queries=t.queries.embeddedTView(l)),l}function Ir(e,t,n,r,i,o,a,s,c,d,l){let f=n+ye,g=t.firstCreatePass?Ev(f,t,e,r,i,o,a,s,d):t.data[f];c&&(g.flags|=c),Mr(g,!1);let y=Lv(t,e,g,n);Aa()&&Yl(t,e,y,g),Ai(y,e);let M=v0(y,e,y,g);return e[f]=M,Wl(e,M),yv(M,g,e),Ia(g)&&$l(t,e,g),d!=null&&n0(e,g,l),g}function St(e,t,n,r,i,o,a,s){let c=q(),d=de(),l=jt(d.consts,o);return Ir(c,d,e,t,n,r,i,l,void 0,a,s),St}var Lv=Av;function Av(e,t,n,r){return Ta(!0),t[te].createComment("")}var n1=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(n1||{}),Ni=new E(""),V0=!1,bl=class extends Nt{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,ph()&&(this.destroyRef=L(Mi,{optional:!0})??void 0,this.pendingTasks=L(zn,{optional:!0})??void 0)}emit(t){let n=T(null);try{super.next(t)}finally{T(n)}}subscribe(t,n,r){let i=t,o=n||(()=>null),a=r;if(t&&typeof t=="object"){let c=t;i=c.next?.bind(c),o=c.error?.bind(c),a=c.complete?.bind(c)}this.__isAsync&&(o=this.wrapInTimeout(o),i&&(i=this.wrapInTimeout(i)),a&&(a=this.wrapInTimeout(a)));let s=super.subscribe({next:i,error:o,complete:a});return t instanceof X&&t.add(s),s}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},ze=bl;function j0(e){let t,n;function r(){e=wi;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function c2(e){return queueMicrotask(()=>e()),()=>{e=wi}}var r1="isAngularZone",Xa=r1+"_ID",Tv=0,le=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new ze(!1);onMicrotaskEmpty=new ze(!1);onStable=new ze(!1);onError=new ze(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:i=!1,scheduleInRootZone:o=V0}=t;if(typeof Zone>"u")throw new S(908,!1);Zone.assertZonePatched();let a=this;a._nesting=0,a._outer=a._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(a._inner=a._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(a._inner=a._inner.fork(Zone.longStackTraceZoneSpec)),a.shouldCoalesceEventChangeDetection=!i&&r,a.shouldCoalesceRunChangeDetection=i,a.callbackScheduled=!1,a.scheduleInRootZone=o,Fv(a)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(r1)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new S(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new S(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,i){let o=this._inner,a=o.scheduleEventTask("NgZoneEvent: "+i,t,Rv,wi,wi);try{return o.runTask(a,n,r)}finally{o.cancelTask(a)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Rv={};function i1(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Pv(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){j0(()=>{e.callbackScheduled=!1,Dl(e),e.isCheckStableRunning=!0,i1(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),Dl(e)}function Fv(e){let t=()=>{Pv(e)},n=Tv++;e._inner=e._inner.fork({name:"angular",properties:{[r1]:!0,[Xa]:n,[Xa+n]:!0},onInvokeTask:(r,i,o,a,s,c)=>{if(Nv(c))return r.invokeTask(o,a,s,c);try{return d2(e),r.invokeTask(o,a,s,c)}finally{(e.shouldCoalesceEventChangeDetection&&a.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),l2(e)}},onInvoke:(r,i,o,a,s,c,d)=>{try{return d2(e),r.invoke(o,a,s,c,d)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Ov(c)&&t(),l2(e)}},onHasTask:(r,i,o,a)=>{r.hasTask(o,a),i===o&&(a.change=="microTask"?(e._hasPendingMicrotasks=a.microTask,Dl(e),i1(e)):a.change=="macroTask"&&(e.hasPendingMacrotasks=a.macroTask))},onHandleError:(r,i,o,a)=>(r.handleError(o,a),e.runOutsideAngular(()=>e.onError.emit(a)),!1)})}function Dl(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function d2(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function l2(e){e._nesting--,i1(e)}var Ka=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new ze;onMicrotaskEmpty=new ze;onStable=new ze;onError=new ze;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,i){return t.apply(n,r)}};function Nv(e){return H0(e,"__ignore_ng_zone__")}function Ov(e){return H0(e,"__scheduler_tick__")}function H0(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var B0=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=P({token:e,providedIn:"root",factory:()=>new e})}return e})();var o1=new E("");function Kn(e){return!!e&&typeof e.then=="function"}function ps(e){return!!e&&typeof e.subscribe=="function"}var q0=new E("");var a1=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=L(q0,{optional:!0})??[];injector=L(Jt);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let i of this.appInits){let o=mr(this.injector,i);if(Kn(o))n.push(o);else if(ps(o)){let a=new Promise((s,c)=>{o.subscribe({complete:s,error:c})});n.push(a)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(i=>{this.reject(i)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),z0=new E("");function U0(){Rc(()=>{let e="";throw new S(600,e)})}function W0(e){return e.isBoundToModule}var Vv=10;var Oi=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=L(ht);afterRenderManager=L(B0);zonelessEnabled=L(Ra);rootEffectScheduler=L(Qd);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new Nt;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=L(zn);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(Ce(n=>!n))}constructor(){L(Ni,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:i=>{i&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=L(Be);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,i=Jt.NULL){return this._injector.get(le).run(()=>{Y(10);let a=n instanceof ls;if(!this._injector.get(a1).done){let M="";throw new S(405,M)}let c;a?c=n:c=this._injector.get(us).resolveComponentFactory(n),this.componentTypes.push(c.componentType);let d=W0(c)?void 0:this._injector.get(_i),l=r||c.selector,f=c.create(i,[],l,d),g=f.location.nativeElement,y=f.injector.get(o1,null);return y?.registerApplication(g),f.onDestroy(()=>{this.detachView(f.hostView),xi(this.components,f),y?.unregisterApplication(g)}),this._loadComponent(f),Y(11,f),f})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){Y(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(n1.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new S(101,!1);let n=T(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,T(n),this.afterTick.next(),Y(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Gn,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<Vv;)Y(14),this.synchronizeOnce(),Y(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let n=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:i}of this.allViews){if(!r&&!gi(i))continue;let o=r&&!this.zonelessEnabled?0:1;Jl(i,o),n=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}n||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>gi(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;xi(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView);try{this.tick()}catch(i){this.internalErrorHandler(i)}this.components.push(n),this._injector.get(z0,[]).forEach(i=>i(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>xi(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new S(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function xi(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function Lr(e,t,n,r){let i=q(),o=Bn();if(yt(i,o,t)){let a=de(),s=La();ek(s,i,e,t,n,r)}return Lr}function fs(){return q()[be][ie]}var Sl=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),i=Math.max(t,n),o=this.detach(i);if(i-r>1){let a=this.detach(r);this.attach(r,o),this.attach(i,a)}else this.attach(r,o)}move(t,n){this.attach(n,this.detach(t))}};function ol(e,t,n,r,i){return e===n&&Object.is(t,r)?1:Object.is(i(e,t),i(n,r))?-1:0}function jv(e,t,n){let r,i,o=0,a=e.length-1,s=void 0;if(Array.isArray(t)){let c=t.length-1;for(;o<=a&&o<=c;){let d=e.at(o),l=t[o],f=ol(o,d,o,l,n);if(f!==0){f<0&&e.updateValue(o,l),o++;continue}let g=e.at(a),y=t[c],M=ol(a,g,c,y,n);if(M!==0){M<0&&e.updateValue(a,y),a--,c--;continue}let C=n(o,d),_=n(a,g),F=n(o,l);if(Object.is(F,_)){let Xt=n(c,y);Object.is(Xt,C)?(e.swap(o,a),e.updateValue(a,y),c--,a--):e.move(a,o),e.updateValue(o,l),o++;continue}if(r??=new Ya,i??=h2(e,o,a,n),_l(e,r,o,F))e.updateValue(o,l),o++,a++;else if(i.has(F))r.set(C,e.detach(o)),a--;else{let Xt=e.create(o,t[o]);e.attach(o,Xt),o++,a++}}for(;o<=c;)u2(e,r,n,o,t[o]),o++}else if(t!=null){let c=t[Symbol.iterator](),d=c.next();for(;!d.done&&o<=a;){let l=e.at(o),f=d.value,g=ol(o,l,o,f,n);if(g!==0)g<0&&e.updateValue(o,f),o++,d=c.next();else{r??=new Ya,i??=h2(e,o,a,n);let y=n(o,f);if(_l(e,r,o,y))e.updateValue(o,f),o++,a++,d=c.next();else if(!i.has(y))e.attach(o,e.create(o,f)),o++,a++,d=c.next();else{let M=n(o,l);r.set(M,e.detach(o)),a--}}}for(;!d.done;)u2(e,r,n,e.length,d.value),d=c.next()}for(;o<=a;)e.destroy(e.detach(a--));r?.forEach(c=>{e.destroy(c)})}function _l(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function u2(e,t,n,r,i){if(_l(e,t,r,n(r,i)))e.updateValue(r,i);else{let o=e.create(r,i);e.attach(r,o)}}function h2(e,t,n,r){let i=new Set;for(let o=t;o<=n;o++)i.add(r(o,e.at(o)));return i}var Ya=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let i=this._vMap;for(;i.has(r);)r=i.get(r);i.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let i=this._vMap;for(;i.has(r);)r=i.get(r),t(r,n)}}};function H(e,t,n,r,i,o,a,s){Er("NgControlFlow");let c=q(),d=de(),l=jt(d.consts,o);return Ir(c,d,e,t,n,r,i,l,256,a,s),Yn}function Yn(e,t,n,r,i,o,a,s){Er("NgControlFlow");let c=q(),d=de(),l=jt(d.consts,o);return Ir(c,d,e,t,n,r,i,l,512,a,s),Yn}function B(e,t){Er("NgControlFlow");let n=q(),r=Bn(),i=n[r]!==et?n[r]:-1,o=i!==-1?Qa(n,ye+i):void 0,a=0;if(yt(n,r,e)){let s=T(null);try{if(o!==void 0&&w0(o,a),e!==-1){let c=ye+e,d=Qa(n,c),l=Tl(n[j],c),f=x0(d,l,n),g=Ti(n,l,t,{dehydratedView:f});Ri(d,g,a,xr(l,f))}}finally{T(s)}}else if(o!==void 0){let s=M0(o,a);s!==void 0&&(s[ie]=t)}}var El=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-Ie}};function ys(e){return e}function nt(e,t){return t}var Ll=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function De(e,t,n,r,i,o,a,s,c,d,l,f,g){Er("NgControlFlow");let y=q(),M=de(),C=c!==void 0,_=q(),F=s?a.bind(_[be][ie]):a,Xt=new Ll(C,F);_[ye+e]=Xt,Ir(y,M,e+1,t,n,r,i,jt(M.consts,o),256),C&&Ir(y,M,e+2,c,d,l,f,jt(M.consts,g),512)}var Al=class extends Sl{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-Ie}at(t){return this.getLView(t)[ie].$implicit}attach(t,n){let r=n[Pn];this.needsIndexUpdate||=t!==this.length,Ri(this.lContainer,n,t,xr(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,Hv(this.lContainer,t)}create(t,n){let r=Ga(this.lContainer,this.templateTNode.tView.ssrId),i=Ti(this.hostLView,this.templateTNode,new El(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),i}destroy(t){ss(t[j],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[ie].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[ie].$index=t}getLView(t){return Bv(this.lContainer,t)}};function Se(e){let t=T(null),n=Ht();try{let r=q(),i=r[j],o=r[n],a=n+1,s=Qa(r,a);if(o.liveCollection===void 0){let d=Tl(i,a);o.liveCollection=new Al(s,r,d)}else o.liveCollection.reset();let c=o.liveCollection;if(jv(c,e,o.trackByFn),c.updateIndexes(),o.hasEmptyBlock){let d=Bn(),l=c.length===0;if(yt(r,d,l)){let f=n+2,g=Qa(r,f);if(l){let y=Tl(i,f),M=x0(g,y,r),C=Ti(r,y,void 0,{dehydratedView:M});Ri(g,C,0,xr(y,M))}else i.firstUpdatePass&&Vk(g),w0(g,0)}}}finally{T(t)}}function Qa(e,t){return e[t]}function Hv(e,t){return Di(e,t)}function Bv(e,t){return M0(e,t)}function Tl(e,t){return mi(e,t)}function v(e,t,n){let r=q(),i=Bn();if(yt(r,i,t)){let o=de(),a=La();r0(a,r,e,t,r[te],n)}return v}function Rl(e,t,n,r,i){Gl(t,e,n,i?"class":"style",r)}function u(e,t,n,r){let i=q(),o=de(),a=ye+e,s=i[te],c=o.firstCreatePass?_0(a,o,i,t,i0,Fd(),n,r):o.data[a],d=qv(o,i,c,s,t,e);i[a]=d;let l=Ia(c);return Mr(c,!0),Y2(s,d,c),!as(c)&&Aa()&&Yl(o,i,d,c),(Ch()===0||l)&&Ai(d,i),xh(),l&&($l(o,i,c),W2(o,c,i)),r!==null&&n0(i,c),u}function h(){let e=qe();Vd()?jd():(e=e.parent,Mr(e,!1));let t=e;bh(t)&&Dh(),Ih();let n=de();return n.firstCreatePass&&E0(n,t),t.classesWithoutHost!=null&&ng(t)&&Rl(n,t,q(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&rg(t)&&Rl(n,t,q(),t.stylesWithoutHost,!1),h}function w(e,t,n,r){return u(e,t,n,r),h(),w}var qv=(e,t,n,r,i,o)=>(Ta(!0),Z2(r,i,Oh()));function Q(){return q()}var Un=void 0;function zv(e){let t=Math.floor(Math.abs(e)),n=e.toString().replace(/^[^.]*\.?/,"").length;return t===1&&n===0?1:5}var Uv=["en",[["a","p"],["AM","PM"],Un],[["AM","PM"],Un,Un],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Un,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Un,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Un,"{1} 'at' {0}",Un],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",zv],al={};function Xe(e){let t=Wv(e),n=p2(t);if(n)return n;let r=t.split("-")[0];if(n=p2(r),n)return n;if(r==="en")return Uv;throw new S(701,!1)}function p2(e){return e in al||(al[e]=Ln.ng&&Ln.ng.common&&Ln.ng.common.locales&&Ln.ng.common.locales[e]),al[e]}var se=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(se||{});function Wv(e){return e.toLowerCase().replace(/_/g,"-")}var Vi="en-US";var $v=Vi;function $0(e){typeof e=="string"&&($v=e.toLowerCase().replace(/_/g,"-"))}function I(e,t,n){let r=q(),i=de(),o=qe();return G0(i,r,r[te],o,e,t,n),I}function G0(e,t,n,r,i,o,a){let s=!0,c=null;if((r.type&3||a)&&(c??=nl(r,t,o),Jk(r,e,t,a,n,i,o,c)&&(s=!1)),s){let d=r.outputs?.[i],l=r.hostDirectiveOutputs?.[i];if(l&&l.length)for(let f=0;f<l.length;f+=2){let g=l[f],y=l[f+1];c??=nl(r,t,o),r2(r,t,g,y,i,c)}if(d&&d.length)for(let f of d)c??=nl(r,t,o),r2(r,t,f,i,i,c)}}function k(e=1){return Nh(e)}function Gv(e,t){let n=null,r=Ag(e);for(let i=0;i<t.length;i++){let o=t[i];if(o==="*"){n=i;continue}if(r===null?G2(e,o,!0):Pg(r,o))return i}return n}function s1(e){let t=q()[be][Oe];if(!t.projection){let n=e?e.length:1,r=t.projection=ah(n,null),i=r.slice(),o=t.child;for(;o!==null;){if(o.type!==128){let a=e?Gv(o,e):0;a!==null&&(i[a]?i[a].projectionNext=o:r[a]=o,i[a]=o)}o=o.next}}}function c1(e,t=0,n,r,i,o){let a=q(),s=de(),c=r?e+1:null;c!==null&&Ir(a,s,c,r,i,o,null,n);let d=ds(s,ye+e,16,null,n||null);d.projection===null&&(d.projection=t),jd();let f=!a[Pn]||Nd();a[be][Oe].projection[d.projection]===null&&c!==null?Zv(a,s,c):f&&!as(d)&&gk(s,a,d)}function Zv(e,t,n){let r=ye+n,i=t.data[r],o=e[r],a=Ga(o,i.tView.ssrId),s=Ti(e,i,void 0,{dehydratedView:a});Ri(o,s,0,xr(i,a))}function Na(e,t){return e<<17|t<<2}function Zn(e){return e>>17&32767}function Xv(e){return(e&2)==2}function Kv(e,t){return e&131071|t<<17}function Pl(e){return e|2}function br(e){return(e&131068)>>2}function sl(e,t){return e&-131069|t<<2}function Yv(e){return(e&1)===1}function Fl(e){return e|1}function Qv(e,t,n,r,i,o){let a=o?t.classBindings:t.styleBindings,s=Zn(a),c=br(a);e[r]=n;let d=!1,l;if(Array.isArray(n)){let f=n;l=f[1],(l===null||yr(f,l)>0)&&(d=!0)}else l=n;if(i)if(c!==0){let g=Zn(e[s+1]);e[r+1]=Na(g,s),g!==0&&(e[g+1]=sl(e[g+1],r)),e[s+1]=Kv(e[s+1],r)}else e[r+1]=Na(s,0),s!==0&&(e[s+1]=sl(e[s+1],r)),s=r;else e[r+1]=Na(c,0),s===0?s=r:e[c+1]=sl(e[c+1],r),c=r;d&&(e[r+1]=Pl(e[r+1])),f2(e,l,r,!0),f2(e,l,r,!1),Jv(t,l,e,r,o),a=Na(s,c),o?t.classBindings=a:t.styleBindings=a}function Jv(e,t,n,r,i){let o=i?e.residualClasses:e.residualStyles;o!=null&&typeof t=="string"&&yr(o,t)>=0&&(n[r+1]=Fl(n[r+1]))}function f2(e,t,n,r){let i=e[n+1],o=t===null,a=r?Zn(i):br(i),s=!1;for(;a!==0&&(s===!1||o);){let c=e[a],d=e[a+1];e4(c,t)&&(s=!0,e[a+1]=r?Fl(d):Pl(d)),a=r?Zn(d):br(d)}s&&(e[n+1]=r?Pl(i):Fl(i))}function e4(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?yr(e,t)>=0:!1}var ft={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function t4(e){return e.substring(ft.key,ft.keyEnd)}function n4(e){return r4(e),Z0(e,X0(e,0,ft.textEnd))}function Z0(e,t){let n=ft.textEnd;return n===t?-1:(t=ft.keyEnd=i4(e,ft.key=t,n),X0(e,t,n))}function r4(e){ft.key=0,ft.keyEnd=0,ft.value=0,ft.valueEnd=0,ft.textEnd=e.length}function X0(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function i4(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function ms(e,t,n){return K0(e,t,n,!1),ms}function ji(e,t){return K0(e,t,null,!0),ji}function _e(e){a4(h4,o4,e,!0)}function o4(e,t){for(let n=n4(t);n>=0;n=Z0(t,n))va(e,t4(t),!0)}function K0(e,t,n,r){let i=q(),o=de(),a=Ud(2);if(o.firstUpdatePass&&Q0(o,e,a,r),t!==et&&yt(i,a,t)){let s=o.data[Ht()];J0(o,s,i,i[te],e,i[a+1]=f4(t,n),r,a)}}function a4(e,t,n,r){let i=de(),o=Ud(2);i.firstUpdatePass&&Q0(i,null,o,r);let a=q();if(n!==et&&yt(a,o,n)){let s=i.data[Ht()];if(ep(s,r)&&!Y0(i,o)){let c=r?s.classesWithoutHost:s.stylesWithoutHost;c!==null&&(n=pa(c,n||"")),Rl(i,s,a,n,r)}else p4(i,s,a,a[te],a[o+1],a[o+1]=u4(e,t,n),r,o)}}function Y0(e,t){return t>=e.expandoStartIndex}function Q0(e,t,n,r){let i=e.data;if(i[n+1]===null){let o=i[Ht()],a=Y0(e,n);ep(o,r)&&t===null&&!a&&(t=!1),t=s4(i,o,t,r),Qv(i,o,t,n,a,r)}}function s4(e,t,n,r){let i=Th(e),o=r?t.residualClasses:t.residualStyles;if(i===null)(r?t.classBindings:t.styleBindings)===0&&(n=cl(null,e,t,n,r),n=Li(n,t.attrs,r),o=null);else{let a=t.directiveStylingLast;if(a===-1||e[a]!==i)if(n=cl(i,e,t,n,r),o===null){let c=c4(e,t,r);c!==void 0&&Array.isArray(c)&&(c=cl(null,e,t,c[1],r),c=Li(c,t.attrs,r),d4(e,t,r,c))}else o=l4(e,t,r)}return o!==void 0&&(r?t.residualClasses=o:t.residualStyles=o),n}function c4(e,t,n){let r=n?t.classBindings:t.styleBindings;if(br(r)!==0)return e[Zn(r)]}function d4(e,t,n,r){let i=n?t.classBindings:t.styleBindings;e[Zn(i)]=r}function l4(e,t,n){let r,i=t.directiveEnd;for(let o=1+t.directiveStylingLast;o<i;o++){let a=e[o].hostAttrs;r=Li(r,a,n)}return Li(r,t.attrs,n)}function cl(e,t,n,r,i){let o=null,a=n.directiveEnd,s=n.directiveStylingLast;for(s===-1?s=n.directiveStart:s++;s<a&&(o=t[s],r=Li(r,o.hostAttrs,i),o!==e);)s++;return e!==null&&(n.directiveStylingLast=s),r}function Li(e,t,n){let r=n?1:2,i=-1;if(t!==null)for(let o=0;o<t.length;o++){let a=t[o];typeof a=="number"?i=a:i===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),va(e,a,n?!0:t[++o]))}return e===void 0?null:e}function u4(e,t,n){if(n==null||n==="")return Ne;let r=[],i=Bl(n);if(Array.isArray(i))for(let o=0;o<i.length;o++)e(r,i[o],!0);else if(typeof i=="object")for(let o in i)i.hasOwnProperty(o)&&e(r,o,i[o]);else typeof i=="string"&&t(r,i);return r}function h4(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&va(e,r,n)}function p4(e,t,n,r,i,o,a,s){i===et&&(i=Ne);let c=0,d=0,l=0<i.length?i[0]:null,f=0<o.length?o[0]:null;for(;l!==null||f!==null;){let g=c<i.length?i[c+1]:void 0,y=d<o.length?o[d+1]:void 0,M=null,C;l===f?(c+=2,d+=2,g!==y&&(M=f,C=y)):f===null||l!==null&&l<f?(c+=2,M=l):(d+=2,M=f,C=y),M!==null&&J0(e,t,n,r,M,C,a,s),l=c<i.length?i[c]:null,f=d<o.length?o[d]:null}}function J0(e,t,n,r,i,o,a,s){if(!(t.type&3))return;let c=e.data,d=c[s+1],l=Yv(d)?y2(c,t,n,i,br(d),a):void 0;if(!Ja(l)){Ja(o)||Xv(d)&&(o=y2(c,null,n,i,s,a));let f=Ed(Ht(),n);vk(r,a,f,i,o)}}function y2(e,t,n,r,i,o){let a=t===null,s;for(;i>0;){let c=e[i],d=Array.isArray(c),l=d?c[1]:c,f=l===null,g=n[i+1];g===et&&(g=f?Ne:void 0);let y=f?Ma(g,r):l===r?g:void 0;if(d&&!Ja(y)&&(y=Ma(c,r)),Ja(y)&&(s=y,a))return s;let M=e[i+1];i=a?Zn(M):br(M)}if(t!==null){let c=o?t.residualClasses:t.residualStyles;c!=null&&(s=Ma(c,r))}return s}function Ja(e){return e!==void 0}function f4(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=$e(Bl(e)))),e}function ep(e,t){return(e.flags&(t?8:16))!==0}function m(e,t=""){let n=q(),r=de(),i=e+ye,o=r.firstCreatePass?ds(r,i,1,t,null):r.data[i],a=y4(r,n,o,t,e);n[i]=a,Aa()&&Yl(r,n,a,o),Mr(o,!1)}var y4=(e,t,n,r,i)=>(Ta(!0),Vg(t[te],r));function tp(e,t,n,r=""){return yt(e,Bn(),n)?t+ma(n)+r:et}function Ee(e){return z("",e),Ee}function z(e,t,n){let r=q(),i=tp(r,e,t,n);return i!==et&&m4(r,Ht(),i),z}function m4(e,t,n){let r=Ed(t,e);jg(e[te],r,n)}function mt(e,t,n){Xd(t)&&(t=t());let r=q(),i=Bn();if(yt(r,i,t)){let o=de(),a=La();r0(a,r,e,t,r[te],n)}return mt}function _t(e,t){let n=Xd(e);return n&&e.set(t),n}function gt(e,t){let n=q(),r=de(),i=qe();return G0(r,n,n[te],i,e,t),gt}function gs(e,t,n=""){return tp(q(),e,t,n)}function g4(e,t,n){let r=de();if(r.firstCreatePass){let i=Ct(e);Nl(n,r.data,r.blueprint,i,!0),Nl(t,r.data,r.blueprint,i,!1)}}function Nl(e,t,n,r,i){if(e=xe(e),Array.isArray(e))for(let o=0;o<e.length;o++)Nl(e[o],t,n,r,i);else{let o=de(),a=q(),s=qe(),c=_n(e)?e:xe(e.provide),d=Id(e),l=s.providerIndexes&1048575,f=s.directiveStart,g=s.providerIndexes>>20;if(_n(e)||!e.multi){let y=new $n(d,i,x),M=ll(c,t,i?l:l+g,f);M===-1?(hl(Ua(s,a),o,c),dl(o,e,t.length),t.push(c),s.directiveStart++,s.directiveEnd++,i&&(s.providerIndexes+=1048576),n.push(y),a.push(y)):(n[M]=y,a[M]=y)}else{let y=ll(c,t,l+g,f),M=ll(c,t,l,l+g),C=y>=0&&n[y],_=M>=0&&n[M];if(i&&!_||!i&&!C){hl(Ua(s,a),o,c);let F=M4(i?v4:k4,n.length,i,r,d);!i&&_&&(n[M].providerFactory=F),dl(o,e,t.length,0),t.push(c),s.directiveStart++,s.directiveEnd++,i&&(s.providerIndexes+=1048576),n.push(F),a.push(F)}else{let F=np(n[i?M:y],d,!i&&r);dl(o,e,y>-1?y:M,F)}!i&&r&&_&&n[M].componentProviders++}}}function dl(e,t,n,r){let i=_n(t),o=hh(t);if(i||o){let c=(o?xe(t.useClass):t).prototype.ngOnDestroy;if(c){let d=e.destroyHooks||(e.destroyHooks=[]);if(!i&&t.multi){let l=d.indexOf(n);l===-1?d.push(n,[r,c]):d[l+1].push(r,c)}else d.push(n,c)}}}function np(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function ll(e,t,n,r){for(let i=n;i<r;i++)if(t[i]===e)return i;return-1}function k4(e,t,n,r){return Ol(this.multi,[])}function v4(e,t,n,r){let i=this.multi,o;if(this.providerFactory){let a=this.providerFactory.componentProviders,s=Wa(n,n[j],this.providerFactory.index,r);o=s.slice(0,a),Ol(i,o);for(let c=a;c<s.length;c++)o.push(s[c])}else o=[],Ol(i,o);return o}function Ol(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function M4(e,t,n,r,i){let o=new $n(e,n,x);return o.multi=[],o.index=t,o.componentProviders=0,np(o,i,r&&!n),o}function sn(e,t=[]){return n=>{n.providersResolver=(r,i)=>g4(r,i?i(e):e,t)}}function rp(e,t){let n=e[t];return n===et?void 0:n}function w4(e,t,n,r,i,o){let a=t+n;return yt(e,a,i)?T0(e,a+1,o?r.call(o,i):r(i)):rp(e,a+1)}function C4(e,t,n,r,i,o,a){let s=t+n;return Qk(e,s,i,o)?T0(e,s+2,a?r.call(a,i,o):r(i,o)):rp(e,s+2)}function ne(e,t){let n=de(),r,i=e+ye;n.firstCreatePass?(r=x4(t,n.pipeRegistry),n.data[i]=r,r.onDestroy&&(n.destroyHooks??=[]).push(i,r.onDestroy)):r=n.data[i];let o=r.factory||(r.factory=Yt(r.type,!0)),a,s=Fe(x);try{let c=za(!1),d=o();return za(c),Ad(n,q(),i,d),d}finally{Fe(s)}}function x4(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function ce(e,t,n){let r=e+ye,i=q(),o=Ld(i,r);return ip(i,r)?w4(i,zd(),t,o.transform,n,o):o.transform(n)}function d1(e,t,n,r){let i=e+ye,o=q(),a=Ld(o,i);return ip(o,i)?C4(o,zd(),t,a.transform,n,r,a):a.transform(n,r)}function ip(e,t){return e[j].data[t].pure}var I4=(()=>{class e{zone=L(le);changeDetectionScheduler=L(en);applicationRef=L(Oi);applicationErrorHandler=L(ht);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(n){this.applicationErrorHandler(n)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function op({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new le(G(A({},ap()),{scheduleInRootZone:n})),[{provide:le,useFactory:e},{provide:Tn,multi:!0,useFactory:()=>{let r=L(I4,{optional:!0});return()=>r.initialize()}},{provide:Tn,multi:!0,useFactory:()=>{let r=L(b4);return()=>{r.initialize()}}},t===!0?{provide:Kd,useValue:!0}:[],{provide:Yd,useValue:n??V0},{provide:ht,useFactory:()=>{let r=L(le),i=L(Be),o;return a=>{r.runOutsideAngular(()=>{i.destroyed&&!o?setTimeout(()=>{throw a}):(o??=i.get(Ke),o.handleError(a))})}}}]}function ap(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var b4=(()=>{class e{subscription=new X;initialized=!1;zone=L(le);pendingTasks=L(zn);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{le.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{le.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var sp=(()=>{class e{applicationErrorHandler=L(ht);appRef=L(Oi);taskService=L(zn);ngZone=L(le);zonelessEnabled=L(Ra);tracing=L(Ni,{optional:!0});disableScheduling=L(Kd,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new X;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Xa):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(L(Yd,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Ka||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let i=this.useMicrotaskScheduler?c2:j0;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>i(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>i(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Xa+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(n),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,c2(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function D4(){return typeof $localize<"u"&&$localize.locale||Vi}var Hi=new E("",{providedIn:"root",factory:()=>L(Hi,{optional:!0,skipSelf:!0})||D4()});function rt(e){return Qu(e)}function Ar(e,t){return Lo(e,t?.equal)}var cp=class{[je];constructor(t){this[je]=t}destroy(){this[je].destroy()}};var O4=new E("");O4.__NG_ELEMENT_ID__=e=>{let t=qe();if(t===null)throw new S(204,!1);if(t.type&2)return t.value;if(e&8)return null;throw new S(204,!1)};var l1=new E(""),V4=new E("");function Bi(e){return!e.moduleRef}function j4(e){let t=Bi(e)?e.r3Injector:e.moduleRef.injector,n=t.get(le);return n.run(()=>{Bi(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(ht),i;if(n.runOutsideAngular(()=>{i=n.onError.subscribe({next:r})}),Bi(e)){let o=()=>t.destroy(),a=e.platformInjector.get(l1);a.add(o),t.onDestroy(()=>{i.unsubscribe(),a.delete(o)})}else{let o=()=>e.moduleRef.destroy(),a=e.platformInjector.get(l1);a.add(o),e.moduleRef.onDestroy(()=>{xi(e.allPlatformModules,e.moduleRef),i.unsubscribe(),a.delete(o)})}return B4(r,n,()=>{let o=t.get(a1);return o.runInitializers(),o.donePromise.then(()=>{let a=t.get(Hi,Vi);if($0(a||Vi),!t.get(V4,!0))return Bi(e)?t.get(Oi):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Bi(e)){let c=t.get(Oi);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return H4?.(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}var H4;function B4(e,t,n){try{let r=n();return Kn(r)?r.catch(i=>{throw t.runOutsideAngular(()=>e(i)),i}):r}catch(r){throw t.runOutsideAngular(()=>e(r)),r}}var ks=null;function q4(e=[],t){return Jt.create({name:t,providers:[{provide:li,useValue:"platform"},{provide:l1,useValue:new Set([()=>ks=null])},...e]})}function z4(e=[]){if(ks)return ks;let t=q4(e);return ks=t,U0(),U4(t),t}function U4(e){let t=e.get(rs,null);mr(e,()=>{t?.forEach(n=>n())})}var ue=(()=>{class e{static __NG_ELEMENT_ID__=W4}return e})();function W4(e){return $4(qe(),q(),(e&16)===16)}function $4(e,t,n){if(rn(e)&&!n){let r=Je(e.index,t);return new an(r,r)}else if(e.type&175){let r=t[be];return new an(r,t)}return null}var u1=class{constructor(){}supports(t){return t1(t)}create(t){return new h1(t)}},G4=(e,t)=>t,h1=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||G4}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,i=0,o=null;for(;n||r;){let a=!r||n&&n.currentIndex<dp(r,i,o)?n:r,s=dp(a,i,o),c=a.currentIndex;if(a===r)i--,r=r._nextRemoved;else if(n=n._next,a.previousIndex==null)i++;else{o||(o=[]);let d=s-i,l=c-i;if(d!=l){for(let g=0;g<d;g++){let y=g<o.length?o[g]:o[g]=0,M=y+g;l<=M&&M<d&&(o[g]=y+1)}let f=a.previousIndex;o[f]=l-d}}s!==c&&t(a,s,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!t1(t))throw new S(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,i,o,a;if(Array.isArray(t)){this.length=t.length;for(let s=0;s<this.length;s++)o=t[s],a=this._trackByFn(s,o),n===null||!Object.is(n.trackById,a)?(n=this._mismatch(n,o,a,s),r=!0):(r&&(n=this._verifyReinsertion(n,o,a,s)),Object.is(n.item,o)||this._addIdentityChange(n,o)),n=n._next}else i=0,L0(t,s=>{a=this._trackByFn(i,s),n===null||!Object.is(n.trackById,a)?(n=this._mismatch(n,s,a,i),r=!0):(r&&(n=this._verifyReinsertion(n,s,a,i)),Object.is(n.item,s)||this._addIdentityChange(n,s)),n=n._next,i++}),this.length=i;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,i){let o;return t===null?o=this._itTail:(o=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,o,i)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,i),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,o,i)):t=this._addAfter(new p1(n,r),o,i)),t}_verifyReinsertion(t,n,r,i){let o=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return o!==null?t=this._reinsertAfter(o,t._prev,i):t.currentIndex!=i&&(t.currentIndex=i,this._addToMoves(t,i)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let i=t._prevRemoved,o=t._nextRemoved;return i===null?this._removalsHead=o:i._nextRemoved=o,o===null?this._removalsTail=i:o._prevRemoved=i,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let i=n===null?this._itHead:n._next;return t._next=i,t._prev=n,i===null?this._itTail=t:i._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new vs),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new vs),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},p1=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},f1=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},vs=class{map=new Map;put(t){let n=t.trackById,r=this.map.get(n);r||(r=new f1,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,i=this.map.get(r);return i?i.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function dp(e,t,n){let r=e.previousIndex;if(r===null)return r;let i=0;return n&&r<n.length&&(i=n[r]),r+t+i}function lp(){return new y1([new u1])}var y1=(()=>{class e{factories;static \u0275prov=P({token:e,providedIn:"root",factory:lp});constructor(n){this.factories=n}static create(n,r){if(r!=null){let i=r.factories.slice();n=n.concat(i)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||lp()),deps:[[e,new g2,new es]]}}find(n){let r=this.factories.find(i=>i.supports(n));if(r!=null)return r;throw new S(901,!1)}}return e})();function up(e){Y(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,i=z4(r),o=[op({}),{provide:en,useExisting:sp},Hh,...n||[]],a=new Ei({providers:o,parent:i,debugName:"",runEnvironmentInitializers:!1});return j4({r3Injector:a.injector,platformInjector:i,rootComponent:t})}catch(t){return Promise.reject(t)}finally{Y(9)}}function m1(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}var hp=null;function cn(){return hp}function g1(e){hp??=e}var qi=class{};var Le=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(Le||{}),K=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(K||{}),Ue=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(Ue||{}),Ut={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function mp(e){return Xe(e)[se.LocaleId]}function gp(e,t,n){let r=Xe(e),i=[r[se.DayPeriodsFormat],r[se.DayPeriodsStandalone]],o=it(i,t);return it(o,n)}function kp(e,t,n){let r=Xe(e),i=[r[se.DaysFormat],r[se.DaysStandalone]],o=it(i,t);return it(o,n)}function vp(e,t,n){let r=Xe(e),i=[r[se.MonthsFormat],r[se.MonthsStandalone]],o=it(i,t);return it(o,n)}function Mp(e,t){let r=Xe(e)[se.Eras];return it(r,t)}function zi(e,t){let n=Xe(e);return it(n[se.DateFormat],t)}function Ui(e,t){let n=Xe(e);return it(n[se.TimeFormat],t)}function Wi(e,t){let r=Xe(e)[se.DateTimeFormat];return it(r,t)}function $i(e,t){let n=Xe(e),r=n[se.NumberSymbols][t];if(typeof r>"u"){if(t===Ut.CurrencyDecimal)return n[se.NumberSymbols][Ut.Decimal];if(t===Ut.CurrencyGroup)return n[se.NumberSymbols][Ut.Group]}return r}function wp(e){if(!e[se.ExtraData])throw new S(2303,!1)}function Cp(e){let t=Xe(e);return wp(t),(t[se.ExtraData][2]||[]).map(r=>typeof r=="string"?k1(r):[k1(r[0]),k1(r[1])])}function xp(e,t,n){let r=Xe(e);wp(r);let i=[r[se.ExtraData][0],r[se.ExtraData][1]],o=it(i,t)||[];return it(o,n)||[]}function it(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new S(2304,!1)}function k1(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}var Z4=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Ms={},X4=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function Ip(e,t,n,r){let i=i5(e);t=zt(n,t)||t;let a=[],s;for(;t;)if(s=X4.exec(t),s){a=a.concat(s.slice(1));let l=a.pop();if(!l)break;t=l}else{a.push(t);break}let c=i.getTimezoneOffset();r&&(c=Dp(r,c),i=r5(i,r));let d="";return a.forEach(l=>{let f=t5(l);d+=f?f(i,n,c):l==="''"?"'":l.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),d}function bs(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function zt(e,t){let n=mp(e);if(Ms[n]??={},Ms[n][t])return Ms[n][t];let r="";switch(t){case"shortDate":r=zi(e,Ue.Short);break;case"mediumDate":r=zi(e,Ue.Medium);break;case"longDate":r=zi(e,Ue.Long);break;case"fullDate":r=zi(e,Ue.Full);break;case"shortTime":r=Ui(e,Ue.Short);break;case"mediumTime":r=Ui(e,Ue.Medium);break;case"longTime":r=Ui(e,Ue.Long);break;case"fullTime":r=Ui(e,Ue.Full);break;case"short":let i=zt(e,"shortTime"),o=zt(e,"shortDate");r=ws(Wi(e,Ue.Short),[i,o]);break;case"medium":let a=zt(e,"mediumTime"),s=zt(e,"mediumDate");r=ws(Wi(e,Ue.Medium),[a,s]);break;case"long":let c=zt(e,"longTime"),d=zt(e,"longDate");r=ws(Wi(e,Ue.Long),[c,d]);break;case"full":let l=zt(e,"fullTime"),f=zt(e,"fullDate");r=ws(Wi(e,Ue.Full),[l,f]);break}return r&&(Ms[n][t]=r),r}function ws(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function kt(e,t,n="-",r,i){let o="";(e<0||i&&e<=0)&&(i?e=-e+1:(e=-e,o=n));let a=String(e);for(;a.length<t;)a="0"+a;return r&&(a=a.slice(a.length-t)),o+a}function K4(e,t){return kt(e,3).substring(0,t)}function pe(e,t,n=0,r=!1,i=!1){return function(o,a){let s=Y4(e,o);if((n>0||s>-n)&&(s+=n),e===3)s===0&&n===-12&&(s=12);else if(e===6)return K4(s,t);let c=$i(a,Ut.MinusSign);return kt(s,t,c,r,i)}}function Y4(e,t){switch(e){case 0:return t.getFullYear();case 1:return t.getMonth();case 2:return t.getDate();case 3:return t.getHours();case 4:return t.getMinutes();case 5:return t.getSeconds();case 6:return t.getMilliseconds();case 7:return t.getDay();default:throw new S(2301,!1)}}function ee(e,t,n=Le.Format,r=!1){return function(i,o){return Q4(i,o,e,t,n,r)}}function Q4(e,t,n,r,i,o){switch(n){case 2:return vp(t,i,r)[e.getMonth()];case 1:return kp(t,i,r)[e.getDay()];case 0:let a=e.getHours(),s=e.getMinutes();if(o){let d=Cp(t),l=xp(t,i,r),f=d.findIndex(g=>{if(Array.isArray(g)){let[y,M]=g,C=a>=y.hours&&s>=y.minutes,_=a<M.hours||a===M.hours&&s<M.minutes;if(y.hours<M.hours){if(C&&_)return!0}else if(C||_)return!0}else if(g.hours===a&&g.minutes===s)return!0;return!1});if(f!==-1)return l[f]}return gp(t,i,r)[a<12?0:1];case 3:return Mp(t,r)[e.getFullYear()<=0?0:1];default:let c=n;throw new S(2302,!1)}}function Cs(e){return function(t,n,r){let i=-1*r,o=$i(n,Ut.MinusSign),a=i>0?Math.floor(i/60):Math.ceil(i/60);switch(e){case 0:return(i>=0?"+":"")+kt(a,2,o)+kt(Math.abs(i%60),2,o);case 1:return"GMT"+(i>=0?"+":"")+kt(a,1,o);case 2:return"GMT"+(i>=0?"+":"")+kt(a,2,o)+":"+kt(Math.abs(i%60),2,o);case 3:return r===0?"Z":(i>=0?"+":"")+kt(a,2,o)+":"+kt(Math.abs(i%60),2,o);default:throw new S(2302,!1)}}}var J4=0,Is=4;function e5(e){let t=bs(e,J4,1).getDay();return bs(e,0,1+(t<=Is?Is:Is+7)-t)}function bp(e){let t=e.getDay(),n=t===0?-3:Is-t;return bs(e.getFullYear(),e.getMonth(),e.getDate()+n)}function v1(e,t=!1){return function(n,r){let i;if(t){let o=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,a=n.getDate();i=1+Math.floor((a+o)/7)}else{let o=bp(n),a=e5(o.getFullYear()),s=o.getTime()-a.getTime();i=1+Math.round(s/6048e5)}return kt(i,e,$i(r,Ut.MinusSign))}}function xs(e,t=!1){return function(n,r){let o=bp(n).getFullYear();return kt(o,e,$i(r,Ut.MinusSign),t)}}var M1={};function t5(e){if(M1[e])return M1[e];let t;switch(e){case"G":case"GG":case"GGG":t=ee(3,K.Abbreviated);break;case"GGGG":t=ee(3,K.Wide);break;case"GGGGG":t=ee(3,K.Narrow);break;case"y":t=pe(0,1,0,!1,!0);break;case"yy":t=pe(0,2,0,!0,!0);break;case"yyy":t=pe(0,3,0,!1,!0);break;case"yyyy":t=pe(0,4,0,!1,!0);break;case"Y":t=xs(1);break;case"YY":t=xs(2,!0);break;case"YYY":t=xs(3);break;case"YYYY":t=xs(4);break;case"M":case"L":t=pe(1,1,1);break;case"MM":case"LL":t=pe(1,2,1);break;case"MMM":t=ee(2,K.Abbreviated);break;case"MMMM":t=ee(2,K.Wide);break;case"MMMMM":t=ee(2,K.Narrow);break;case"LLL":t=ee(2,K.Abbreviated,Le.Standalone);break;case"LLLL":t=ee(2,K.Wide,Le.Standalone);break;case"LLLLL":t=ee(2,K.Narrow,Le.Standalone);break;case"w":t=v1(1);break;case"ww":t=v1(2);break;case"W":t=v1(1,!0);break;case"d":t=pe(2,1);break;case"dd":t=pe(2,2);break;case"c":case"cc":t=pe(7,1);break;case"ccc":t=ee(1,K.Abbreviated,Le.Standalone);break;case"cccc":t=ee(1,K.Wide,Le.Standalone);break;case"ccccc":t=ee(1,K.Narrow,Le.Standalone);break;case"cccccc":t=ee(1,K.Short,Le.Standalone);break;case"E":case"EE":case"EEE":t=ee(1,K.Abbreviated);break;case"EEEE":t=ee(1,K.Wide);break;case"EEEEE":t=ee(1,K.Narrow);break;case"EEEEEE":t=ee(1,K.Short);break;case"a":case"aa":case"aaa":t=ee(0,K.Abbreviated);break;case"aaaa":t=ee(0,K.Wide);break;case"aaaaa":t=ee(0,K.Narrow);break;case"b":case"bb":case"bbb":t=ee(0,K.Abbreviated,Le.Standalone,!0);break;case"bbbb":t=ee(0,K.Wide,Le.Standalone,!0);break;case"bbbbb":t=ee(0,K.Narrow,Le.Standalone,!0);break;case"B":case"BB":case"BBB":t=ee(0,K.Abbreviated,Le.Format,!0);break;case"BBBB":t=ee(0,K.Wide,Le.Format,!0);break;case"BBBBB":t=ee(0,K.Narrow,Le.Format,!0);break;case"h":t=pe(3,1,-12);break;case"hh":t=pe(3,2,-12);break;case"H":t=pe(3,1);break;case"HH":t=pe(3,2);break;case"m":t=pe(4,1);break;case"mm":t=pe(4,2);break;case"s":t=pe(5,1);break;case"ss":t=pe(5,2);break;case"S":t=pe(6,1);break;case"SS":t=pe(6,2);break;case"SSS":t=pe(6,3);break;case"Z":case"ZZ":case"ZZZ":t=Cs(0);break;case"ZZZZZ":t=Cs(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=Cs(1);break;case"OOOO":case"ZZZZ":case"zzzz":t=Cs(2);break;default:return null}return M1[e]=t,t}function Dp(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function n5(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function r5(e,t,n){let i=e.getTimezoneOffset(),o=Dp(t,i);return n5(e,-1*(o-i))}function i5(e){if(pp(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[i,o=1,a=1]=e.split("-").map(s=>+s);return bs(i,o-1,a)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(Z4))return o5(r)}let t=new Date(e);if(!pp(t))throw new S(2302,!1);return t}function o5(e){let t=new Date(0),n=0,r=0,i=e[8]?t.setUTCFullYear:t.setFullYear,o=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),i.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let a=Number(e[4]||0)-n,s=Number(e[5]||0)-r,c=Number(e[6]||0),d=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return o.call(t,a,s,c,d),t}function pp(e){return e instanceof Date&&!isNaN(e.valueOf())}var Ds=class{$implicit;ngForOf;index;count;constructor(t,n,r,i){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=i}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Tr=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,r,i){this._viewContainer=n,this._template=r,this._differs=i}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((i,o,a)=>{if(i.previousIndex==null)r.createEmbeddedView(this._template,new Ds(i.item,this._ngForOf,-1,-1),a===null?void 0:a);else if(a==null)r.remove(o===null?void 0:o);else if(o!==null){let s=r.get(o);r.move(s,a),fp(s,i)}});for(let i=0,o=r.length;i<o;i++){let s=r.get(i).context;s.index=i,s.count=o,s.ngForOf=this._ngForOf}n.forEachIdentityChange(i=>{let o=r.get(i.currentIndex);fp(o,i)})}static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(x(Fi),x(Pi),x(y1))};static \u0275dir=ge({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function fp(e,t){e.context.$implicit=t.item}var Gi=(()=>{class e{_viewContainer;_context=new Ss;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){yp(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){yp(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(x(Fi),x(Pi))};static \u0275dir=ge({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),Ss=class{$implicit=null;ngIf=null};function yp(e,t){if(e&&!e.createEmbeddedView)throw new S(2020,!1)}function Sp(e,t){return new S(2100,!1)}var w1=class{createSubscription(t,n,r){return rt(()=>t.subscribe({next:n,error:r}))}dispose(t){rt(()=>t.unsubscribe())}},C1=class{createSubscription(t,n,r){return t.then(i=>n?.(i),i=>r?.(i)),{unsubscribe:()=>{n=null,r=null}}}dispose(t){t.unsubscribe()}},a5=new C1,s5=new w1,vt=(()=>{class e{_ref;_latestValue=null;markForCheckOnValueUpdate=!0;_subscription=null;_obj=null;_strategy=null;applicationErrorHandler=L(ht);constructor(n){this._ref=n}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(n){if(!this._obj){if(n)try{this.markForCheckOnValueUpdate=!1,this._subscribe(n)}finally{this.markForCheckOnValueUpdate=!0}return this._latestValue}return n!==this._obj?(this._dispose(),this.transform(n)):this._latestValue}_subscribe(n){this._obj=n,this._strategy=this._selectStrategy(n),this._subscription=this._strategy.createSubscription(n,r=>this._updateLatestValue(n,r),r=>this.applicationErrorHandler(r))}_selectStrategy(n){if(Kn(n))return a5;if(ps(n))return s5;throw Sp(e,n)}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(n,r){n===this._obj&&(this._latestValue=r,this.markForCheckOnValueUpdate&&this._ref?.markForCheck())}static \u0275fac=function(r){return new(r||e)(x(ue,16))};static \u0275pipe=hs({name:"async",type:e,pure:!1})}return e})();var c5="mediumDate",_p=new E(""),Ep=new E(""),x1=(()=>{class e{locale;defaultTimezone;defaultOptions;constructor(n,r,i){this.locale=n,this.defaultTimezone=r,this.defaultOptions=i}transform(n,r,i,o){if(n==null||n===""||n!==n)return null;try{let a=r??this.defaultOptions?.dateFormat??c5,s=i??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return Ip(n,a,o||this.locale,s)}catch(a){throw Sp(e,a.message)}}static \u0275fac=function(r){return new(r||e)(x(Hi,16),x(_p,24),x(Ep,24))};static \u0275pipe=hs({name:"date",type:e,pure:!0})}return e})();var we=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Dt({type:e});static \u0275inj=ct({})}return e})();function Zi(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[i,o]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(i.trim()===t)return decodeURIComponent(o)}return null}var Qn=class{};var Lp="browser",d5="server";function Ap(e){return e===d5}var Ls=new E(""),S1=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(i=>{i.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,i,o){return this._findPluginFor(r).addEventListener(n,r,i,o)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(o=>o.supports(n)),!r)throw new S(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(R(Ls),R(le))};static \u0275prov=P({token:e,factory:e.\u0275fac})}return e})(),Xi=class{_doc;constructor(t){this._doc=t}manager},_s="ng-app-id";function Tp(e){for(let t of e)t.remove()}function Rp(e,t){let n=t.createElement("style");return n.textContent=e,n}function l5(e,t,n,r){let i=e.head?.querySelectorAll(`style[${_s}="${t}"],link[${_s}="${t}"]`);if(i)for(let o of i)o.removeAttribute(_s),o instanceof HTMLLinkElement?r.set(o.href.slice(o.href.lastIndexOf("/")+1),{usage:0,elements:[o]}):o.textContent&&n.set(o.textContent,{usage:0,elements:[o]})}function b1(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var _1=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,i,o={}){this.doc=n,this.appId=r,this.nonce=i,this.isServer=Ap(o),l5(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let i of n)this.addUsage(i,this.inline,Rp);r?.forEach(i=>this.addUsage(i,this.external,b1))}removeStyles(n,r){for(let i of n)this.removeUsage(i,this.inline);r?.forEach(i=>this.removeUsage(i,this.external))}addUsage(n,r,i){let o=r.get(n);o?o.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(a=>this.addElement(a,i(n,this.doc)))})}removeUsage(n,r){let i=r.get(n);i&&(i.usage--,i.usage<=0&&(Tp(i.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])Tp(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:i}]of this.inline)i.push(this.addElement(n,Rp(r,this.doc)));for(let[r,{elements:i}]of this.external)i.push(this.addElement(n,b1(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(_s,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(R(Ze),R(ns),R(is,8),R(_r))};static \u0275prov=P({token:e,factory:e.\u0275fac})}return e})(),I1={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},E1=/%COMP%/g;var Fp="%COMP%",u5=`_nghost-${Fp}`,h5=`_ngcontent-${Fp}`,p5=!0,f5=new E("",{providedIn:"root",factory:()=>p5});function y5(e){return h5.replace(E1,e)}function m5(e){return u5.replace(E1,e)}function Np(e,t){return t.map(n=>n.replace(E1,e))}var L1=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,i,o,a,s,c,d=null,l=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=i,this.removeStylesOnCompDestroy=o,this.doc=a,this.platformId=s,this.ngZone=c,this.nonce=d,this.tracingService=l,this.platformIsServer=!1,this.defaultRenderer=new Ki(n,a,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;let i=this.getOrCreateRenderer(n,r);return i instanceof Es?i.applyToHost(n):i instanceof Yi&&i.applyStyles(),i}getOrCreateRenderer(n,r){let i=this.rendererByCompId,o=i.get(r.id);if(!o){let a=this.doc,s=this.ngZone,c=this.eventManager,d=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,f=this.platformIsServer,g=this.tracingService;switch(r.encapsulation){case Bt.Emulated:o=new Es(c,d,r,this.appId,l,a,s,f,g);break;case Bt.ShadowDom:return new D1(c,d,n,r,a,s,this.nonce,f,g);default:o=new Yi(c,d,r,l,a,s,f,g);break}i.set(r.id,o)}return o}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(R(S1),R(_1),R(ns),R(f5),R(Ze),R(_r),R(le),R(is),R(Ni,8))};static \u0275prov=P({token:e,factory:e.\u0275fac})}return e})(),Ki=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,i,o){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=i,this.tracingService=o}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(I1[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(Pp(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(Pp(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new S(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,i){if(i){n=i+":"+n;let o=I1[i];o?t.setAttributeNS(o,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let i=I1[r];i?t.removeAttributeNS(i,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,i){i&(It.DashCase|It.Important)?t.style.setProperty(n,r,i&It.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&It.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,i){if(typeof t=="string"&&(t=cn().getGlobalEventTarget(this.doc,t),!t))throw new S(5102,!1);let o=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(o=this.tracingService.wrapEventListener(t,n,o)),this.eventManager.addEventListener(t,n,o,i)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;t(n)===!1&&n.preventDefault()}}};function Pp(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var D1=class extends Ki{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,i,o,a,s,c,d){super(t,o,a,c,d),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=i.styles;l=Np(i.id,l);for(let g of l){let y=document.createElement("style");s&&y.setAttribute("nonce",s),y.textContent=g,this.shadowRoot.appendChild(y)}let f=i.getExternalStyles?.();if(f)for(let g of f){let y=b1(g,o);s&&y.setAttribute("nonce",s),this.shadowRoot.appendChild(y)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Yi=class extends Ki{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,i,o,a,s,c,d){super(t,o,a,s,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=i;let l=r.styles;this.styles=d?Np(d,l):l,this.styleUrls=r.getExternalStyles?.(d)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Es=class extends Yi{contentAttr;hostAttr;constructor(t,n,r,i,o,a,s,c,d){let l=i+"-"+r.id;super(t,n,r,o,a,s,c,d,l),this.contentAttr=y5(l),this.hostAttr=m5(l)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var As=class e extends qi{supportsDOMEvents=!0;static makeCurrent(){g1(new e)}onAndCancel(t,n,r,i){return t.addEventListener(n,r,i),()=>{t.removeEventListener(n,r,i)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=g5();return n==null?null:k5(n)}resetBaseElement(){Qi=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return Zi(document.cookie,t)}},Qi=null;function g5(){return Qi=Qi||document.head.querySelector("base"),Qi?Qi.getAttribute("href"):null}function k5(e){return new URL(e,document.baseURI).pathname}var v5=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=P({token:e,factory:e.\u0275fac})}return e})(),Vp=(()=>{class e extends Xi{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,i,o){return n.addEventListener(r,i,o),()=>this.removeEventListener(n,r,i,o)}removeEventListener(n,r,i,o){return n.removeEventListener(r,i,o)}static \u0275fac=function(r){return new(r||e)(R(Ze))};static \u0275prov=P({token:e,factory:e.\u0275fac})}return e})(),Op=["alt","control","meta","shift"],M5={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},w5={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},jp=(()=>{class e extends Xi{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,i,o){let a=e.parseEventName(r),s=e.eventCallback(a.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>cn().onAndCancel(n,a.domEventName,s,o))}static parseEventName(n){let r=n.toLowerCase().split("."),i=r.shift();if(r.length===0||!(i==="keydown"||i==="keyup"))return null;let o=e._normalizeKey(r.pop()),a="",s=r.indexOf("code");if(s>-1&&(r.splice(s,1),a="code."),Op.forEach(d=>{let l=r.indexOf(d);l>-1&&(r.splice(l,1),a+=d+".")}),a+=o,r.length!=0||o.length===0)return null;let c={};return c.domEventName=i,c.fullKey=a,c}static matchEventFullKeyCode(n,r){let i=M5[n.key]||n.key,o="";return r.indexOf("code.")>-1&&(i=n.code,o="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),Op.forEach(a=>{if(a!==i){let s=w5[a];s(n)&&(o+=a+".")}}),o+=i,o===r)}static eventCallback(n,r,i){return o=>{e.matchEventFullKeyCode(o,n)&&i.runGuarded(()=>r(o))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(R(Ze))};static \u0275prov=P({token:e,factory:e.\u0275fac})}return e})();function A1(e,t){return up(A({rootComponent:e},C5(t)))}function C5(e){return{appProviders:[...S5,...e?.providers??[]],platformProviders:D5}}function x5(){As.makeCurrent()}function I5(){return new Ke}function b5(){return Hl(document),document}var D5=[{provide:_r,useValue:Lp},{provide:rs,useValue:x5,multi:!0},{provide:Ze,useFactory:b5}];var S5=[{provide:li,useValue:"root"},{provide:Ke,useFactory:I5},{provide:Ls,useClass:Vp,multi:!0,deps:[Ze]},{provide:Ls,useClass:jp,multi:!0,deps:[Ze]},L1,_1,S1,{provide:Gn,useExisting:L1},{provide:Qn,useClass:v5},[]];var Pr=class{},eo=class{},er=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let i=n.slice(0,r),o=n.slice(r+1).trim();this.addHeaderEntry(i,o)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let i=(t.op==="a"?this.headers.get(n):void 0)||[];i.push(...r),this.headers.set(n,i);break;case"d":let o=t.value;if(!o)this.headers.delete(n),this.normalizedNames.delete(n);else{let a=this.headers.get(n);if(!a)return;a=a.filter(s=>o.indexOf(s)===-1),a.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,a)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(o=>o.toString()),i=t.toLowerCase();this.headers.set(i,r),this.maybeSetNormalizedName(t,i)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var Rs=class{encodeKey(t){return Hp(t)}encodeValue(t){return Hp(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function _5(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(i=>{let o=i.indexOf("="),[a,s]=o==-1?[t.decodeKey(i),""]:[t.decodeKey(i.slice(0,o)),t.decodeValue(i.slice(o+1))],c=n.get(a)||[];c.push(s),n.set(a,c)}),n}var E5=/%(\d[a-f0-9])/gi,L5={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Hp(e){return encodeURIComponent(e).replace(E5,(t,n)=>L5[n]??t)}function Ts(e){return`${e}`}var dn=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new Rs,t.fromString){if(t.fromObject)throw new S(2805,!1);this.map=_5(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],i=Array.isArray(r)?r.map(Ts):[Ts(r)];this.map.set(n,i)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let i=t[r];Array.isArray(i)?i.forEach(o=>{n.push({param:r,value:o,op:"a"})}):n.push({param:r,value:i,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(Ts(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],i=r.indexOf(Ts(t.value));i!==-1&&r.splice(i,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var Ps=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function A5(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Bp(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function qp(e){return typeof Blob<"u"&&e instanceof Blob}function zp(e){return typeof FormData<"u"&&e instanceof FormData}function T5(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Up="Content-Type",Wp="Accept",$p="X-Request-URL",Gp="text/plain",Zp="application/json",R5=`${Zp}, ${Gp}, */*`,Ji=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;keepalive=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(t,n,r,i){this.url=n,this.method=t.toUpperCase();let o;if(A5(this.method)||i?(this.body=r!==void 0?r:null,o=i):o=r,o&&(this.reportProgress=!!o.reportProgress,this.withCredentials=!!o.withCredentials,this.keepalive=!!o.keepalive,o.responseType&&(this.responseType=o.responseType),o.headers&&(this.headers=o.headers),o.context&&(this.context=o.context),o.params&&(this.params=o.params),this.transferCache=o.transferCache),this.headers??=new er,this.context??=new Ps,!this.params)this.params=new dn,this.urlWithParams=n;else{let a=this.params.toString();if(a.length===0)this.urlWithParams=n;else{let s=n.indexOf("?"),c=s===-1?"?":s<n.length-1?"&":"";this.urlWithParams=n+c+a}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Bp(this.body)||qp(this.body)||zp(this.body)||T5(this.body)?this.body:this.body instanceof dn?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||zp(this.body)?null:qp(this.body)?this.body.type||null:Bp(this.body)?null:typeof this.body=="string"?Gp:this.body instanceof dn?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?Zp:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,i=t.responseType||this.responseType,o=t.keepalive??this.keepalive,a=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,c=t.withCredentials??this.withCredentials,d=t.reportProgress??this.reportProgress,l=t.headers||this.headers,f=t.params||this.params,g=t.context??this.context;return t.setHeaders!==void 0&&(l=Object.keys(t.setHeaders).reduce((y,M)=>y.set(M,t.setHeaders[M]),l)),t.setParams&&(f=Object.keys(t.setParams).reduce((y,M)=>y.set(M,t.setParams[M]),f)),new e(n,r,s,{params:f,headers:l,context:g,reportProgress:d,responseType:i,withCredentials:c,transferCache:a,keepalive:o})}},Rr=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Rr||{}),Fr=class{headers;status;statusText;url;ok;type;constructor(t,n=200,r="OK"){this.headers=t.headers||new er,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Fs=class e extends Fr{constructor(t={}){super(t)}type=Rr.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Ns=class e extends Fr{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=Rr.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Os=class extends Fr{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},P5=200,F5=204;function T1(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache,keepalive:e.keepalive}}var N5=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,i={}){let o;if(n instanceof Ji)o=n;else{let c;i.headers instanceof er?c=i.headers:c=new er(i.headers);let d;i.params&&(i.params instanceof dn?d=i.params:d=new dn({fromObject:i.params})),o=new Ji(n,r,i.body!==void 0?i.body:null,{headers:c,context:i.context,params:d,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials,transferCache:i.transferCache,keepalive:i.keepalive})}let a=na(o).pipe(Gc(c=>this.handler.handle(c)));if(n instanceof Ji||i.observe==="events")return a;let s=a.pipe($c(c=>c instanceof Ns));switch(i.observe||"body"){case"body":switch(o.responseType){case"arraybuffer":return s.pipe(Ce(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new S(2806,!1);return c.body}));case"blob":return s.pipe(Ce(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new S(2807,!1);return c.body}));case"text":return s.pipe(Ce(c=>{if(c.body!==null&&typeof c.body!="string")throw new S(2808,!1);return c.body}));case"json":default:return s.pipe(Ce(c=>c.body))}case"response":return s;default:throw new S(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new dn().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,i={}){return this.request("PATCH",n,T1(i,r))}post(n,r,i={}){return this.request("POST",n,T1(i,r))}put(n,r,i={}){return this.request("PUT",n,T1(i,r))}static \u0275fac=function(r){return new(r||e)(R(Pr))};static \u0275prov=P({token:e,factory:e.\u0275fac})}return e})();var O5=new E("");function V5(e,t){return t(e)}function j5(e,t,n){return(r,i)=>mr(n,()=>t(r,o=>e(o,i)))}var P1=new E(""),Xp=new E(""),H5=new E(""),Kp=new E("",{providedIn:"root",factory:()=>!0});var Vs=(()=>{class e extends Pr{backend;injector;chain=null;pendingTasks=L(Pa);contributeToStability=L(Kp);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(Xp),...this.injector.get(H5,[])]));this.chain=r.reduceRight((i,o)=>j5(i,o,this.injector),V5)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,i=>this.backend.handle(i)).pipe(Zc(r))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(R(eo),R(Be))};static \u0275prov=P({token:e,factory:e.\u0275fac})}return e})();var B5=/^\)\]\}',?\n/,q5=RegExp(`^${$p}:`,"m");function z5(e){return"responseURL"in e&&e.responseURL?e.responseURL:q5.test(e.getAllResponseHeaders())?e.getResponseHeader($p):null}var R1=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new S(-2800,!1);n.keepalive;let r=this.xhrFactory;return na(null).pipe(Kc(()=>new Z(o=>{let a=r.build();if(a.open(n.method,n.urlWithParams),n.withCredentials&&(a.withCredentials=!0),n.headers.forEach((C,_)=>a.setRequestHeader(C,_.join(","))),n.headers.has(Wp)||a.setRequestHeader(Wp,R5),!n.headers.has(Up)){let C=n.detectContentTypeHeader();C!==null&&a.setRequestHeader(Up,C)}if(n.responseType){let C=n.responseType.toLowerCase();a.responseType=C!=="json"?C:"text"}let s=n.serializeBody(),c=null,d=()=>{if(c!==null)return c;let C=a.statusText||"OK",_=new er(a.getAllResponseHeaders()),F=z5(a)||n.url;return c=new Fs({headers:_,status:a.status,statusText:C,url:F}),c},l=()=>{let{headers:C,status:_,statusText:F,url:Xt}=d(),Ve=null;_!==F5&&(Ve=typeof a.response>"u"?a.responseText:a.response),_===0&&(_=Ve?P5:0);let Dc=_>=200&&_<300;if(n.responseType==="json"&&typeof Ve=="string"){let Ry=Ve;Ve=Ve.replace(B5,"");try{Ve=Ve!==""?JSON.parse(Ve):null}catch(Py){Ve=Ry,Dc&&(Dc=!1,Ve={error:Py,text:Ve})}}Dc?(o.next(new Ns({body:Ve,headers:C,status:_,statusText:F,url:Xt||void 0})),o.complete()):o.error(new Os({error:Ve,headers:C,status:_,statusText:F,url:Xt||void 0}))},f=C=>{let{url:_}=d(),F=new Os({error:C,status:a.status||0,statusText:a.statusText||"Unknown Error",url:_||void 0});o.error(F)},g=!1,y=C=>{g||(o.next(d()),g=!0);let _={type:Rr.DownloadProgress,loaded:C.loaded};C.lengthComputable&&(_.total=C.total),n.responseType==="text"&&a.responseText&&(_.partialText=a.responseText),o.next(_)},M=C=>{let _={type:Rr.UploadProgress,loaded:C.loaded};C.lengthComputable&&(_.total=C.total),o.next(_)};return a.addEventListener("load",l),a.addEventListener("error",f),a.addEventListener("timeout",f),a.addEventListener("abort",f),n.reportProgress&&(a.addEventListener("progress",y),s!==null&&a.upload&&a.upload.addEventListener("progress",M)),a.send(s),o.next({type:Rr.Sent}),()=>{a.removeEventListener("error",f),a.removeEventListener("abort",f),a.removeEventListener("load",l),a.removeEventListener("timeout",f),n.reportProgress&&(a.removeEventListener("progress",y),s!==null&&a.upload&&a.upload.removeEventListener("progress",M)),a.readyState!==a.DONE&&a.abort()}})))}static \u0275fac=function(r){return new(r||e)(R(Qn))};static \u0275prov=P({token:e,factory:e.\u0275fac})}return e})(),Yp=new E(""),U5="XSRF-TOKEN",W5=new E("",{providedIn:"root",factory:()=>U5}),$5="X-XSRF-TOKEN",G5=new E("",{providedIn:"root",factory:()=>$5}),to=class{},Z5=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r){this.doc=n,this.cookieName=r}getToken(){let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=Zi(n,this.cookieName),this.lastCookieString=n),this.lastToken}static \u0275fac=function(r){return new(r||e)(R(Ze),R(W5))};static \u0275prov=P({token:e,factory:e.\u0275fac})}return e})();function X5(e,t){let n=e.url.toLowerCase();if(!L(Yp)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=L(to).getToken(),i=L(G5);return r!=null&&!e.headers.has(i)&&(e=e.clone({headers:e.headers.set(i,r)})),t(e)}function F1(...e){let t=[N5,R1,Vs,{provide:Pr,useExisting:Vs},{provide:eo,useFactory:()=>L(O5,{optional:!0})??L(R1)},{provide:Xp,useValue:X5,multi:!0},{provide:Yp,useValue:!0},{provide:to,useClass:Z5}];for(let n of e)t.push(...n.\u0275providers);return wa(t)}var N=function(e){return e.INFO="info",e.WARNING="warning",e.ERROR="error",e.CRITICAL="critical",e}(N||{}),We=(()=>{let t=class t{constructor(){this.errorsSubject=new J([]),this.errors$=this.errorsSubject.asObservable(),this.errorCounter=0}handleApiError(r,i){console.error("API Error:",r,"Context:",i);let o;this.isErrorResponse(r)?o=this.createNotificationFromApiError(r.error,i):r&&typeof r=="object"&&"error"in r&&r.error&&typeof r.error=="object"&&"message"in r.error?o=this.createNotification(N.ERROR,"API Error",String(r.error.message),i):r&&typeof r=="object"&&"message"in r?o=this.createNotification(N.ERROR,"Application Error",String(r.message),i):typeof r=="string"?o=this.createNotification(N.ERROR,"Error",r,i):o=this.createNotification(N.ERROR,"Unknown Error","An unexpected error occurred",i||JSON.stringify(r)),this.addError(o)}handleValidationError(r,i){let o=this.createNotification(N.WARNING,"Validation Error",`${r}: ${i}`,"form_validation");this.addError(o)}handleNetworkError(){let r=this.createNotification(N.ERROR,"Network Error","Unable to connect to the server. Please check your connection.","network");this.addError(r)}handleTimeoutError(){let r=this.createNotification(N.WARNING,"Request Timeout","The request took too long to complete. Please try again.","timeout");this.addError(r)}showSuccess(r,i=3e3){let o=this.createNotification(N.INFO,"Success",r);o.autoHide=!0,o.duration=i,this.addError(o)}showInfo(r,i=5e3){let o=this.createNotification(N.INFO,"Information",r);o.autoHide=!0,o.duration=i,this.addError(o)}showWarning(r,i=7e3){let o=this.createNotification(N.WARNING,"Warning",r);o.autoHide=!0,o.duration=i,this.addError(o)}dismissError(r){let o=this.errorsSubject.value.map(a=>a.id===r?G(A({},a),{dismissed:!0}):a);this.errorsSubject.next(o)}clearAllErrors(){this.errorsSubject.next([])}getActiveErrors(){return this.errorsSubject.value.filter(r=>!r.dismissed)}isErrorResponse(r){return r!==null&&typeof r=="object"&&r!==void 0&&"error"in r&&typeof r.error=="object"&&r.error!==null&&"code"in r.error&&"message"in r.error&&typeof r.error.code=="string"&&typeof r.error.message=="string"}createNotificationFromApiError(r,i){let o=this.mapErrorCodeToSeverity(r.code);return{id:this.generateErrorId(),severity:o,title:this.getErrorTitle(r.code),message:r.message,details:r.details||i,timestamp:new Date,dismissed:!1,autoHide:o!==N.CRITICAL,duration:this.getErrorDuration(o)}}createNotification(r,i,o,a){return{id:this.generateErrorId(),severity:r,title:i,message:o,details:a,timestamp:new Date,dismissed:!1,autoHide:r!==N.CRITICAL,duration:this.getErrorDuration(r)}}addError(r){let i=this.errorsSubject.value;this.errorsSubject.next([...i,r])}mapErrorCodeToSeverity(r){switch(r){case"VALIDATION_ERROR":case"INVALID_INPUT":case"MISSING_FIELD":return N.WARNING;case"NOT_FOUND_ERROR":case"TIMEOUT_ERROR":return N.WARNING;case"AUTHENTICATION_ERROR":case"AUTHORIZATION_ERROR":case"NETWORK_ERROR":return N.ERROR;case"INTERNAL_ERROR":case"DATABASE_ERROR":case"FILESYSTEM_ERROR":return N.CRITICAL;default:return N.ERROR}}getErrorTitle(r){switch(r){case"VALIDATION_ERROR":case"INVALID_INPUT":case"MISSING_FIELD":return"Validation Error";case"AUTHENTICATION_ERROR":return"Authentication Error";case"AUTHORIZATION_ERROR":return"Access Denied";case"NOT_FOUND_ERROR":return"Not Found";case"NETWORK_ERROR":return"Network Error";case"TIMEOUT_ERROR":return"Request Timeout";case"RCLONE_ERROR":return"Sync Error";case"FILESYSTEM_ERROR":return"File System Error";case"INTERNAL_ERROR":return"System Error";default:return"Error"}}getErrorDuration(r){switch(r){case N.INFO:return 3e3;case N.WARNING:return 5e3;case N.ERROR:return 7e3;case N.CRITICAL:return 0;default:return 5e3}}generateErrorId(){return`error_${++this.errorCounter}_${Date.now()}`}};t.\u0275fac=function(i){return new(i||t)},t.\u0275prov=P({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();var Qp=(()=>{let t=class t{constructor(r,i){this.errorService=r,this.zone=i}handleError(r){console.error("Global error caught:",r),this.zone.run(()=>{this.isChunkLoadError(r)?this.handleChunkLoadError():this.isNetworkError(r)?this.errorService.handleNetworkError():this.isTimeoutError(r)?this.errorService.handleTimeoutError():this.errorService.handleApiError(r,"global_error_handler")})}isChunkLoadError(r){let i=r&&typeof r=="object"&&"message"in r?String(r.message):"";return i.includes("Loading chunk")||i.includes("Loading CSS chunk")}isNetworkError(r){let i=r&&typeof r=="object"&&"message"in r?String(r.message):"";return i.includes("Network Error")||i.includes("ERR_NETWORK")||i.includes("ERR_INTERNET_DISCONNECTED")}isTimeoutError(r){let i=r&&typeof r=="object"&&"message"in r?String(r.message):"";return i.includes("timeout")||i.includes("Timeout")}handleChunkLoadError(){this.errorService.showWarning("Application update detected. Please refresh the page.",1e4)}};t.\u0275fac=function(i){return new(i||t)(R(We),R(le))},t.\u0275prov=P({token:t,factory:t.\u0275fac});let e=t;return e})();var Jp=(()=>{let t=class t{constructor(r){this.errorService=r}intercept(r,i){return i.handle(r).pipe(Xc({count:2,delay:(o,a)=>{if(this.shouldRetry(o))return console.log(`Retrying request (attempt ${a+1}):`,r.url),new Promise(s=>setTimeout(s,Math.pow(2,a)*1e3));throw o}}),sa(o=>(this.handleHttpError(o,r),Uc(()=>o))))}shouldRetry(r){return r.status===0||r.status>=500&&r.status<600||r.status===408||r.status===504}handleHttpError(r,i){let o=`${i.method} ${i.url}`;switch(r.status){case 0:this.errorService.handleNetworkError();break;case 400:this.errorService.handleApiError(r,o);break;case 401:this.errorService.handleApiError({error:{code:"AUTHENTICATION_ERROR",message:"Authentication required. Please log in.",timestamp:new Date().toISOString(),trace_id:this.generateTraceId()}},o);break;case 403:this.errorService.handleApiError({error:{code:"AUTHORIZATION_ERROR",message:"You do not have permission to perform this action.",timestamp:new Date().toISOString(),trace_id:this.generateTraceId()}},o);break;case 404:this.errorService.handleApiError({error:{code:"NOT_FOUND_ERROR",message:"The requested resource was not found.",timestamp:new Date().toISOString(),trace_id:this.generateTraceId()}},o);break;case 408:case 504:this.errorService.handleTimeoutError();break;case 409:this.errorService.handleApiError({error:{code:"CONFLICT_ERROR",message:"The request conflicts with the current state of the resource.",timestamp:new Date().toISOString(),trace_id:this.generateTraceId()}},o);break;case 422:this.errorService.handleApiError(r,o);break;case 429:this.errorService.handleApiError({error:{code:"RATE_LIMIT_ERROR",message:"Too many requests. Please wait before trying again.",timestamp:new Date().toISOString(),trace_id:this.generateTraceId()}},o);break;case 500:case 502:case 503:this.errorService.handleApiError({error:{code:"INTERNAL_ERROR",message:"A server error occurred. Please try again later.",details:r.message,timestamp:new Date().toISOString(),trace_id:this.generateTraceId()}},o);break;default:this.errorService.handleApiError(r,o);break}}generateTraceId(){return`client_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}};t.\u0275fac=function(i){return new(i||t)(R(We))},t.\u0275prov=P({token:t,factory:t.\u0275fac});let e=t;return e})();var ef={providers:[F1(),{provide:P1,useClass:Jp,multi:!0},{provide:Ke,useClass:Qp}]};var K5="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";function js(e=21){let t="",n=e|0;for(;n--;)t+=K5[Math.random()*64|0];return t}var Y5=window.location.origin+"/wails/runtime",Wt=Object.freeze({Call:0,Clipboard:1,Application:2,Events:3,ContextMenu:4,Dialog:5,Window:6,Screens:7,System:8,Browser:9,CancelCall:10}),Q5=js();function $t(e,t=""){return function(n,r=null){return J5(e,n,t,r)}}function J5(e,t,n,r){return O(this,null,function*(){var i,o;let a=new URL(Y5);a.searchParams.append("object",e.toString()),a.searchParams.append("method",t.toString()),r&&a.searchParams.append("args",JSON.stringify(r));let s={"x-wails-client-id":Q5};n&&(s["x-wails-window-name"]=n);let c=yield fetch(a,{headers:s});if(!c.ok)throw new Error(yield c.text());return((o=(i=c.headers.get("Content-Type"))===null||i===void 0?void 0:i.indexOf("application/json"))!==null&&o!==void 0?o:-1)!==-1?c.json():c.text()})}var QT=$t(Wt.System);var N1=function(){var e,t,n,r,i;try{if(!((t=(e=window.chrome)===null||e===void 0?void 0:e.webview)===null||t===void 0)&&t.postMessage)return window.chrome.webview.postMessage.bind(window.chrome.webview);if(!((i=(r=(n=window.webkit)===null||n===void 0?void 0:n.messageHandlers)===null||r===void 0?void 0:r.external)===null||i===void 0)&&i.postMessage)return window.webkit.messageHandlers.external.postMessage.bind(window.webkit.messageHandlers.external)}catch{}return console.warn(`
%c\u26A0\uFE0F Browser Environment Detected %c

%cOnly UI previews are available in the browser. For full functionality, please run the application in desktop mode.
More information at: https://v3.wails.io/learn/build/#using-a-browser-for-development
`,"background: #ffffff; color: #000000; font-weight: bold; padding: 4px 8px; border-radius: 4px; border: 2px solid #000000;","background: transparent;","color: #ffffff; font-style: italic; font-weight: bold;"),null}();function Nr(e){N1?.(e)}function O1(){return window._wails.environment.OS==="windows"}function tf(){return!!window._wails.environment.Debug}function nf(){return new MouseEvent("mousedown").buttons===0}function Hs(e){var t;return e.target instanceof HTMLElement?e.target:!(e.target instanceof HTMLElement)&&e.target instanceof Node&&(t=e.target.parentElement)!==null&&t!==void 0?t:document.body}var t3=!1;document.addEventListener("DOMContentLoaded",()=>{t3=!0});window.addEventListener("contextmenu",o3);var n3=$t(Wt.ContextMenu),r3=0;function i3(e,t,n,r){n3(r3,{id:e,x:t,y:n,data:r})}function o3(e){let t=Hs(e),n=window.getComputedStyle(t).getPropertyValue("--custom-contextmenu").trim();if(n){e.preventDefault();let r=window.getComputedStyle(t).getPropertyValue("--custom-contextmenu-data");i3(n,e.clientX,e.clientY,r)}else a3(e,t)}function a3(e,t){if(tf())return;switch(window.getComputedStyle(t).getPropertyValue("--default-contextmenu").trim()){case"show":return;case"hide":e.preventDefault();return}if(t.isContentEditable)return;let n=window.getSelection(),r=n&&n.toString().length>0;if(r)for(let i=0;i<n.rangeCount;i++){let a=n.getRangeAt(i).getClientRects();for(let s=0;s<a.length;s++){let c=a[s];if(document.elementFromPoint(c.left,c.top)===t)return}}(t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement)&&(r||!t.readOnly&&!t.disabled)||e.preventDefault()}function Bs(e){try{return window._wails.flags[e]}catch(t){throw new Error("Unable to retrieve flag '"+e+"': "+t,{cause:t})}}var no=!1,ro=!1,B1=!1,Vr=!1,jr=!1,nr="",rf="auto",Or=0,V1=nf();window._wails=window._wails||{};window._wails.setResizable=e=>{B1=e,B1||(Vr=jr=!1,ot())};window.addEventListener("mousedown",q1,{capture:!0});window.addEventListener("mousemove",q1,{capture:!0});window.addEventListener("mouseup",q1,{capture:!0});for(let e of["click","contextmenu","dblclick"])window.addEventListener(e,s3,{capture:!0});function s3(e){(ro||jr)&&(e.stopImmediatePropagation(),e.stopPropagation(),e.preventDefault())}var j1=0,c3=1,H1=2;function q1(e){let t,n=e.buttons;switch(e.type){case"mousedown":t=j1,V1||(n=Or|1<<e.button);break;case"mouseup":t=c3,V1||(n=Or&~(1<<e.button));break;default:t=H1,V1||(n=Or);break}let r=Or&~n,i=n&~Or;Or=n,t===j1&&!(i&e.button)&&(r|=1<<e.button,i|=1<<e.button),(t!==H1&&jr||ro&&(t===j1||e.button!==0))&&(e.stopImmediatePropagation(),e.stopPropagation(),e.preventDefault()),r&1&&l3(e),i&1&&d3(e),t===H1&&h3(e)}function d3(e){if(no=!1,Vr=!1,!O1()&&e.type==="mousedown"&&e.button===0&&e.detail!==1)return;if(nr){Vr=!0;return}let t=Hs(e),n=window.getComputedStyle(t);no=n.getPropertyValue("--wails-draggable").trim()==="drag"&&e.offsetX-parseFloat(n.paddingLeft)<t.clientWidth&&e.offsetY-parseFloat(n.paddingTop)<t.clientHeight}function l3(e){no=!1,ro=!1,Vr=!1,jr=!1}var u3=Object.freeze({"se-resize":"nwse-resize","sw-resize":"nesw-resize","nw-resize":"nwse-resize","ne-resize":"nesw-resize","w-resize":"ew-resize","n-resize":"ns-resize","s-resize":"ns-resize","e-resize":"ew-resize"});function ot(e){e?(nr||(rf=document.body.style.cursor),document.body.style.cursor=u3[e]):!e&&nr&&(document.body.style.cursor=rf),nr=e||""}function h3(e){if(Vr&&nr?(jr=!0,Nr("wails:resize:"+nr)):no&&(ro=!0,Nr("wails:drag")),ro||jr){no=Vr=!1;return}if(!B1||!O1()){nr&&ot();return}let t=Bs("system.resizeHandleHeight")||5,n=Bs("system.resizeHandleWidth")||5,r=Bs("resizeCornerExtra")||10,i=window.outerWidth-e.clientX<n,o=e.clientX<n,a=e.clientY<t,s=window.outerHeight-e.clientY<t,c=window.outerWidth-e.clientX<n+r,d=e.clientX<n+r,l=e.clientY<t+r,f=window.outerHeight-e.clientY<t+r;!d&&!l&&!f&&!c?ot():c&&f?ot("se-resize"):d&&f?ot("sw-resize"):d&&l?ot("nw-resize"):l&&c?ot("ne-resize"):o?ot("w-resize"):a?ot("n-resize"):s?ot("s-resize"):i?ot("e-resize"):ot()}var Ae={};Sc(Ae,{ByID:()=>P3,ByName:()=>R3,Call:()=>Z1,RuntimeError:()=>$s});var af=Function.prototype.toString,Hr=typeof Reflect=="object"&&Reflect!==null&&Reflect.apply,z1,qs;if(typeof Hr=="function"&&typeof Object.defineProperty=="function")try{z1=Object.defineProperty({},"length",{get:function(){throw qs}}),qs={},Hr(function(){throw 42},null,z1)}catch(e){e!==qs&&(Hr=null)}else Hr=null;var p3=/^\s*class\b/,W1=function(t){try{var n=af.call(t);return p3.test(n)}catch{return!1}},U1=function(t){try{return W1(t)?!1:(af.call(t),!0)}catch{return!1}},zs=Object.prototype.toString,f3="[object Object]",y3="[object Function]",m3="[object GeneratorFunction]",g3="[object HTMLAllCollection]",k3="[object HTML document.all class]",v3="[object HTMLCollection]",M3=typeof Symbol=="function"&&!!Symbol.toStringTag,w3=!(0 in[,]),$1=function(){return!1};typeof document=="object"&&(of=document.all,zs.call(of)===zs.call(document.all)&&($1=function(t){if((w3||!t)&&(typeof t>"u"||typeof t=="object"))try{var n=zs.call(t);return(n===g3||n===k3||n===v3||n===f3)&&t("")==null}catch{}return!1}));var of;function C3(e){if($1(e))return!0;if(!e||typeof e!="function"&&typeof e!="object")return!1;try{Hr(e,null,z1)}catch(t){if(t!==qs)return!1}return!W1(e)&&U1(e)}function x3(e){if($1(e))return!0;if(!e||typeof e!="function"&&typeof e!="object")return!1;if(M3)return U1(e);if(W1(e))return!1;var t=zs.call(e);return t!==y3&&t!==m3&&!/^\[object HTML/.test(t)?!1:U1(e)}var ln=Hr?C3:x3;var G1,io=class extends Error{constructor(t,n){super(t,n),this.name="CancelError"}},rr=class extends Error{constructor(t,n,r){super((r??"Unhandled rejection in cancelled promise.")+" Reason: "+I3(n),{cause:n}),this.promise=t,this.name="CancelledRejectionError"}},Gt=Symbol("barrier"),sf=Symbol("cancelImpl"),cf=(G1=Symbol.species)!==null&&G1!==void 0?G1:Symbol("speciesPolyfill"),Ws=class e extends Promise{constructor(t,n){let r,i;if(super((c,d)=>{r=c,i=d}),this.constructor[cf]!==Promise)throw new TypeError("CancellablePromise does not support transparent subclassing. Please refrain from overriding the [Symbol.species] static property.");let o={promise:this,resolve:r,reject:i,get oncancelled(){return n??null},set oncancelled(c){n=c??void 0}},a={get root(){return a},resolving:!1,settled:!1};Object.defineProperties(this,{[Gt]:{configurable:!1,enumerable:!1,writable:!0,value:null},[sf]:{configurable:!1,enumerable:!1,writable:!1,value:uf(o,a)}});let s=pf(o,a);try{t(hf(o,a),s)}catch(c){a.resolving?console.log("Unhandled exception in CancellablePromise executor.",c):s(c)}}cancel(t){return new e(n=>{Promise.all([this[sf](new io("Promise cancelled.",{cause:t})),b3(this)]).then(()=>n(),()=>n())})}cancelOn(t){return t.aborted?this.cancel(t.reason):t.addEventListener("abort",()=>void this.cancel(t.reason),{capture:!0}),this}then(t,n,r){if(!(this instanceof e))throw new TypeError("CancellablePromise.prototype.then called on an invalid object.");if(ln(t)||(t=df),ln(n)||(n=lf),t===df&&n==lf)return new e(o=>o(this));let i={};return this[Gt]=i,new e((o,a)=>{super.then(s=>{var c;this[Gt]===i&&(this[Gt]=null),(c=i.resolve)===null||c===void 0||c.call(i);try{o(t(s))}catch(d){a(d)}},s=>{var c;this[Gt]===i&&(this[Gt]=null),(c=i.resolve)===null||c===void 0||c.call(i);try{o(n(s))}catch(d){a(d)}})},o=>O(this,null,function*(){try{return r?.(o)}finally{yield this.cancel(o)}}))}catch(t,n){return this.then(void 0,t,n)}finally(t,n){if(!(this instanceof e))throw new TypeError("CancellablePromise.prototype.finally called on an invalid object.");return ln(t)?this.then(r=>e.resolve(t()).then(()=>r),r=>e.resolve(t()).then(()=>{throw r}),n):this.then(t,t,n)}static get[cf](){return Promise}static all(t){let n=Array.from(t),r=n.length===0?e.resolve(n):new e((i,o)=>{Promise.all(n).then(i,o)},i=>Us(r,n,i));return r}static allSettled(t){let n=Array.from(t),r=n.length===0?e.resolve(n):new e((i,o)=>{Promise.allSettled(n).then(i,o)},i=>Us(r,n,i));return r}static any(t){let n=Array.from(t),r=n.length===0?e.resolve(n):new e((i,o)=>{Promise.any(n).then(i,o)},i=>Us(r,n,i));return r}static race(t){let n=Array.from(t),r=new e((i,o)=>{Promise.race(n).then(i,o)},i=>Us(r,n,i));return r}static cancel(t){let n=new e(()=>{});return n.cancel(t),n}static timeout(t,n){let r=new e(()=>{});return AbortSignal&&typeof AbortSignal=="function"&&AbortSignal.timeout&&typeof AbortSignal.timeout=="function"?AbortSignal.timeout(t).addEventListener("abort",()=>void r.cancel(n)):setTimeout(()=>void r.cancel(n),t),r}static sleep(t,n){return new e(r=>{setTimeout(()=>r(n),t)})}static reject(t){return new e((n,r)=>r(t))}static resolve(t){return t instanceof e?t:new e(n=>n(t))}static withResolvers(){let t={oncancelled:null};return t.promise=new e((n,r)=>{t.resolve=n,t.reject=r},n=>{var r;(r=t.oncancelled)===null||r===void 0||r.call(t,n)}),t}};function uf(e,t){let n;return r=>{if(t.settled||(t.settled=!0,t.reason=r,e.reject(r),Promise.prototype.then.call(e.promise,void 0,i=>{if(i!==r)throw i})),!(!t.reason||!e.oncancelled))return n=new Promise(i=>{try{i(e.oncancelled(t.reason.cause))}catch(o){Promise.reject(new rr(e.promise,o,"Unhandled exception in oncancelled callback."))}}).catch(i=>{Promise.reject(new rr(e.promise,i,"Unhandled rejection in oncancelled callback."))}),e.oncancelled=null,n}}function hf(e,t){return n=>{if(!t.resolving){if(t.resolving=!0,n===e.promise){if(t.settled)return;t.settled=!0,e.reject(new TypeError("A promise cannot be resolved with itself."));return}if(n!=null&&(typeof n=="object"||typeof n=="function")){let r;try{r=n.then}catch(i){t.settled=!0,e.reject(i);return}if(ln(r)){try{let a=n.cancel;if(ln(a)){let s=c=>{Reflect.apply(a,n,[c])};t.reason?uf(Object.assign(Object.assign({},e),{oncancelled:s}),t)(t.reason):e.oncancelled=s}}catch{}let i={root:t.root,resolving:!1,get settled(){return this.root.settled},set settled(a){this.root.settled=a},get reason(){return this.root.reason}},o=pf(e,i);try{Reflect.apply(r,n,[hf(e,i),o])}catch(a){o(a)}return}}t.settled||(t.settled=!0,e.resolve(n))}}}function pf(e,t){return n=>{if(!t.resolving)if(t.resolving=!0,t.settled){try{if(n instanceof io&&t.reason instanceof io&&Object.is(n.cause,t.reason.cause))return}catch{}Promise.reject(new rr(e.promise,n))}else t.settled=!0,e.reject(n)}}function Us(e,t,n){let r=[];for(let i of t){let o;try{if(!ln(i.then)||(o=i.cancel,!ln(o)))continue}catch{continue}let a;try{a=Reflect.apply(o,i,[n])}catch(s){Promise.reject(new rr(e,s,"Unhandled exception in cancel method."));continue}a&&r.push((a instanceof Promise?a:Promise.resolve(a)).catch(s=>{Promise.reject(new rr(e,s,"Unhandled rejection in cancel method."))}))}return Promise.all(r)}function df(e){return e}function lf(e){throw e}function I3(e){try{if(e instanceof Error||typeof e!="object"||e.toString!==Object.prototype.toString)return""+e}catch{}try{return JSON.stringify(e)}catch{}try{return Object.prototype.toString.call(e)}catch{}return"<could not convert error to string>"}function b3(e){var t;let n=(t=e[Gt])!==null&&t!==void 0?t:{};return"promise"in n||Object.assign(n,Br()),e[Gt]==null&&(n.resolve(),e[Gt]=n),n.promise}var Br=Promise.withResolvers;Br&&typeof Br=="function"?Br=Br.bind(Promise):Br=function(){let e,t;return{promise:new Promise((r,i)=>{e=r,t=i}),resolve:e,reject:t}};window._wails=window._wails||{};window._wails.callResultHandler=L3;window._wails.callErrorHandler=A3;var D3=$t(Wt.Call),S3=$t(Wt.CancelCall),qr=new Map,_3=0,E3=0,$s=class extends Error{constructor(t,n){super(t,n),this.name="RuntimeError"}};function L3(e,t,n){let r=ff(e);if(r)if(!t)r.resolve(void 0);else if(!n)r.resolve(t);else try{r.resolve(JSON.parse(t))}catch(i){r.reject(new TypeError("could not parse result: "+i.message,{cause:i}))}}function A3(e,t,n){let r=ff(e);if(r)if(!n)r.reject(new Error(t));else{let i;try{i=JSON.parse(t)}catch(s){r.reject(new TypeError("could not parse error: "+s.message,{cause:s}));return}let o={};i.cause&&(o.cause=i.cause);let a;switch(i.kind){case"ReferenceError":a=new ReferenceError(i.message,o);break;case"TypeError":a=new TypeError(i.message,o);break;case"RuntimeError":a=new $s(i.message,o);break;default:a=new Error(i.message,o);break}r.reject(a)}}function ff(e){let t=qr.get(e);return qr.delete(e),t}function T3(){let e;do e=js();while(qr.has(e));return e}function Z1(e){let t=T3(),n=Ws.withResolvers();qr.set(t,{resolve:n.resolve,reject:n.reject});let r=D3(_3,Object.assign({"call-id":t},e)),i=!1;r.then(()=>{i=!0},a=>{qr.delete(t),n.reject(a)});let o=()=>(qr.delete(t),S3(E3,{"call-id":t}).catch(a=>{console.error("Error while requesting binding call cancellation:",a)}));return n.oncancelled=()=>i?o():r.then(o),n.promise}function R3(e,...t){return Z1({methodName:e,args:t})}function P3(e,...t){return Z1({methodID:e,args:t})}var Zt={};Sc(Zt,{Any:()=>ir,Array:()=>N3,ByteSlice:()=>F3,Map:()=>O3,Nullable:()=>V3,Struct:()=>j3});function ir(e){return e}function F3(e){return e??""}function N3(e){return e===ir?t=>t===null?[]:t:t=>{if(t===null)return[];for(let n=0;n<t.length;n++)t[n]=e(t[n]);return t}}function O3(e,t){return t===ir?n=>n===null?{}:n:n=>{if(n===null)return{};for(let r in n)n[r]=t(n[r]);return n}}function V3(e){return e===ir?ir:t=>t===null?null:e(t)}function j3(e){let t=!0;for(let n in e)if(e[n]!==ir){t=!1;break}return t?ir:n=>{for(let r in e)r in n&&(n[r]=e[r](n[r]));return n}}var Xs={};Sc(Xs,{Emit:()=>G3,Off:()=>W3,OffAll:()=>$3,On:()=>z3,OnMultiple:()=>X1,Once:()=>U3,Types:()=>mf,WailsEvent:()=>Zs});var Mt=new Map,Gs=class{constructor(t,n,r){this.eventName=t,this.callback=n,this.maxCallbacks=r||-1}dispatch(t){try{this.callback(t)}catch(n){console.error(n)}return this.maxCallbacks===-1?!1:(this.maxCallbacks-=1,this.maxCallbacks===0)}};function yf(e){let t=Mt.get(e.eventName);t&&(t=t.filter(n=>n!==e),t.length===0?Mt.delete(e.eventName):Mt.set(e.eventName,t))}var mf=Object.freeze({Windows:Object.freeze({APMPowerSettingChange:"windows:APMPowerSettingChange",APMPowerStatusChange:"windows:APMPowerStatusChange",APMResumeAutomatic:"windows:APMResumeAutomatic",APMResumeSuspend:"windows:APMResumeSuspend",APMSuspend:"windows:APMSuspend",ApplicationStarted:"windows:ApplicationStarted",SystemThemeChanged:"windows:SystemThemeChanged",WebViewNavigationCompleted:"windows:WebViewNavigationCompleted",WindowActive:"windows:WindowActive",WindowBackgroundErase:"windows:WindowBackgroundErase",WindowClickActive:"windows:WindowClickActive",WindowClosing:"windows:WindowClosing",WindowDidMove:"windows:WindowDidMove",WindowDidResize:"windows:WindowDidResize",WindowDPIChanged:"windows:WindowDPIChanged",WindowDragDrop:"windows:WindowDragDrop",WindowDragEnter:"windows:WindowDragEnter",WindowDragLeave:"windows:WindowDragLeave",WindowDragOver:"windows:WindowDragOver",WindowEndMove:"windows:WindowEndMove",WindowEndResize:"windows:WindowEndResize",WindowFullscreen:"windows:WindowFullscreen",WindowHide:"windows:WindowHide",WindowInactive:"windows:WindowInactive",WindowKeyDown:"windows:WindowKeyDown",WindowKeyUp:"windows:WindowKeyUp",WindowKillFocus:"windows:WindowKillFocus",WindowNonClientHit:"windows:WindowNonClientHit",WindowNonClientMouseDown:"windows:WindowNonClientMouseDown",WindowNonClientMouseLeave:"windows:WindowNonClientMouseLeave",WindowNonClientMouseMove:"windows:WindowNonClientMouseMove",WindowNonClientMouseUp:"windows:WindowNonClientMouseUp",WindowPaint:"windows:WindowPaint",WindowRestore:"windows:WindowRestore",WindowSetFocus:"windows:WindowSetFocus",WindowShow:"windows:WindowShow",WindowStartMove:"windows:WindowStartMove",WindowStartResize:"windows:WindowStartResize",WindowUnFullscreen:"windows:WindowUnFullscreen",WindowZOrderChanged:"windows:WindowZOrderChanged",WindowMinimise:"windows:WindowMinimise",WindowUnMinimise:"windows:WindowUnMinimise",WindowMaximise:"windows:WindowMaximise",WindowUnMaximise:"windows:WindowUnMaximise"}),Mac:Object.freeze({ApplicationDidBecomeActive:"mac:ApplicationDidBecomeActive",ApplicationDidChangeBackingProperties:"mac:ApplicationDidChangeBackingProperties",ApplicationDidChangeEffectiveAppearance:"mac:ApplicationDidChangeEffectiveAppearance",ApplicationDidChangeIcon:"mac:ApplicationDidChangeIcon",ApplicationDidChangeOcclusionState:"mac:ApplicationDidChangeOcclusionState",ApplicationDidChangeScreenParameters:"mac:ApplicationDidChangeScreenParameters",ApplicationDidChangeStatusBarFrame:"mac:ApplicationDidChangeStatusBarFrame",ApplicationDidChangeStatusBarOrientation:"mac:ApplicationDidChangeStatusBarOrientation",ApplicationDidChangeTheme:"mac:ApplicationDidChangeTheme",ApplicationDidFinishLaunching:"mac:ApplicationDidFinishLaunching",ApplicationDidHide:"mac:ApplicationDidHide",ApplicationDidResignActive:"mac:ApplicationDidResignActive",ApplicationDidUnhide:"mac:ApplicationDidUnhide",ApplicationDidUpdate:"mac:ApplicationDidUpdate",ApplicationShouldHandleReopen:"mac:ApplicationShouldHandleReopen",ApplicationWillBecomeActive:"mac:ApplicationWillBecomeActive",ApplicationWillFinishLaunching:"mac:ApplicationWillFinishLaunching",ApplicationWillHide:"mac:ApplicationWillHide",ApplicationWillResignActive:"mac:ApplicationWillResignActive",ApplicationWillTerminate:"mac:ApplicationWillTerminate",ApplicationWillUnhide:"mac:ApplicationWillUnhide",ApplicationWillUpdate:"mac:ApplicationWillUpdate",MenuDidAddItem:"mac:MenuDidAddItem",MenuDidBeginTracking:"mac:MenuDidBeginTracking",MenuDidClose:"mac:MenuDidClose",MenuDidDisplayItem:"mac:MenuDidDisplayItem",MenuDidEndTracking:"mac:MenuDidEndTracking",MenuDidHighlightItem:"mac:MenuDidHighlightItem",MenuDidOpen:"mac:MenuDidOpen",MenuDidPopUp:"mac:MenuDidPopUp",MenuDidRemoveItem:"mac:MenuDidRemoveItem",MenuDidSendAction:"mac:MenuDidSendAction",MenuDidSendActionToItem:"mac:MenuDidSendActionToItem",MenuDidUpdate:"mac:MenuDidUpdate",MenuWillAddItem:"mac:MenuWillAddItem",MenuWillBeginTracking:"mac:MenuWillBeginTracking",MenuWillDisplayItem:"mac:MenuWillDisplayItem",MenuWillEndTracking:"mac:MenuWillEndTracking",MenuWillHighlightItem:"mac:MenuWillHighlightItem",MenuWillOpen:"mac:MenuWillOpen",MenuWillPopUp:"mac:MenuWillPopUp",MenuWillRemoveItem:"mac:MenuWillRemoveItem",MenuWillSendAction:"mac:MenuWillSendAction",MenuWillSendActionToItem:"mac:MenuWillSendActionToItem",MenuWillUpdate:"mac:MenuWillUpdate",WebViewDidCommitNavigation:"mac:WebViewDidCommitNavigation",WebViewDidFinishNavigation:"mac:WebViewDidFinishNavigation",WebViewDidReceiveServerRedirectForProvisionalNavigation:"mac:WebViewDidReceiveServerRedirectForProvisionalNavigation",WebViewDidStartProvisionalNavigation:"mac:WebViewDidStartProvisionalNavigation",WindowDidBecomeKey:"mac:WindowDidBecomeKey",WindowDidBecomeMain:"mac:WindowDidBecomeMain",WindowDidBeginSheet:"mac:WindowDidBeginSheet",WindowDidChangeAlpha:"mac:WindowDidChangeAlpha",WindowDidChangeBackingLocation:"mac:WindowDidChangeBackingLocation",WindowDidChangeBackingProperties:"mac:WindowDidChangeBackingProperties",WindowDidChangeCollectionBehavior:"mac:WindowDidChangeCollectionBehavior",WindowDidChangeEffectiveAppearance:"mac:WindowDidChangeEffectiveAppearance",WindowDidChangeOcclusionState:"mac:WindowDidChangeOcclusionState",WindowDidChangeOrderingMode:"mac:WindowDidChangeOrderingMode",WindowDidChangeScreen:"mac:WindowDidChangeScreen",WindowDidChangeScreenParameters:"mac:WindowDidChangeScreenParameters",WindowDidChangeScreenProfile:"mac:WindowDidChangeScreenProfile",WindowDidChangeScreenSpace:"mac:WindowDidChangeScreenSpace",WindowDidChangeScreenSpaceProperties:"mac:WindowDidChangeScreenSpaceProperties",WindowDidChangeSharingType:"mac:WindowDidChangeSharingType",WindowDidChangeSpace:"mac:WindowDidChangeSpace",WindowDidChangeSpaceOrderingMode:"mac:WindowDidChangeSpaceOrderingMode",WindowDidChangeTitle:"mac:WindowDidChangeTitle",WindowDidChangeToolbar:"mac:WindowDidChangeToolbar",WindowDidDeminiaturize:"mac:WindowDidDeminiaturize",WindowDidEndSheet:"mac:WindowDidEndSheet",WindowDidEnterFullScreen:"mac:WindowDidEnterFullScreen",WindowDidEnterVersionBrowser:"mac:WindowDidEnterVersionBrowser",WindowDidExitFullScreen:"mac:WindowDidExitFullScreen",WindowDidExitVersionBrowser:"mac:WindowDidExitVersionBrowser",WindowDidExpose:"mac:WindowDidExpose",WindowDidFocus:"mac:WindowDidFocus",WindowDidMiniaturize:"mac:WindowDidMiniaturize",WindowDidMove:"mac:WindowDidMove",WindowDidOrderOffScreen:"mac:WindowDidOrderOffScreen",WindowDidOrderOnScreen:"mac:WindowDidOrderOnScreen",WindowDidResignKey:"mac:WindowDidResignKey",WindowDidResignMain:"mac:WindowDidResignMain",WindowDidResize:"mac:WindowDidResize",WindowDidUpdate:"mac:WindowDidUpdate",WindowDidUpdateAlpha:"mac:WindowDidUpdateAlpha",WindowDidUpdateCollectionBehavior:"mac:WindowDidUpdateCollectionBehavior",WindowDidUpdateCollectionProperties:"mac:WindowDidUpdateCollectionProperties",WindowDidUpdateShadow:"mac:WindowDidUpdateShadow",WindowDidUpdateTitle:"mac:WindowDidUpdateTitle",WindowDidUpdateToolbar:"mac:WindowDidUpdateToolbar",WindowDidZoom:"mac:WindowDidZoom",WindowFileDraggingEntered:"mac:WindowFileDraggingEntered",WindowFileDraggingExited:"mac:WindowFileDraggingExited",WindowFileDraggingPerformed:"mac:WindowFileDraggingPerformed",WindowHide:"mac:WindowHide",WindowMaximise:"mac:WindowMaximise",WindowUnMaximise:"mac:WindowUnMaximise",WindowMinimise:"mac:WindowMinimise",WindowUnMinimise:"mac:WindowUnMinimise",WindowShouldClose:"mac:WindowShouldClose",WindowShow:"mac:WindowShow",WindowWillBecomeKey:"mac:WindowWillBecomeKey",WindowWillBecomeMain:"mac:WindowWillBecomeMain",WindowWillBeginSheet:"mac:WindowWillBeginSheet",WindowWillChangeOrderingMode:"mac:WindowWillChangeOrderingMode",WindowWillClose:"mac:WindowWillClose",WindowWillDeminiaturize:"mac:WindowWillDeminiaturize",WindowWillEnterFullScreen:"mac:WindowWillEnterFullScreen",WindowWillEnterVersionBrowser:"mac:WindowWillEnterVersionBrowser",WindowWillExitFullScreen:"mac:WindowWillExitFullScreen",WindowWillExitVersionBrowser:"mac:WindowWillExitVersionBrowser",WindowWillFocus:"mac:WindowWillFocus",WindowWillMiniaturize:"mac:WindowWillMiniaturize",WindowWillMove:"mac:WindowWillMove",WindowWillOrderOffScreen:"mac:WindowWillOrderOffScreen",WindowWillOrderOnScreen:"mac:WindowWillOrderOnScreen",WindowWillResignMain:"mac:WindowWillResignMain",WindowWillResize:"mac:WindowWillResize",WindowWillUnfocus:"mac:WindowWillUnfocus",WindowWillUpdate:"mac:WindowWillUpdate",WindowWillUpdateAlpha:"mac:WindowWillUpdateAlpha",WindowWillUpdateCollectionBehavior:"mac:WindowWillUpdateCollectionBehavior",WindowWillUpdateCollectionProperties:"mac:WindowWillUpdateCollectionProperties",WindowWillUpdateShadow:"mac:WindowWillUpdateShadow",WindowWillUpdateTitle:"mac:WindowWillUpdateTitle",WindowWillUpdateToolbar:"mac:WindowWillUpdateToolbar",WindowWillUpdateVisibility:"mac:WindowWillUpdateVisibility",WindowWillUseStandardFrame:"mac:WindowWillUseStandardFrame",WindowZoomIn:"mac:WindowZoomIn",WindowZoomOut:"mac:WindowZoomOut",WindowZoomReset:"mac:WindowZoomReset"}),Linux:Object.freeze({ApplicationStartup:"linux:ApplicationStartup",SystemThemeChanged:"linux:SystemThemeChanged",WindowDeleteEvent:"linux:WindowDeleteEvent",WindowDidMove:"linux:WindowDidMove",WindowDidResize:"linux:WindowDidResize",WindowFocusIn:"linux:WindowFocusIn",WindowFocusOut:"linux:WindowFocusOut",WindowLoadChanged:"linux:WindowLoadChanged"}),Common:Object.freeze({ApplicationOpenedWithFile:"common:ApplicationOpenedWithFile",ApplicationStarted:"common:ApplicationStarted",ThemeChanged:"common:ThemeChanged",WindowClosing:"common:WindowClosing",WindowDidMove:"common:WindowDidMove",WindowDidResize:"common:WindowDidResize",WindowDPIChanged:"common:WindowDPIChanged",WindowFilesDropped:"common:WindowFilesDropped",WindowFocus:"common:WindowFocus",WindowFullscreen:"common:WindowFullscreen",WindowHide:"common:WindowHide",WindowLostFocus:"common:WindowLostFocus",WindowMaximise:"common:WindowMaximise",WindowMinimise:"common:WindowMinimise",WindowRestore:"common:WindowRestore",WindowRuntimeReady:"common:WindowRuntimeReady",WindowShow:"common:WindowShow",WindowUnFullscreen:"common:WindowUnFullscreen",WindowUnMaximise:"common:WindowUnMaximise",WindowUnMinimise:"common:WindowUnMinimise",WindowZoom:"common:WindowZoom",WindowZoomIn:"common:WindowZoomIn",WindowZoomOut:"common:WindowZoomOut",WindowZoomReset:"common:WindowZoomReset"})});window._wails=window._wails||{};window._wails.dispatchWailsEvent=q3;var H3=$t(Wt.Events),B3=0,Zs=class{constructor(t,n=null){this.name=t,this.data=n}};function q3(e){let t=Mt.get(e.name);if(!t)return;let n=new Zs(e.name,e.data);"sender"in e&&(n.sender=e.sender),t=t.filter(r=>!r.dispatch(n)),t.length===0?Mt.delete(e.name):Mt.set(e.name,t)}function X1(e,t,n){let r=Mt.get(e)||[],i=new Gs(e,t,n);return r.push(i),Mt.set(e,r),()=>yf(i)}function z3(e,t){return X1(e,t,-1)}function U3(e,t){return X1(e,t,1)}function W3(...e){e.forEach(t=>Mt.delete(t))}function $3(){Mt.clear()}function G3(e){return H3(B3,e)}window._wails=window._wails||{};window._wails.invoke=Nr;Nr("wails:runtime:ready");var oo=class e{constructor(t={}){"DebugMode"in t||(this.DebugMode=!1),"ProfileFilePath"in t||(this.ProfileFilePath=""),"ResyncFilePath"in t||(this.ResyncFilePath=""),"RcloneFilePath"in t||(this.RcloneFilePath=""),Object.assign(this,t)}static createFrom(t={}){let n=typeof t=="string"?JSON.parse(t):t;return new e(n)}};var Te=class e{constructor(t={}){"working_dir"in t||(this.working_dir=""),"selected_profile_index"in t||(this.selected_profile_index=0),"profiles"in t||(this.profiles=[]),"env_config"in t||(this.env_config=new oo),Object.assign(this,t)}static createFrom(t={}){let n=Ks,r=Y3,i=typeof t=="string"?JSON.parse(t):t;return"profiles"in i&&(i.profiles=n(i.profiles)),"env_config"in i&&(i.env_config=r(i.env_config)),new e(i)}},Et=class e{constructor(t={}){"name"in t||(this.name=""),"from"in t||(this.from=""),"to"in t||(this.to=""),"included_paths"in t||(this.included_paths=[]),"excluded_paths"in t||(this.excluded_paths=[]),"bandwidth"in t||(this.bandwidth=0),"parallel"in t||(this.parallel=0),"backup_path"in t||(this.backup_path=""),"cache_path"in t||(this.cache_path=""),Object.assign(this,t)}static createFrom(t={}){let n=gf,r=gf,i=typeof t=="string"?JSON.parse(t):t;return"included_paths"in i&&(i.included_paths=n(i.included_paths)),"excluded_paths"in i&&(i.excluded_paths=r(i.excluded_paths)),new e(i)}},Ks=function e(...t){return Ks===e&&(Ks=K3),Ks(...t)},X3=Et.createFrom,K3=Zt.Array(X3),Y3=oo.createFrom,gf=Zt.Array(Zt.Any);var Ys=class e{constructor(t={}){"message"in t||(this.message=""),Object.assign(this,t)}static createFrom(t={}){let n=typeof t=="string"?JSON.parse(t):t;return new e(n)}};var Qs=class e{constructor(t={}){"name"in t||(this.name=""),"type"in t||(this.type=""),"source"in t||(this.source=""),"description"in t||(this.description=""),Object.assign(this,t)}static createFrom(t={}){let n=typeof t=="string"?JSON.parse(t):t;return new e(n)}};function kf(e,t,n){let r=Ae.ByName("desktop/backend.App.AddRemote",e,t,n),i=r.then(o=>or(o));return i.cancel=r.cancel.bind(r),i}function vf(e){return Ae.ByName("desktop/backend.App.DeleteRemote",e)}function Mf(){let e=Ae.ByName("desktop/backend.App.ExportProfiles"),t=e.then(n=>or(n));return t.cancel=e.cancel.bind(e),t}function wf(){let e=Ae.ByName("desktop/backend.App.ExportRemotes"),t=e.then(n=>or(n));return t.cancel=e.cancel.bind(e),t}function Cf(){let e=Ae.ByName("desktop/backend.App.GetConfigInfo"),t=e.then(n=>tM(n));return t.cancel=e.cancel.bind(e),t}function xf(){let e=Ae.ByName("desktop/backend.App.GetRemotes"),t=e.then(n=>rM(n));return t.cancel=e.cancel.bind(e),t}function If(){let e=Ae.ByName("desktop/backend.App.ImportProfiles"),t=e.then(n=>or(n));return t.cancel=e.cancel.bind(e),t}function bf(){let e=Ae.ByName("desktop/backend.App.ImportRemotes"),t=e.then(n=>or(n));return t.cancel=e.cancel.bind(e),t}function Df(){let e=Ae.ByName("desktop/backend.App.StopAddingRemote"),t=e.then(n=>or(n));return t.cancel=e.cancel.bind(e),t}function Y1(e){return Ae.ByName("desktop/backend.App.StopCommand",e)}function Js(e,t){return Ae.ByName("desktop/backend.App.Sync",e,t)}function ec(e,t,n){return Ae.ByName("desktop/backend.App.SyncWithTabId",e,t,n)}function Sf(e){let t=Ae.ByName("desktop/backend.App.UpdateProfiles",e),n=t.then(r=>or(r));return n.cancel=t.cancel.bind(t),n}var eM=Ys.createFrom,or=Zt.Nullable(eM),tM=Te.createFrom,nM=Qs.createFrom,rM=Zt.Array(nM);var tc={command:"sync_status",status:"running",progress:0,speed:"0 B/s",eta:"--",files_transferred:0,total_files:0,bytes_transferred:0,total_bytes:0,current_file:"",errors:0,checks:0,deletes:0,renames:0,timestamp:new Date().toISOString(),elapsed_time:"0s",action:"pull"};function nc(e){return["running","completed","error","stopped"].includes(e)}function rc(e){return["pull","push","bi","bi-resync"].includes(e)}var ic=(()=>{let t=class t{constructor(){this.tabs$=new J([]),this.activeTabId$=new J(null),console.log("TabService constructor called")}get tabs(){return this.tabs$.asObservable()}get activeTabId(){return this.activeTabId$.asObservable()}get tabsValue(){return this.tabs$.value}get activeTabIdValue(){return this.activeTabId$.value}createTab(r){let i=this.generateTabId(),o=r||`Tab ${this.tabs$.value.length+1}`,a={id:i,name:o,selectedProfileIndex:null,currentAction:void 0,currentTaskId:0,data:[],isActive:!1,isEditing:!1,isStopping:!1,syncStatus:null},c=this.tabs$.value.map(d=>G(A({},d),{isActive:!1}));return a.isActive=!0,c.push(a),this.tabs$.next(c),this.activeTabId$.next(i),i}deleteTab(r){let i=this.tabs$.value,o=i.findIndex(s=>s.id===r);if(o===-1)return;let a=i.filter(s=>s.id!==r);if(this.activeTabId$.value===r)if(a.length>0){let s=o>0?o-1:0;a[s]&&(a[s].isActive=!0,this.activeTabId$.next(a[s].id))}else this.activeTabId$.next(null);this.tabs$.next(a)}setActiveTab(r){let o=this.tabs$.value.map(a=>G(A({},a),{isActive:a.id===r}));this.tabs$.next(o),this.activeTabId$.next(r)}updateTab(r,i){let a=this.tabs$.value.map(s=>s.id===r?A(A({},s),i):s);this.tabs$.next(a)}getTab(r){return this.tabs$.value.find(i=>i.id===r)}getActiveTab(){let r=this.activeTabId$.value;if(r)return this.getTab(r)}startRenameTab(r){this.updateTab(r,{isEditing:!0})}finishRenameTab(r,i){let o=i.trim();o?this.updateTab(r,{name:o,isEditing:!1}):this.updateTab(r,{isEditing:!1})}cancelRenameTab(r){this.updateTab(r,{isEditing:!1})}handleCommandEvent(r){if(!r.tab_id)return;let i=this.getTab(r.tab_id);if(i)switch(r.command){case"command_started":this.updateTab(r.tab_id,{data:["Command started..."],syncStatus:null});break;case"command_stoped":this.updateTab(r.tab_id,{currentAction:void 0,currentTaskId:0,isStopping:!1,syncStatus:null});break;case"command_output":this.updateTab(r.tab_id,{data:[r.error||""]});break;case"error":this.updateTab(r.tab_id,{data:[...i.data,r.error||""]});break;case"sync_status":this.handleTabSyncStatusUpdate(r.tab_id,r);break}}handleTabSyncStatusUpdate(r,i){let o=this.getTab(r);if(!o)return;let a=o.syncStatus||A({},tc),s=G(A(A({},a),i),{status:i.status&&nc(i.status)?i.status:a.status,progress:i.progress??a.progress,speed:i.speed||a.speed,eta:i.eta||a.eta,files_transferred:i.files_transferred??a.files_transferred,total_files:i.total_files??a.total_files,bytes_transferred:i.bytes_transferred??a.bytes_transferred,total_bytes:i.total_bytes??a.total_bytes,current_file:i.current_file||a.current_file,errors:i.errors??a.errors,checks:i.checks??a.checks,deletes:i.deletes??a.deletes,renames:i.renames??a.renames,elapsed_time:i.elapsed_time||a.elapsed_time,action:i.action&&rc(i.action)?i.action:a.action});this.updateTab(r,{syncStatus:s})}generateTabId(){return"tab_"+Math.random().toString(36).substring(2,11)+"_"+Date.now()}};t.\u0275fac=function(i){return new(i||t)},t.\u0275prov=P({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();var W=function(e){return e.Pull="pull",e.Push="push",e.Bi="bi",e.BiResync="bi-resync",e}(W||{}),Lt=(()=>{let t=class t{constructor(r,i){this.tabService=r,this.errorService=i,this.currentId$=new J(0),this.currentAction$=new J(void 0),this.data$=new J([]),this.remotes$=new J([]),this.syncStatus$=new J(null),console.log("AppService constructor called");let o=new Te;o.profiles=[],this.configInfo$=new J(o),console.log("AppService initial configInfo:",o),this.eventCleanup=Xs.On("tofe",a=>{let s=a.data,c=JSON.parse(s);if(c.tab_id){this.tabService.handleCommandEvent(c);return}switch(c.command){case"command_started":this.replaceData("Command started..."),this.syncStatus$.next(null);break;case"command_stoped":this.currentAction$.next(void 0),this.currentId$.next(0),this.syncStatus$.next(null);break;case"command_output":this.replaceData(c.error||"");break;case"error":{let d=[...this.data$.value,c.error||"Unknown error"];this.data$.next(d);break}case"sync_status":this.handleSyncStatusUpdate(c);break}}),console.log("AppService calling getConfigInfo and getRemotes"),this.getConfigInfo(),this.getRemotes()}ngOnDestroy(){console.log("AppService ngOnDestroy called"),this.eventCleanup&&(console.log("AppService cleaning up event listener"),this.eventCleanup(),this.eventCleanup=void 0)}replaceData(r){this.data$.next([r])}handleSyncStatusUpdate(r){let i=this.syncStatus$.value||A({},tc),o=G(A(A({},i),r),{status:r.status&&nc(r.status)?r.status:i.status,progress:r.progress??i.progress,speed:r.speed||i.speed,eta:r.eta||i.eta,files_transferred:r.files_transferred??i.files_transferred,total_files:r.total_files??i.total_files,bytes_transferred:r.bytes_transferred??i.bytes_transferred,total_bytes:r.total_bytes??i.total_bytes,current_file:r.current_file||i.current_file,errors:r.errors??i.errors,checks:r.checks??i.checks,deletes:r.deletes??i.deletes,renames:r.renames??i.renames,elapsed_time:r.elapsed_time||i.elapsed_time,action:r.action&&rc(r.action)?r.action:i.action});this.syncStatus$.next(o)}pull(r){return O(this,null,function*(){this.currentAction$.value!==W.Pull&&(this.replaceData("Pulling..."),this.currentId$.next(yield Js(W.Pull,r)),this.currentId$.value&&this.currentAction$.next(W.Pull))})}pullWithTab(r,i){return O(this,null,function*(){let o=this.tabService.getTab(i);if(!o||o.currentAction===W.Pull)return;this.tabService.updateTab(i,{data:["Pulling..."]});let a=yield ec(W.Pull,r,i);a&&this.tabService.updateTab(i,{currentAction:W.Pull,currentTaskId:a})})}push(r){return O(this,null,function*(){this.currentAction$.value!==W.Push&&(this.replaceData("Pushing..."),this.currentId$.next(yield Js(W.Push,r)),this.currentId$.value&&this.currentAction$.next(W.Push))})}pushWithTab(r,i){return O(this,null,function*(){let o=this.tabService.getTab(i);if(!o||o.currentAction===W.Push)return;this.tabService.updateTab(i,{data:["Pushing..."]});let a=yield ec(W.Push,r,i);a&&this.tabService.updateTab(i,{currentAction:W.Push,currentTaskId:a})})}bi(r,i=!1){return O(this,null,function*(){this.currentAction$.value!==W.Bi&&(this.replaceData("Bi..."),this.currentId$.next(yield Js(i?W.BiResync:W.Bi,r)),this.currentId$.value&&this.currentAction$.next(W.Bi))})}biWithTab(r,i,o=!1){return O(this,null,function*(){let a=this.tabService.getTab(i);if(!a||a.currentAction===W.Bi)return;this.tabService.updateTab(i,{data:["Bi..."]});let s=yield ec(o?W.BiResync:W.Bi,r,i);s&&this.tabService.updateTab(i,{currentAction:W.Bi,currentTaskId:s})})}stopCommand(){this.currentAction$.value&&Y1(this.currentId$.value)}stopCommandForTab(r){let i=this.tabService.getTab(r);!i||!i.currentAction||!i.currentTaskId||Y1(i.currentTaskId)}getConfigInfo(){return O(this,null,function*(){console.log("AppService getConfigInfo called");try{let r=yield Cf();console.log("AppService getConfigInfo received from backend:",r),r.profiles=r.profiles??[],console.log("AppService getConfigInfo after profiles normalization:",r),this.configInfo$.next(r),console.log("AppService getConfigInfo emitted to configInfo$")}catch(r){console.error("AppService getConfigInfo error:",r),this.errorService.handleApiError(r,"get_config_info")}})}getRemotes(){return O(this,null,function*(){try{let r=yield xf();this.remotes$.next(r??[])}catch(r){throw console.error("Error getting remotes:",r),this.errorService.handleApiError(r,"get_remotes"),new Error("Failed to get remotes")}})}addRemote(r){return O(this,null,function*(){if(!r.name||!r.type)throw new Error("Remote name and type are required");try{let i=yield kf(r.name,r.type,{});if(i)throw new Error(i.message);yield this.getRemotes()}catch(i){throw console.error("Error adding remote:",i),this.errorService.handleApiError(i,"add_remote"),i}})}stopAddingRemote(){return O(this,null,function*(){try{let r=yield Df();if(r)throw new Error(r.message)}catch(r){throw console.error("Error stopping add remote:",r),this.errorService.handleApiError(r,"stop_adding_remote"),r}})}deleteRemote(r){return O(this,null,function*(){if(!r)throw new Error("Remote name is required");try{yield vf(r),yield this.getRemotes()}catch(i){throw console.error("Error deleting remote:",i),this.errorService.handleApiError(i,"delete_remote"),new Error("Failed to delete remote")}})}addProfile(){return O(this,null,function*(){let r=new Et;r.name="",r.from="",r.to="",r.included_paths=[],r.excluded_paths=[],r.parallel=16,r.bandwidth=5;let i=this.configInfo$.value,o=new Te;return Object.assign(o,i),o.profiles=[...i.profiles,r],this.configInfo$.next(o),yield this.saveConfigInfo(),o.profiles.length-1})}removeProfile(r){return O(this,null,function*(){let i=this.configInfo$.value,o=new Te;Object.assign(o,i),o.profiles=i.profiles.filter((a,s)=>s!==r),this.configInfo$.next(o),yield this.saveConfigInfo()})}updateProfile(r,i){let o=this.configInfo$.value,a=new Te;Object.assign(a,o),a.profiles=o.profiles.map((s,c)=>{if(c===r){let d=new Et;return Object.assign(d,i),d}return s}),this.configInfo$.next(a)}updateConfigInfo(){let r=this.configInfo$.value,i=new Te;Object.assign(i,r),this.configInfo$.next(i)}saveConfigInfo(){return O(this,null,function*(){try{let r=yield Sf(this.configInfo$.value.profiles);if(r)throw r.message}catch(r){console.error(r),this.errorService.handleApiError(r,"save_config_info")}})}addIncludePath(r){let i=this.configInfo$.value,o=new Te;Object.assign(o,i),o.profiles=i.profiles.map((a,s)=>{if(s===r){let c=new Et;return Object.assign(c,a),c.included_paths=[...a.included_paths,"/**"],c}return a}),this.configInfo$.next(o)}removeIncludePath(r,i){let o=this.configInfo$.value,a=new Te;Object.assign(a,o),a.profiles=o.profiles.map((s,c)=>{if(c===r){let d=new Et;return Object.assign(d,s),d.included_paths=s.included_paths.filter((l,f)=>f!==i),d}return s}),this.configInfo$.next(a)}addExcludePath(r){let i=this.configInfo$.value,o=new Te;Object.assign(o,i),o.profiles=i.profiles.map((a,s)=>{if(s===r){let c=new Et;return Object.assign(c,a),c.excluded_paths=[...a.excluded_paths,"/**"],c}return a}),this.configInfo$.next(o)}removeExcludePath(r,i){let o=this.configInfo$.value,a=new Te;Object.assign(a,o),a.profiles=o.profiles.map((s,c)=>{if(c===r){let d=new Et;return Object.assign(d,s),d.excluded_paths=s.excluded_paths.filter((l,f)=>f!==i),d}return s}),this.configInfo$.next(a)}exportProfiles(){return O(this,null,function*(){try{yield Mf()}catch(r){throw console.error("Error exporting profiles:",r),this.errorService.handleApiError(r,"export_profiles"),new Error("Failed to export profiles")}})}importProfiles(){return O(this,null,function*(){try{yield If(),yield this.getConfigInfo()}catch(r){throw console.error("Error importing profiles:",r),this.errorService.handleApiError(r,"import_profiles"),new Error("Failed to import profiles")}})}exportRemotes(){return O(this,null,function*(){try{yield wf()}catch(r){throw console.error("Error exporting remotes:",r),this.errorService.handleApiError(r,"export_remotes"),new Error("Failed to export remotes")}})}importRemotes(){return O(this,null,function*(){try{yield bf(),yield this.getRemotes()}catch(r){throw console.error("Error importing remotes:",r),this.errorService.handleApiError(r,"import_remotes"),new Error("Failed to import remotes")}})}};t.\u0275fac=function(i){return new(i||t)(R(ic),R(We))},t.\u0275prov=P({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function iM(e){return e!==null&&typeof e=="object"&&e!==void 0&&"name"in e&&"from"in e&&"to"in e&&"included_paths"in e&&"excluded_paths"in e&&"bandwidth"in e&&"parallel"in e&&typeof e.name=="string"&&typeof e.from=="string"&&typeof e.to=="string"&&Array.isArray(e.included_paths)&&Array.isArray(e.excluded_paths)&&typeof e.bandwidth=="number"&&typeof e.parallel=="number"}function Q1(e,t){return t>=0&&t<e.profiles.length&&iM(e.profiles[t])}function J1(e){switch(e){case W.Pull:return{color:"primary",icon:"download",label:"Pulling"};case W.Push:return{color:"accent",icon:"upload",label:"Pushing"};case W.Bi:return{color:"primary",icon:"sync",label:"Syncing"};case W.BiResync:return{color:"warn",icon:"refresh",label:"Resyncing"};default:return{color:"primary",icon:"play_arrow",label:"Running"}}}function eu(e){if(e==null||e==="")return null;let t=parseInt(String(e));return isNaN(t)?null:t}function zr(e,t){return t===null?!1:Q1(e,t)}var Vf=(()=>{class e{_renderer;_elementRef;onChange=n=>{};onTouched=()=>{};constructor(n,r){this._renderer=n,this._elementRef=r}setProperty(n,r){this._renderer.setProperty(this._elementRef.nativeElement,n,r)}registerOnTouched(n){this.onTouched=n}registerOnChange(n){this.onChange=n}setDisabledState(n){this.setProperty("disabled",n)}static \u0275fac=function(r){return new(r||e)(x(qt),x(bt))};static \u0275dir=ge({type:e})}return e})(),iu=(()=>{class e extends Vf{static \u0275fac=(()=>{let n;return function(i){return(n||(n=Sr(e)))(i||e)}})();static \u0275dir=ge({type:e,features:[tt]})}return e})(),hc=new E("");var oM={provide:hc,useExisting:st(()=>hn),multi:!0};function aM(){let e=cn()?cn().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var sM=new E(""),hn=(()=>{class e extends Vf{_compositionMode;_composing=!1;constructor(n,r,i){super(n,r),this._compositionMode=i,this._compositionMode==null&&(this._compositionMode=!aM())}writeValue(n){let r=n??"";this.setProperty("value",r)}_handleInput(n){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(n)}_compositionStart(){this._composing=!0}_compositionEnd(n){this._composing=!1,this._compositionMode&&this.onChange(n)}static \u0275fac=function(r){return new(r||e)(x(qt),x(bt),x(sM,8))};static \u0275dir=ge({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,i){r&1&&I("input",function(a){return i._handleInput(a.target.value)})("blur",function(){return i.onTouched()})("compositionstart",function(){return i._compositionStart()})("compositionend",function(a){return i._compositionEnd(a.target.value)})},standalone:!1,features:[sn([oM]),tt]})}return e})();function cM(e){return e==null||dM(e)===0}function dM(e){return e==null?null:Array.isArray(e)||typeof e=="string"?e.length:e instanceof Set?e.size:null}var ou=new E(""),jf=new E("");function lM(e){return cM(e.value)?{required:!0}:null}function Ef(e){return null}function Hf(e){return e!=null}function Bf(e){return Kn(e)?Kt(e):e}function qf(e){let t={};return e.forEach(n=>{t=n!=null?A(A({},t),n):t}),Object.keys(t).length===0?null:t}function zf(e,t){return t.map(n=>n(e))}function uM(e){return!e.validate}function Uf(e){return e.map(t=>uM(t)?t:n=>t.validate(n))}function hM(e){if(!e)return null;let t=e.filter(Hf);return t.length==0?null:function(n){return qf(zf(n,t))}}function au(e){return e!=null?hM(Uf(e)):null}function pM(e){if(!e)return null;let t=e.filter(Hf);return t.length==0?null:function(n){let r=zf(n,t).map(Bf);return Wc(r).pipe(Ce(qf))}}function su(e){return e!=null?pM(Uf(e)):null}function Lf(e,t){return e===null?[t]:Array.isArray(e)?[...e,t]:[e,t]}function fM(e){return e._rawValidators}function yM(e){return e._rawAsyncValidators}function tu(e){return e?Array.isArray(e)?e:[e]:[]}function ac(e,t){return Array.isArray(e)?e.includes(t):e===t}function Af(e,t){let n=tu(t);return tu(e).forEach(i=>{ac(n,i)||n.push(i)}),n}function Tf(e,t){return tu(t).filter(n=>!ac(e,n))}var sc=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=au(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=su(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,n){return this.control?this.control.hasError(t,n):!1}getError(t,n){return this.control?this.control.getError(t,n):null}},$r=class extends sc{name;get formDirective(){return null}get path(){return null}},po=class extends sc{_parent=null;name=null;valueAccessor=null},cc=class{_cd;constructor(t){this._cd=t}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},mM={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},eP=G(A({},mM),{"[class.ng-submitted]":"isSubmitted"}),Gr=(()=>{class e extends cc{constructor(n){super(n)}static \u0275fac=function(r){return new(r||e)(x(po,2))};static \u0275dir=ge({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,i){r&2&&ji("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)},standalone:!1,features:[tt]})}return e})(),Wf=(()=>{class e extends cc{constructor(n){super(n)}static \u0275fac=function(r){return new(r||e)(x($r,10))};static \u0275dir=ge({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,i){r&2&&ji("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)("ng-submitted",i.isSubmitted)},standalone:!1,features:[tt]})}return e})();var so="VALID",oc="INVALID",Ur="PENDING",co="DISABLED",un=class{},dc=class extends un{value;source;constructor(t,n){super(),this.value=t,this.source=n}},uo=class extends un{pristine;source;constructor(t,n){super(),this.pristine=t,this.source=n}},ho=class extends un{touched;source;constructor(t,n){super(),this.touched=t,this.source=n}},Wr=class extends un{status;source;constructor(t,n){super(),this.status=t,this.source=n}},nu=class extends un{source;constructor(t){super(),this.source=t}},ru=class extends un{source;constructor(t){super(),this.source=t}};function $f(e){return(pc(e)?e.validators:e)||null}function gM(e){return Array.isArray(e)?au(e):e||null}function Gf(e,t){return(pc(t)?t.asyncValidators:e)||null}function kM(e){return Array.isArray(e)?su(e):e||null}function pc(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function vM(e,t,n){let r=e.controls;if(!(t?Object.keys(r):r).length)throw new S(1e3,"");if(!r[n])throw new S(1001,"")}function MM(e,t,n){e._forEachChild((r,i)=>{if(n[i]===void 0)throw new S(1002,"")})}var lc=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(t,n){this._assignValidators(t),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get status(){return rt(this.statusReactive)}set status(t){rt(()=>this.statusReactive.set(t))}_status=Ar(()=>this.statusReactive());statusReactive=qn(void 0);get valid(){return this.status===so}get invalid(){return this.status===oc}get pending(){return this.status==Ur}get disabled(){return this.status===co}get enabled(){return this.status!==co}errors;get pristine(){return rt(this.pristineReactive)}set pristine(t){rt(()=>this.pristineReactive.set(t))}_pristine=Ar(()=>this.pristineReactive());pristineReactive=qn(!0);get dirty(){return!this.pristine}get touched(){return rt(this.touchedReactive)}set touched(t){rt(()=>this.touchedReactive.set(t))}_touched=Ar(()=>this.touchedReactive());touchedReactive=qn(!1);get untouched(){return!this.touched}_events=new Nt;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(Af(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(Af(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(Tf(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(Tf(t,this._rawAsyncValidators))}hasValidator(t){return ac(this._rawValidators,t)}hasAsyncValidator(t){return ac(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){let n=this.touched===!1;this.touched=!0;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsTouched(G(A({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new ho(!0,r))}markAllAsDirty(t={}){this.markAsDirty({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsDirty(t))}markAllAsTouched(t={}){this.markAsTouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsTouched(t))}markAsUntouched(t={}){let n=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=t.sourceControl??this;this._forEachChild(i=>{i.markAsUntouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:r})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,r),n&&t.emitEvent!==!1&&this._events.next(new ho(!1,r))}markAsDirty(t={}){let n=this.pristine===!0;this.pristine=!1;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsDirty(G(A({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new uo(!1,r))}markAsPristine(t={}){let n=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=t.sourceControl??this;this._forEachChild(i=>{i.markAsPristine({onlySelf:!0,emitEvent:t.emitEvent})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t,r),n&&t.emitEvent!==!1&&this._events.next(new uo(!0,r))}markAsPending(t={}){this.status=Ur;let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Wr(this.status,n)),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.markAsPending(G(A({},t),{sourceControl:n}))}disable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=co,this.errors=null,this._forEachChild(i=>{i.disable(G(A({},t),{onlySelf:!0}))}),this._updateValue();let r=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new dc(this.value,r)),this._events.next(new Wr(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(G(A({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(i=>i(!0))}enable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=so,this._forEachChild(r=>{r.enable(G(A({},t),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors(G(A({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(t,n){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine({},n),this._parent._updateTouched({},n))}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===so||this.status===Ur)&&this._runAsyncValidator(r,t.emitEvent)}let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new dc(this.value,n)),this._events.next(new Wr(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(G(A({},t),{sourceControl:n}))}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?co:so}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t,n){if(this.asyncValidator){this.status=Ur,this._hasOwnPendingAsyncValidator={emitEvent:n!==!1,shouldHaveEmitted:t!==!1};let r=Bf(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(i=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(i,{emitEvent:n,shouldHaveEmitted:t})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let t=(this._hasOwnPendingAsyncValidator?.emitEvent||this._hasOwnPendingAsyncValidator?.shouldHaveEmitted)??!1;return this._hasOwnPendingAsyncValidator=null,t}return!1}setErrors(t,n={}){this.errors=t,this._updateControlsErrors(n.emitEvent!==!1,this,n.shouldHaveEmitted)}get(t){let n=t;return n==null||(Array.isArray(n)||(n=n.split(".")),n.length===0)?null:n.reduce((r,i)=>r&&r._find(i),this)}getError(t,n){let r=n?this.get(n):this;return r&&r.errors?r.errors[t]:null}hasError(t,n){return!!this.getError(t,n)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t,n,r){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),(t||r)&&this._events.next(new Wr(this.status,n)),this._parent&&this._parent._updateControlsErrors(t,n,r)}_initObservables(){this.valueChanges=new ze,this.statusChanges=new ze}_calculateStatus(){return this._allControlsDisabled()?co:this.errors?oc:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(Ur)?Ur:this._anyControlsHaveStatus(oc)?oc:so}_anyControlsHaveStatus(t){return this._anyControls(n=>n.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t,n){let r=!this._anyControlsDirty(),i=this.pristine!==r;this.pristine=r,this._parent&&!t.onlySelf&&this._parent._updatePristine(t,n),i&&this._events.next(new uo(this.pristine,n))}_updateTouched(t={},n){this.touched=this._anyControlsTouched(),this._events.next(new ho(this.touched,n)),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,n)}_onDisabledChange=[];_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){pc(t)&&t.updateOn!=null&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){let n=this._parent&&this._parent.dirty;return!t&&!!n&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=gM(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=kM(this._rawAsyncValidators)}},uc=class extends lc{constructor(t,n,r){super($f(n),Gf(r,n)),this.controls=t,this._initObservables(),this._setUpdateStrategy(n),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(t,n){return this.controls[t]?this.controls[t]:(this.controls[t]=n,n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange),n)}addControl(t,n,r={}){this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(t,n={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}setControl(t,n,r={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],n&&this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(t){return this.controls.hasOwnProperty(t)&&this.controls[t].enabled}setValue(t,n={}){MM(this,!0,t),Object.keys(t).forEach(r=>{vM(this,!0,r),this.controls[r].setValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n)}patchValue(t,n={}){t!=null&&(Object.keys(t).forEach(r=>{let i=this.controls[r];i&&i.patchValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n))}reset(t={},n={}){this._forEachChild((r,i)=>{r.reset(t?t[i]:null,{onlySelf:!0,emitEvent:n.emitEvent})}),this._updatePristine(n,this),this._updateTouched(n,this),this.updateValueAndValidity(n)}getRawValue(){return this._reduceChildren({},(t,n,r)=>(t[r]=n.getRawValue(),t))}_syncPendingControls(){let t=this._reduceChildren(!1,(n,r)=>r._syncPendingControls()?!0:n);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){Object.keys(this.controls).forEach(n=>{let r=this.controls[n];r&&t(r,n)})}_setUpControls(){this._forEachChild(t=>{t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(t){for(let[n,r]of Object.entries(this.controls))if(this.contains(n)&&t(r))return!0;return!1}_reduceValue(){let t={};return this._reduceChildren(t,(n,r,i)=>((r.enabled||this.disabled)&&(n[i]=r.value),n))}_reduceChildren(t,n){let r=t;return this._forEachChild((i,o)=>{r=n(r,i,o)}),r}_allControlsDisabled(){for(let t of Object.keys(this.controls))if(this.controls[t].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(t){return this.controls.hasOwnProperty(t)?this.controls[t]:null}};var cu=new E("",{providedIn:"root",factory:()=>du}),du="always";function wM(e,t){return[...t.path,e]}function Zf(e,t,n=du){Xf(e,t),t.valueAccessor.writeValue(e.value),(e.disabled||n==="always")&&t.valueAccessor.setDisabledState?.(e.disabled),xM(e,t),bM(e,t),IM(e,t),CM(e,t)}function Rf(e,t){e.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(t)})}function CM(e,t){if(t.valueAccessor.setDisabledState){let n=r=>{t.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(n),t._registerOnDestroy(()=>{e._unregisterOnDisabledChange(n)})}}function Xf(e,t){let n=fM(e);t.validator!==null?e.setValidators(Lf(n,t.validator)):typeof n=="function"&&e.setValidators([n]);let r=yM(e);t.asyncValidator!==null?e.setAsyncValidators(Lf(r,t.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let i=()=>e.updateValueAndValidity();Rf(t._rawValidators,i),Rf(t._rawAsyncValidators,i)}function xM(e,t){t.valueAccessor.registerOnChange(n=>{e._pendingValue=n,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&Kf(e,t)})}function IM(e,t){t.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&Kf(e,t),e.updateOn!=="submit"&&e.markAsTouched()})}function Kf(e,t){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function bM(e,t){let n=(r,i)=>{t.valueAccessor.writeValue(r),i&&t.viewToModelUpdate(r)};e.registerOnChange(n),t._registerOnDestroy(()=>{e._unregisterOnChange(n)})}function DM(e,t){e==null,Xf(e,t)}function SM(e,t){if(!e.hasOwnProperty("model"))return!1;let n=e.model;return n.isFirstChange()?!0:!Object.is(t,n.currentValue)}function _M(e){return Object.getPrototypeOf(e.constructor)===iu}function EM(e,t){e._syncPendingControls(),t.forEach(n=>{let r=n.control;r.updateOn==="submit"&&r._pendingChange&&(n.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}function LM(e,t){if(!t)return null;Array.isArray(t);let n,r,i;return t.forEach(o=>{o.constructor===hn?n=o:_M(o)?r=o:i=o}),i||r||n||null}var AM={provide:$r,useExisting:st(()=>lu)},lo=Promise.resolve(),lu=(()=>{class e extends $r{callSetDisabledState;get submitted(){return rt(this.submittedReactive)}_submitted=Ar(()=>this.submittedReactive());submittedReactive=qn(!1);_directives=new Set;form;ngSubmit=new ze;options;constructor(n,r,i){super(),this.callSetDisabledState=i,this.form=new uc({},au(n),su(r))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(n){lo.then(()=>{let r=this._findContainer(n.path);n.control=r.registerControl(n.name,n.control),Zf(n.control,n,this.callSetDisabledState),n.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(n)})}getControl(n){return this.form.get(n.path)}removeControl(n){lo.then(()=>{let r=this._findContainer(n.path);r&&r.removeControl(n.name),this._directives.delete(n)})}addFormGroup(n){lo.then(()=>{let r=this._findContainer(n.path),i=new uc({});DM(i,n),r.registerControl(n.name,i),i.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(n){lo.then(()=>{let r=this._findContainer(n.path);r&&r.removeControl(n.name)})}getFormGroup(n){return this.form.get(n.path)}updateModel(n,r){lo.then(()=>{this.form.get(n.path).setValue(r)})}setValue(n){this.control.setValue(n)}onSubmit(n){return this.submittedReactive.set(!0),EM(this.form,this._directives),this.ngSubmit.emit(n),this.form._events.next(new nu(this.control)),n?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(n=void 0){this.form.reset(n),this.submittedReactive.set(!1),this.form._events.next(new ru(this.form))}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(n){return n.pop(),n.length?this.form.get(n):this.form}static \u0275fac=function(r){return new(r||e)(x(ou,10),x(jf,10),x(cu,8))};static \u0275dir=ge({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(r,i){r&1&&I("submit",function(a){return i.onSubmit(a)})("reset",function(){return i.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[sn([AM]),tt]})}return e})();function Pf(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function Ff(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var TM=class extends lc{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(t=null,n,r){super($f(n),Gf(r,n)),this._applyFormState(t),this._setUpdateStrategy(n),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),pc(n)&&(n.nonNullable||n.initialValueIsDefault)&&(Ff(t)?this.defaultValue=t.value:this.defaultValue=t)}setValue(t,n={}){this.value=this._pendingValue=t,this._onChange.length&&n.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,n.emitViewToModelChange!==!1)),this.updateValueAndValidity(n)}patchValue(t,n={}){this.setValue(t,n)}reset(t=this.defaultValue,n={}){this._applyFormState(t),this.markAsPristine(n),this.markAsUntouched(n),this.setValue(this.value,n),this._pendingChange=!1}_updateValue(){}_anyControls(t){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(t){this._onChange.push(t)}_unregisterOnChange(t){Pf(this._onChange,t)}registerOnDisabledChange(t){this._onDisabledChange.push(t)}_unregisterOnDisabledChange(t){Pf(this._onDisabledChange,t)}_forEachChild(t){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(t){Ff(t)?(this.value=this._pendingValue=t.value,t.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=t}};var RM={provide:po,useExisting:st(()=>ar)},Nf=Promise.resolve(),ar=(()=>{class e extends po{_changeDetectorRef;callSetDisabledState;control=new TM;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new ze;constructor(n,r,i,o,a,s){super(),this._changeDetectorRef=a,this.callSetDisabledState=s,this._parent=n,this._setValidators(r),this._setAsyncValidators(i),this.valueAccessor=LM(this,o)}ngOnChanges(n){if(this._checkForErrors(),!this._registered||"name"in n){if(this._registered&&(this._checkName(),this.formDirective)){let r=n.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in n&&this._updateDisabled(n),SM(n,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(n){this.viewModel=n,this.update.emit(n)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){Zf(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(n){Nf.then(()=>{this.control.setValue(n,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(n){let r=n.isDisabled.currentValue,i=r!==0&&m1(r);Nf.then(()=>{i&&!this.control.disabled?this.control.disable():!i&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(n){return this._parent?wM(n,this._parent):[n]}static \u0275fac=function(r){return new(r||e)(x($r,9),x(ou,10),x(jf,10),x(hc,10),x(ue,8),x(cu,8))};static \u0275dir=ge({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[sn([RM]),tt,Xn]})}return e})();var Yf=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=ge({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1})}return e})();var PM={provide:hc,useExisting:st(()=>Zr),multi:!0};function Qf(e,t){return e==null?`${t}`:(t&&typeof t=="object"&&(t="Object"),`${e}: ${t}`.slice(0,50))}function FM(e){return e.split(":")[0]}var Zr=(()=>{class e extends iu{value;_optionMap=new Map;_idCounter=0;set compareWith(n){this._compareWith=n}_compareWith=Object.is;writeValue(n){this.value=n;let r=this._getOptionId(n),i=Qf(r,n);this.setProperty("value",i)}registerOnChange(n){this.onChange=r=>{this.value=this._getOptionValue(r),n(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(n){for(let r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r),n))return r;return null}_getOptionValue(n){let r=FM(n);return this._optionMap.has(r)?this._optionMap.get(r):n}static \u0275fac=(()=>{let n;return function(i){return(n||(n=Sr(e)))(i||e)}})();static \u0275dir=ge({type:e,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(r,i){r&1&&I("change",function(a){return i.onChange(a.target.value)})("blur",function(){return i.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[sn([PM]),tt]})}return e})(),Xr=(()=>{class e{_element;_renderer;_select;id;constructor(n,r,i){this._element=n,this._renderer=r,this._select=i,this._select&&(this.id=this._select._registerOption())}set ngValue(n){this._select!=null&&(this._select._optionMap.set(this.id,n),this._setElementValue(Qf(this.id,n)),this._select.writeValue(this._select.value))}set value(n){this._setElementValue(n),this._select&&this._select.writeValue(this._select.value)}_setElementValue(n){this._renderer.setProperty(this._element.nativeElement,"value",n)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(r){return new(r||e)(x(bt),x(qt),x(Zr,9))};static \u0275dir=ge({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})(),NM={provide:hc,useExisting:st(()=>Jf),multi:!0};function Of(e,t){return e==null?`${t}`:(typeof t=="string"&&(t=`'${t}'`),t&&typeof t=="object"&&(t="Object"),`${e}: ${t}`.slice(0,50))}function OM(e){return e.split(":")[0]}var Jf=(()=>{class e extends iu{value;_optionMap=new Map;_idCounter=0;set compareWith(n){this._compareWith=n}_compareWith=Object.is;writeValue(n){this.value=n;let r;if(Array.isArray(n)){let i=n.map(o=>this._getOptionId(o));r=(o,a)=>{o._setSelected(i.indexOf(a.toString())>-1)}}else r=(i,o)=>{i._setSelected(!1)};this._optionMap.forEach(r)}registerOnChange(n){this.onChange=r=>{let i=[],o=r.selectedOptions;if(o!==void 0){let a=o;for(let s=0;s<a.length;s++){let c=a[s],d=this._getOptionValue(c.value);i.push(d)}}else{let a=r.options;for(let s=0;s<a.length;s++){let c=a[s];if(c.selected){let d=this._getOptionValue(c.value);i.push(d)}}}this.value=i,n(i)}}_registerOption(n){let r=(this._idCounter++).toString();return this._optionMap.set(r,n),r}_getOptionId(n){for(let r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r)._value,n))return r;return null}_getOptionValue(n){let r=OM(n);return this._optionMap.has(r)?this._optionMap.get(r)._value:n}static \u0275fac=(()=>{let n;return function(i){return(n||(n=Sr(e)))(i||e)}})();static \u0275dir=ge({type:e,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(r,i){r&1&&I("change",function(a){return i.onChange(a.target)})("blur",function(){return i.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[sn([NM]),tt]})}return e})(),Kr=(()=>{class e{_element;_renderer;_select;id;_value;constructor(n,r,i){this._element=n,this._renderer=r,this._select=i,this._select&&(this.id=this._select._registerOption(this))}set ngValue(n){this._select!=null&&(this._value=n,this._setElementValue(Of(this.id,n)),this._select.writeValue(this._select.value))}set value(n){this._select?(this._value=n,this._setElementValue(Of(this.id,n)),this._select.writeValue(this._select.value)):this._setElementValue(n)}_setElementValue(n){this._renderer.setProperty(this._element.nativeElement,"value",n)}_setSelected(n){this._renderer.setProperty(this._element.nativeElement,"selected",n)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(r){return new(r||e)(x(bt),x(qt),x(Jf,9))};static \u0275dir=ge({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})();var VM=(()=>{class e{_validator=Ef;_onChange;_enabled;ngOnChanges(n){if(this.inputName in n){let r=this.normalizeInput(n[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):Ef,this._onChange&&this._onChange()}}validate(n){return this._validator(n)}registerOnValidatorChange(n){this._onChange=n}enabled(n){return n!=null}static \u0275fac=function(r){return new(r||e)};static \u0275dir=ge({type:e,features:[Xn]})}return e})();var jM={provide:ou,useExisting:st(()=>uu),multi:!0};var uu=(()=>{class e extends VM{required;inputName="required";normalizeInput=m1;createValidator=n=>lM;enabled(n){return n}static \u0275fac=(()=>{let n;return function(i){return(n||(n=Sr(e)))(i||e)}})();static \u0275dir=ge({type:e,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(r,i){r&2&&Lr("required",i._enabled?"":null)},inputs:{required:"required"},standalone:!1,features:[sn([jM]),tt]})}return e})();var HM=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Dt({type:e});static \u0275inj=ct({})}return e})();var pn=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:cu,useValue:n.callSetDisabledState??du}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=Dt({type:e});static \u0275inj=ct({imports:[HM]})}return e})();var BM=["*"];var ey=[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]];var ty=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]];var fc=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]];var yc=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]];var ny=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]];var mc=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]];var ry=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]];var gc=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["rect",{x:"9",y:"9",width:"6",height:"6",rx:"1",key:"1ssd4o"}]];var iy=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]];var kc=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]];var Yr=[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]];var fn=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]];var oy=[["path",{d:"M21 21H8a2 2 0 0 1-1.42-.587l-3.994-3.999a2 2 0 0 1 0-2.828l10-10a2 2 0 0 1 2.829 0l5.999 6a2 2 0 0 1 0 2.828L12.834 21",key:"g5wo59"}],["path",{d:"m5.082 11.09 8.828 8.828",key:"1wx5vj"}]];var vc=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]];var ay=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]];var yn=[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]];var sy=[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]];var cy=[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]];var dy=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]];var ly=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]];var uy=[["path",{d:"M5 12h14",key:"1ays0h"}]];var hy=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]];var py=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]];var mn=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]];var fy=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]];var yy=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]];var my=[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]];var gy=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]];var Mc=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]];var wc=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]];var ky=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]];var vy=[["path",{d:"M12 19h8",key:"baeox8"}],["path",{d:"m4 17 6-6-6-6",key:"1yngyt"}]];var My=[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]];var wy=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]],gn=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]];var Cy=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]];var kn=[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]];var Cc=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]];var xy=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]];var At=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]];var xc=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]];var fo={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"},pu=new E("LucideIcons",{factory:()=>new yo({})}),yo=class{constructor(t){this.icons=t}getIcon(t){return this.hasIcon(t)?this.icons[t]:null}hasIcon(t){return typeof this.icons=="object"&&t in this.icons}},qM=(()=>{class e{constructor(){this.color=fo.stroke,this.size=fo.width,this.strokeWidth=fo["stroke-width"],this.absoluteStrokeWidth=!1}}return e.\u0275fac=function(n){return new(n||e)},e.\u0275prov=P({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();function zM(e,t=3){return parseFloat(e.toFixed(t)).toString(10)}var Re=(()=>{class e{constructor(n,r,i,o,a){this.elem=n,this.renderer=r,this.changeDetector=i,this.iconProviders=o,this.iconConfig=a,this.absoluteStrokeWidth=!1,this.defaultSize=fo.height}get size(){return this._size??this.iconConfig.size}set size(n){n?this._size=this.parseNumber(n):delete this._size}get strokeWidth(){return this._strokeWidth??this.iconConfig.strokeWidth}set strokeWidth(n){n?this._strokeWidth=this.parseNumber(n):delete this._strokeWidth}ngOnChanges(n){if(n.name||n.img||n.color||n.size||n.absoluteStrokeWidth||n.strokeWidth||n.class){this.color=this.color??this.iconConfig.color,this.size=this.parseNumber(this.size??this.iconConfig.size),this.strokeWidth=this.parseNumber(this.strokeWidth??this.iconConfig.strokeWidth),this.absoluteStrokeWidth=this.absoluteStrokeWidth??this.iconConfig.absoluteStrokeWidth;let r=this.img??this.name;if(typeof r=="string"){let i=this.getIcon(this.toPascalCase(r));if(i)this.replaceElement(i);else throw new Error(`The "${r}" icon has not been provided by any available icon providers.`)}else if(Array.isArray(r))this.replaceElement(r);else throw new Error("No icon name or image has been provided.")}this.changeDetector.markForCheck()}replaceElement(n){let r=G(A({},fo),{width:this.size,height:this.size,stroke:this.color??this.iconConfig.color,"stroke-width":this.absoluteStrokeWidth?zM(this.strokeWidth/(this.size/this.defaultSize)):this.strokeWidth.toString(10)}),i=this.createElement(["svg",r,n]);i.classList.add("lucide"),typeof this.name=="string"&&i.classList.add(`lucide-${this.name.replace("_","-")}`),this.class&&i.classList.add(...this.class.split(/ /).map(a=>a.trim()).filter(a=>a.length>0));let o=this.elem.nativeElement.childNodes;for(let a of o)this.renderer.removeChild(this.elem.nativeElement,a);this.renderer.appendChild(this.elem.nativeElement,i)}toPascalCase(n){return n.replace(/(\w)([a-z0-9]*)(_|-|\s*)/g,(r,i,o)=>i.toUpperCase()+o.toLowerCase())}parseNumber(n){if(typeof n=="string"){let r=parseInt(n,10);if(isNaN(r))throw new Error(`${n} is not numeric.`);return r}return n}getIcon(n){for(let r of Array.isArray(this.iconProviders)?this.iconProviders:[this.iconProviders])if(r.hasIcon(n))return r.getIcon(n);return null}createElement([n,r,i=[]]){let o=this.renderer.createElement(n,"http://www.w3.org/2000/svg");return Object.keys(r).forEach(a=>{let s=typeof r[a]=="string"?r[a]:r[a].toString(10);this.renderer.setAttribute(o,a,s)}),i.length&&i.forEach(a=>{let s=this.createElement(a);this.renderer.appendChild(o,s)}),o}}return e.\u0275fac=function(n){return new(n||e)(x(bt),x(qt),x(ue),x(pu),x(qM))},e.\u0275cmp=me({type:e,selectors:[["lucide-angular"],["lucide-icon"],["i-lucide"],["span-lucide"]],inputs:{class:"class",name:"name",img:"img",color:"color",absoluteStrokeWidth:"absoluteStrokeWidth",size:"size",strokeWidth:"strokeWidth"},standalone:!1,features:[Xn],ngContentSelectors:BM,decls:1,vars:0,template:function(n,r){n&1&&(s1(),c1(0))},encapsulation:2}),e})(),fu=class{constructor(t){this.icons=t}},UM=e=>new yo(e??{}),Pe=(()=>{class e{static pick(n){return{ngModule:e,providers:[{provide:pu,multi:!0,useValue:new yo(n)},{provide:pu,multi:!0,useFactory:UM,deps:[[new es,fu]]}]}}}return e.\u0275fac=function(n){return new(n||e)},e.\u0275mod=Dt({type:e}),e.\u0275inj=ct({imports:[[]]}),e})();function WM(e,t){if(e&1&&(u(0,"div",2)(1,"div",13),w(2,"lucide-icon",14),u(3,"h3",15),m(4," Sync Status "),h()(),u(5,"div",16),w(6,"lucide-icon",17),u(7,"span"),m(8),h()()()),e&2){let n=k(2);p(2),v("img",n.getStatusIcon()),p(4),v("img",n.getActionIcon()),p(2),Ee(n.getActionLabel())}}function $M(e,t){if(e&1&&m(0),e&2){let n=k(3);z(" / ",n.syncStatus.total_files," ")}}function GM(e,t){if(e&1&&m(0),e&2){let n=k(3);z(" / ",n.formatBytes(n.syncStatus.total_bytes)," ")}}function ZM(e,t){if(e&1&&(u(0,"div",3)(1,"h4",5),m(2," Transfer Statistics "),h(),u(3,"div",18)(4,"div",19)(5,"div",13),w(6,"lucide-icon",20),u(7,"div")(8,"div",21),m(9," Files "),h(),u(10,"div",22),m(11),H(12,$M,1,1),h()()()(),u(13,"div",19)(14,"div",13),w(15,"lucide-icon",23),u(16,"div")(17,"div",21),m(18," Data "),h(),u(19,"div",22),m(20),H(21,GM,1,1),h()()()()()()),e&2){let n=k(2);p(6),v("img",n.FileTextIcon),p(5),z(" ",n.syncStatus.files_transferred," "),p(),B(n.syncStatus.total_files>0?12:-1),p(3),v("img",n.HardDriveIcon),p(5),z(" ",n.formatBytes(n.syncStatus.bytes_transferred)," "),p(),B(n.syncStatus.total_bytes>0?21:-1)}}function XM(e,t){if(e&1&&(u(0,"div",19)(1,"div",13),w(2,"lucide-icon",20),u(3,"div")(4,"div",21),m(5," Checks "),h(),u(6,"div",22),m(7),h()()()()),e&2){let n=k(3);p(2),v("img",n.CheckIcon),p(5),z(" ",n.syncStatus.checks," ")}}function KM(e,t){if(e&1&&(u(0,"div",19)(1,"div",13),w(2,"lucide-icon",24),u(3,"div")(4,"div",21),m(5," Deletes "),h(),u(6,"div",22),m(7),h()()()()),e&2){let n=k(3);p(2),v("img",n.Trash2Icon),p(5),z(" ",n.syncStatus.deletes," ")}}function YM(e,t){if(e&1&&(u(0,"div",19)(1,"div",13),w(2,"lucide-icon",24),u(3,"div")(4,"div",21),m(5," Errors "),h(),u(6,"div",22),m(7),h()()()()),e&2){let n=k(3);p(2),v("img",n.AlertCircleIcon),p(5),z(" ",n.syncStatus.errors," ")}}function QM(e,t){if(e&1&&(u(0,"div",3)(1,"h4",5),m(2," Activity "),h(),u(3,"div",18),H(4,XM,8,2,"div",19),H(5,KM,8,2,"div",19),H(6,YM,8,2,"div",19),h()()),e&2){let n=k(2);p(4),B(n.syncStatus.checks>0?4:-1),p(),B(n.syncStatus.deletes>0?5:-1),p(),B(n.syncStatus.errors>0?6:-1)}}function JM(e,t){if(e&1&&(u(0,"div",12)(1,"h4",5),m(2," Current File "),h(),u(3,"div",25),w(4,"lucide-icon",26),u(5,"span",27),m(6),h()()()),e&2){let n=k(2);p(4),v("img",n.FolderOpenIcon),p(2),Ee(n.syncStatus.current_file)}}function e8(e,t){if(e&1&&(u(0,"div",0),H(1,WM,9,3,"div",2),u(2,"div",3)(3,"div",4)(4,"span",5),m(5,"Progress"),h(),u(6,"span",6),m(7),h()(),u(8,"div",7),w(9,"div",8),h(),u(10,"div",9)(11,"div",10),w(12,"lucide-icon",11),u(13,"span"),m(14),h()(),u(15,"div",10),w(16,"lucide-icon",11),u(17,"span"),m(18),h()(),u(19,"div",10),w(20,"lucide-icon",11),u(21,"span"),m(22),h()()()(),H(23,ZM,22,6,"div",3),H(24,QM,7,3,"div",3),H(25,JM,7,2,"div",12),h()),e&2){let n=k();p(),B(n.showTitle?1:-1),p(6),z("",n.getProgressValue().toFixed(1),"%"),p(2),ms("width",n.getProgressValue(),"%"),p(3),v("img",n.ZapIcon),p(2),Ee(n.syncStatus.speed),p(2),v("img",n.ClockIcon),p(2),Ee(n.syncStatus.eta),p(2),v("img",n.TimerIcon),p(2),Ee(n.syncStatus.elapsed_time),p(),B(n.hasTransferData()?23:-1),p(),B(n.hasActivityData()?24:-1),p(),B(n.syncStatus.current_file?25:-1)}}function t8(e,t){if(e&1&&(u(0,"div",1)(1,"div",28),w(2,"lucide-icon",29),u(3,"h3",30),m(4," Ready to Sync "),h(),u(5,"p",31),m(6," No sync operation running "),h()()()),e&2){let n=k();p(2),v("img",n.MoonIcon)}}var Iy=(()=>{let t=class t{constructor(){this.syncStatus=null,this.showTitle=!0,this.DownloadIcon=fn,this.UploadIcon=kn,this.RotateCwIcon=my,this.CheckCircleIcon=ry,this.XCircleIcon=iy,this.StopCircleIcon=gc,this.FileTextIcon=vc,this.HardDriveIcon=cy,this.CheckIcon=fc,this.Trash2Icon=gn,this.AlertCircleIcon=mc,this.FolderOpenIcon=yn,this.MoonIcon=hy,this.ZapIcon=xc,this.ClockIcon=kc,this.TimerIcon=My}getStatusColor(){if(!this.syncStatus)return"primary";switch(this.syncStatus.status){case"running":return"primary";case"completed":return"accent";case"error":return"warn";case"stopped":return"basic";default:return"primary"}}getStatusIcon(){if(!this.syncStatus)return this.RotateCwIcon;switch(this.syncStatus.status){case"running":return this.RotateCwIcon;case"completed":return this.CheckCircleIcon;case"error":return this.XCircleIcon;case"stopped":return this.StopCircleIcon;default:return this.RotateCwIcon}}getActionIcon(){if(!this.syncStatus)return this.RotateCwIcon;switch(this.syncStatus.action){case"pull":return this.DownloadIcon;case"push":return this.UploadIcon;case"bi":case"bi-resync":return this.RotateCwIcon;default:return this.RotateCwIcon}}getActionLabel(){if(!this.syncStatus)return"Sync";switch(this.syncStatus.action){case"pull":return"Pull";case"push":return"Push";case"bi":return"Bi-Sync";case"bi-resync":return"Bi-Resync";default:return"Sync"}}formatBytes(r){if(r===0)return"0 B";let i=1024,o=["B","KB","MB","GB","TB"],a=Math.floor(Math.log(r)/Math.log(i));return parseFloat((r/Math.pow(i,a)).toFixed(2))+" "+o[a]}getProgressValue(){return this.syncStatus?.progress||0}hasTransferData(){return!!(this.syncStatus&&(this.syncStatus.files_transferred>0||this.syncStatus.bytes_transferred>0||this.syncStatus.total_files>0||this.syncStatus.total_bytes>0))}hasActivityData(){return!!(this.syncStatus&&(this.syncStatus.checks>0||this.syncStatus.deletes>0||this.syncStatus.renames>0||this.syncStatus.errors>0))}};t.\u0275fac=function(i){return new(i||t)},t.\u0275cmp=me({type:t,selectors:[["app-sync-status"]],inputs:{syncStatus:"syncStatus",showTitle:"showTitle"},decls:2,vars:1,consts:[[1,"bg-white","dark:bg-gray-800","rounded-lg","border","border-gray-200","dark:border-gray-700","p-4","space-y-4"],[1,"bg-white","dark:bg-gray-800","rounded-lg","border","border-gray-200","dark:border-gray-700","p-4"],[1,"flex","items-center","justify-between","pb-3","border-b","border-gray-200","dark:border-gray-700"],[1,"space-y-3"],[1,"flex","items-center","justify-between"],[1,"text-sm","font-medium","text-gray-700","dark:text-gray-300"],[1,"text-sm","font-semibold","text-gray-900","dark:text-white"],[1,"w-full","bg-gray-200","dark:bg-gray-700","rounded-full","h-2"],[1,"bg-blue-600","h-2","rounded-full","transition-all","duration-300","ease-out"],[1,"flex","items-center","justify-between","text-xs","text-gray-600","dark:text-gray-400"],[1,"flex","items-center","space-x-1"],[1,"w-3","h-3",3,"img"],[1,"space-y-2"],[1,"flex","items-center","space-x-2"],[1,"w-5","h-5","text-blue-600",3,"img"],[1,"text-lg","font-semibold","text-gray-900","dark:text-white"],[1,"flex","items-center","space-x-2","px-3","py-1","bg-gray-100","dark:bg-gray-700","rounded-full","text-sm","font-medium","text-gray-700","dark:text-gray-300"],[1,"w-4","h-4",3,"img"],[1,"grid","grid-cols-2","gap-3"],[1,"bg-gray-50","dark:bg-gray-700","rounded-lg","p-3"],[1,"w-5","h-5","text-blue-500",3,"img"],[1,"text-sm","font-medium","text-gray-900","dark:text-white"],[1,"text-xs","text-gray-600","dark:text-gray-400"],[1,"w-5","h-5","text-green-500",3,"img"],[1,"w-5","h-5","text-red-500",3,"img"],[1,"flex","items-center","space-x-2","p-3","bg-gray-50","dark:bg-gray-700","rounded-lg"],[1,"w-4","h-4","text-blue-500",3,"img"],[1,"text-sm","text-gray-900","dark:text-white","truncate"],[1,"text-center","py-8"],[1,"w-12","h-12","mx-auto","mb-4","text-gray-400",3,"img"],[1,"text-lg","font-semibold","text-gray-900","dark:text-white","mb-2"],[1,"text-sm","text-gray-600","dark:text-gray-400"]],template:function(i,o){i&1&&H(0,e8,26,13,"div",0)(1,t8,7,1,"div",1),i&2&&B(o.syncStatus?0:1)},dependencies:[we,Pe,Re],encapsulation:2,changeDetection:0});let e=t;return e})();function n8(e,t){if(e&1){let n=Q();u(0,"div",1)(1,"div",4)(2,"div",5)(3,"h1",6),m(4," Welcome to NS Drive Dashboard "),h(),u(5,"p",7),m(6," Start by creating your first sync operation "),h()(),u(7,"div",5)(8,"p",8),m(9," Sync operations allow you to synchronize files between local directories and cloud storage services. Each operation runs independently with its own configuration and profile. "),h(),u(10,"div",9)(11,"div",10),w(12,"lucide-icon",11),u(13,"span",12),m(14,"Real-time synchronization"),h()(),u(15,"div",10),w(16,"lucide-icon",11),u(17,"span",12),m(18,"Multiple cloud providers"),h()(),u(19,"div",10),w(20,"lucide-icon",11),u(21,"span",12),m(22,"Customizable profiles"),h()()()(),u(23,"div",13)(24,"button",14),I("click",function(){b(n);let i=k();return D(i.createTab())}),u(25,"div",15),w(26,"lucide-icon",16),m(27," Create First Operation "),h()()()()()}if(e&2){let n=k();p(12),v("img",n.RefreshCwIcon),p(4),v("img",n.CloudIcon),p(4),v("img",n.SettingsIcon),p(6),v("img",n.PlusIcon)}}function r8(e,t){if(e&1){let n=Q();u(0,"div",27)(1,"button",28),I("click",function(i){b(n);let o=k().$implicit,a=k(2);return i.stopPropagation(),D(a.startRenameTab(o.id))}),w(2,"lucide-icon",29),h(),u(3,"button",30),I("click",function(i){b(n);let o=k().$implicit,a=k(2);return i.stopPropagation(),D(a.deleteTab(o.id))}),w(4,"lucide-icon",29),h()()}if(e&2){let n=k(3);p(2),v("img",n.EditIcon),p(2),v("img",n.Trash2Icon)}}function i8(e,t){if(e&1){let n=Q();u(0,"button",25),I("click",function(){let i=b(n).$index,o=k(2);return D(o.onTabChange(i))}),u(1,"div",15)(2,"span",26),m(3),h(),H(4,r8,5,2,"div",27),h()()}if(e&2){let n=t.$implicit,r=t.$index,i=k(2);_e(i.getActiveTabIndex()===r?"tab-button-active whitespace-nowrap":"tab-button whitespace-nowrap"),p(3),Ee((n==null?null:n.name)||"Operation "+(r+1)),p(),B(n&&n.id?4:-1)}}function o8(e,t){if(e&1&&(u(0,"option",35),m(1),h()),e&2){let n=t.$implicit,r=t.$index;v("value",r),p(),z(" ",n.name," ")}}function a8(e,t){if(e&1){let n=Q();u(0,"div")(1,"div",44)(2,"button",45),I("click",function(){b(n);let i=k(2).$implicit,o=k(2);return D(i.currentAction!==o.Action.Pull?o.pullTab(i.id):o.stopCommandTab(i.id))}),u(3,"div",15),w(4,"lucide-icon",16),m(5),h()(),u(6,"button",45),I("click",function(){b(n);let i=k(2).$implicit,o=k(2);return D(i.currentAction!==o.Action.Push?o.pushTab(i.id):o.stopCommandTab(i.id))}),u(7,"div",15),w(8,"lucide-icon",16),m(9),h()(),u(10,"button",45),I("click",function(){b(n);let i=k(2).$implicit,o=k(2);return D(i.currentAction!==o.Action.Bi?o.biTab(i.id):o.stopCommandTab(i.id))}),u(11,"div",15),w(12,"lucide-icon",16),m(13),h()(),u(14,"button",46),I("click",function(){b(n);let i=k(2).$implicit,o=k(2);return D(i.currentAction!==o.Action.BiResync?o.biResyncTab(i.id):o.stopCommandTab(i.id))}),u(15,"div",15),w(16,"lucide-icon",16),m(17),h()()()()}if(e&2){let n=k(2).$implicit,r=k(2);p(2),_e(n.currentAction===r.Action.Pull?"bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap":"btn-primary"),v("disabled",!r.validateTabProfileIndex(n)&&n.currentAction!==r.Action.Pull||n.isStopping),p(2),v("img",n.isStopping?r.ClockIcon:n.currentAction===r.Action.Pull?r.StopCircleIcon:r.DownloadIcon),p(),z(" ",n.isStopping?"Stopping...":n.currentAction===r.Action.Pull?"Stop Pull":"Pull"," "),p(),_e(n.currentAction===r.Action.Push?"bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap":"bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap"),v("disabled",!r.validateTabProfileIndex(n)&&n.currentAction!==r.Action.Push),p(2),v("img",n.currentAction===r.Action.Push?r.StopCircleIcon:r.UploadIcon),p(),z(" ",n.currentAction===r.Action.Push?"Stop Push":"Push"," "),p(),_e(n.currentAction===r.Action.Bi?"bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap":"btn-primary"),v("disabled",!r.validateTabProfileIndex(n)&&n.currentAction!==r.Action.Bi),p(2),v("img",n.currentAction===r.Action.Bi?r.StopCircleIcon:r.RefreshCwIcon),p(),z(" ",n.currentAction===r.Action.Bi?"Stop Sync":"Sync"," "),p(),v("disabled",!r.validateTabProfileIndex(n)&&n.currentAction!==r.Action.BiResync),p(2),v("img",n.currentAction===r.Action.BiResync?r.StopCircleIcon:r.RotateCcwIcon),p(),z(" ",n.currentAction===r.Action.BiResync?"Stop Resync":"Resync"," ")}}function s8(e,t){if(e&1){let n=Q();u(0,"div",31)(1,"div")(2,"label",32),m(3," Sync Profile "),h(),u(4,"div",33)(5,"select",34),I("change",function(i){b(n);let o=k().$implicit,a=k(2);return D(a.onProfileChange(i,o==null?null:o.id))}),u(6,"option",35),m(7,"No profile selected"),h(),De(8,o8,2,2,"option",35,nt),h(),u(10,"div",36),w(11,"lucide-icon",37),h()()(),u(12,"div")(13,"div",38),w(14,"lucide-icon",39),u(15,"span",40),m(16,"Working Directory"),h()(),u(17,"div",41)(18,"code",42),m(19),ne(20,"async"),h()()(),u(21,"div"),w(22,"app-sync-status",43),h(),H(23,a8,18,18,"div"),h()}if(e&2){let n,r=k(),i=r.$implicit,o=r.$index,a=k(2);p(2),v("for",gs("profile-select-",o)),p(3),v("id",gs("profile-select-",o))("value",i==null?null:i.selectedProfileIndex)("disabled",!i||!i.id),p(),v("value",null),p(2),Se(a.appService.configInfo$.value.profiles),p(3),v("img",a.ChevronDownIcon),p(3),v("img",a.FolderOpenIcon),p(5),Ee((n=ce(20,13,a.appService.configInfo$))==null?null:n.working_dir),p(3),v("syncStatus",i.syncStatus||null)("showTitle",!0),p(),B(a.validateTabProfileIndex(i)?23:-1)}}function c8(e,t){if(e&1&&(u(0,"div"),H(1,s8,24,15,"div",31),h()),e&2){let n=t.$index,r=k(2);p(),B(r.getActiveTabIndex()===n?1:-1)}}function d8(e,t){if(e&1){let n=Q();u(0,"div",2)(1,"div",4)(2,"div",5)(3,"div",17),w(4,"lucide-icon",11),u(5,"h1",18),m(6," Sync Operations "),h()(),u(7,"p",7),m(8),h()(),u(9,"div",19)(10,"div",20)(11,"nav",21),De(12,i8,5,4,"button",22,fs().trackByTabId,!0),h(),u(14,"button",23),I("click",function(){b(n);let i=k();return D(i.createTab())}),w(15,"lucide-icon",24),h()()(),De(16,c8,2,1,"div",null,fs().trackByTabId,!0),h()()}if(e&2){let n=k();p(4),v("img",n.RefreshCwIcon),p(4),z(" ",n.tabService.tabsValue.length," operation(s) configured "),p(4),Se(n.tabService.tabsValue),p(3),v("img",n.PlusIcon),p(),Se(n.tabService.tabsValue)}}function l8(e,t){if(e&1){let n=Q();u(0,"div",47),I("click",function(){b(n);let i=k();return D(i.cancelRename())})("keyup.escape",function(){b(n);let i=k();return D(i.cancelRename())}),u(1,"div",48),I("click",function(i){return b(n),D(i.stopPropagation())})("keydown",function(i){return b(n),D(i.stopPropagation())}),u(2,"h2",49),m(3," Rename Tab "),h(),u(4,"div",5)(5,"label",50),m(6," Tab Name "),h(),u(7,"input",51,0),gt("ngModelChange",function(i){b(n);let o=k();return _t(o.renameDialogData.newName,i)||(o.renameDialogData.newName=i),D(i)}),I("keydown.enter",function(){b(n);let i=k();return D(i.confirmRename())}),h()(),u(9,"div",52)(10,"button",53),I("click",function(){b(n);let i=k();return D(i.cancelRename())}),m(11,"Cancel"),h(),u(12,"button",23),I("click",function(){b(n);let i=k();return D(i.confirmRename())}),m(13,"Rename"),h()()()()}if(e&2){let n=k();p(7),mt("ngModel",n.renameDialogData.newName)}}var by=(()=>{let t=class t{constructor(r,i,o){this.appService=r,this.tabService=i,this.cdr=o,this.Action=W,this.SettingsIcon=Mc,this.PlusIcon=mn,this.DownloadIcon=fn,this.UploadIcon=kn,this.RefreshCwIcon=fy,this.RotateCcwIcon=yy,this.EditIcon=wc,this.Trash2Icon=gn,this.PlayIcon=py,this.SquareIcon=ky,this.EraserIcon=oy,this.FolderOpenIcon=yn,this.FileTextIcon=vc,this.CloudIcon=Yr,this.ChevronDownIcon=yc,this.TerminalIcon=vy,this.ArchiveIcon=ey,this.StopCircleIcon=gc,this.ClockIcon=kc,this.subscriptions=new X,this.showRenameDialog=!1,this.renameDialogData={tabId:"",newName:""}}ngOnInit(){console.log("HomeComponent ngOnInit called"),console.log("AppService configInfo$ current value:",this.appService.configInfo$.value),console.log("TabService tabs current value:",this.tabService.tabsValue),console.log("TabService activeTabId current value:",this.tabService.activeTabIdValue),(!this.subscriptions||this.subscriptions.closed)&&(console.log("HomeComponent creating new subscriptions"),this.subscriptions=new X);try{this.subscriptions.add(this.appService.configInfo$.pipe(Ce(r=>{if(console.log("HomeComponent configInfo$ pipe received:",r),!r){console.warn("HomeComponent received null/undefined configInfo");return}return this.validateCurrentProfileIndex(r)})).subscribe({next:r=>{console.log("HomeComponent profile validation result:",r),this.isCurrentProfileValid=r,this.cdr.detectChanges()},error:r=>{console.error("HomeComponent configInfo$ subscription error:",r),this.isCurrentProfileValid=void 0,this.cdr.detectChanges()}})),this.subscriptions.add(this.tabService.tabs.subscribe({next:r=>{console.log("HomeComponent tabs updated:",r),this.cdr.detectChanges()},error:r=>{console.error("HomeComponent tabs subscription error:",r)}}))}catch(r){console.error("HomeComponent ngOnInit error:",r),this.isCurrentProfileValid=void 0,this.cdr.detectChanges()}}ngOnDestroy(){console.log("HomeComponent ngOnDestroy called"),this.subscriptions.unsubscribe()}changeProfile(r){let i=r.target,o=eu(i.value);if(o!==null&&Q1(this.appService.configInfo$.value,o)){let a=this.appService.configInfo$.value,s=new Te;Object.assign(s,a),s.selected_profile_index=o,this.appService.configInfo$.next(s),this.appService.saveConfigInfo()}}validateCurrentProfileIndex(r){if(console.log("validateCurrentProfileIndex called with:",{configInfo:r,profiles:r?.profiles,selectedIndex:r?.selected_profile_index,profilesLength:r?.profiles?.length}),!r||!r.profiles||!Array.isArray(r.profiles)){console.warn("validateCurrentProfileIndex: invalid configInfo or profiles");return}if(typeof r.selected_profile_index!="number"||r.selected_profile_index<0||r.selected_profile_index>=r.profiles.length){console.warn("validateCurrentProfileIndex: invalid selected_profile_index");return}let i=r.profiles[r.selected_profile_index];return console.log("validateCurrentProfileIndex result:",i),i}pull(){let r=this.validateCurrentProfileIndex(this.appService.configInfo$.value);r&&this.appService.pull(r)}push(){let r=this.validateCurrentProfileIndex(this.appService.configInfo$.value);r&&this.appService.push(r)}bi(){let r=this.validateCurrentProfileIndex(this.appService.configInfo$.value);r&&this.appService.bi(r)}biResync(){let r=this.validateCurrentProfileIndex(this.appService.configInfo$.value);r&&this.appService.bi(r,!0)}stopCommand(){this.appService.stopCommand()}createTab(){this.tabService.createTab(),this.cdr.detectChanges()}deleteTab(r){let i=this.tabService.getTab(r);if(i&&i.currentTaskId&&i.currentAction){if(!confirm(`This operation is currently running (${i.currentAction}). Deleting this tab will stop the operation. Are you sure you want to continue?`))return;this.appService.stopCommandForTab(r)}this.tabService.deleteTab(r)}setActiveTab(r){this.tabService.setActiveTab(r)}pullTab(r){let i=this.tabService.getTab(r);if(!i||!zr(this.appService.configInfo$.value,i.selectedProfileIndex))return;let o=this.appService.configInfo$.value.profiles[i.selectedProfileIndex];this.appService.pullWithTab(o,r),this.cdr.detectChanges()}pushTab(r){let i=this.tabService.getTab(r);if(!i||!zr(this.appService.configInfo$.value,i.selectedProfileIndex))return;let o=this.appService.configInfo$.value.profiles[i.selectedProfileIndex];this.appService.pushWithTab(o,r),this.cdr.detectChanges()}biTab(r){let i=this.tabService.getTab(r);if(!i||!zr(this.appService.configInfo$.value,i.selectedProfileIndex))return;let o=this.appService.configInfo$.value.profiles[i.selectedProfileIndex];this.appService.biWithTab(o,r),this.cdr.detectChanges()}biResyncTab(r){let i=this.tabService.getTab(r);if(!i||!zr(this.appService.configInfo$.value,i.selectedProfileIndex))return;let o=this.appService.configInfo$.value.profiles[i.selectedProfileIndex];this.appService.biWithTab(o,r,!0),this.cdr.detectChanges()}stopCommandTab(r){let i=this.tabService.getTab(r);i&&(console.log("Stopping command for tab:",r,"currentTaskId:",i.currentTaskId,"currentAction:",i.currentAction),this.tabService.updateTab(r,{isStopping:!0,data:[...i.data||[],"Stopping command..."]}),this.appService.stopCommandForTab(r),this.cdr.detectChanges())}changeProfileTab(r,i){if(!i)return;let o=eu(r);this.tabService.updateTab(i,{selectedProfileIndex:o})}validateTabProfileIndex(r){return zr(this.appService.configInfo$.value,r.selectedProfileIndex)}startRenameTab(r){let i=this.tabService.getTab(r);i&&(this.renameDialogData={tabId:r,newName:i.name},this.showRenameDialog=!0,setTimeout(()=>{let o=document.querySelector('input[type="text"]');o&&(o.focus(),o.select())},100))}confirmRename(){this.renameDialogData.newName.trim()&&this.tabService.finishRenameTab(this.renameDialogData.tabId,this.renameDialogData.newName.trim()),this.showRenameDialog=!1}cancelRename(){this.showRenameDialog=!1}finishRenameTab(r,i){this.tabService.finishRenameTab(r,i)}cancelRenameTab(r){this.tabService.cancelRenameTab(r)}getActiveTabIndex(){let r=this.tabService.activeTabIdValue;if(!r)return 0;let i=this.tabService.tabsValue.findIndex(o=>o.id===r);return i>=0?i:0}onTabChange(r){try{let i=this.tabService.tabsValue;r>=0&&r<i.length&&i[r]&&this.tabService.setActiveTab(i[r].id)}catch(i){console.error("Error in onTabChange:",i)}}getActionColor(r){return J1(r).color}getActionIcon(r){switch(r){case W.Pull:return this.DownloadIcon;case W.Push:return this.UploadIcon;case W.Bi:case W.BiResync:return this.RefreshCwIcon;default:return this.RefreshCwIcon}}getActionIconPath(r){switch(r){case W.Pull:return"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10";case W.Push:return"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12";case W.Bi:case W.BiResync:return"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15";default:return"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"}}getActionLabel(r){return J1(r).label}onProfileChange(r,i){let o=r.target,a=o.value==="null"?null:+o.value;this.changeProfileTab(a,i)}trackByTabId(r,i){return i?.id||r.toString()}getSelectedProfile(r){if(!r||r.selectedProfileIndex===null||r.selectedProfileIndex===void 0)return null;let i=this.appService.configInfo$.value;return!i||!i.profiles||r.selectedProfileIndex>=i.profiles.length||r.selectedProfileIndex<0?null:i.profiles[r.selectedProfileIndex]}};t.\u0275fac=function(i){return new(i||t)(x(Lt),x(ic),x(ue))},t.\u0275cmp=me({type:t,selectors:[["app-home"]],decls:3,vars:3,consts:[["renameDialogInput",""],[1,"p-6","max-w-4xl","mx-auto","space-y-6"],[1,"p-6","max-w-6xl","mx-auto"],["tabindex","0","role","dialog","aria-modal","true","aria-labelledby","rename-dialog-title",1,"fixed","inset-0","bg-black","bg-opacity-50","flex","items-center","justify-center","z-50"],[1,"card"],[1,"mb-6"],[1,"text-2xl","font-bold","text-gray-900","dark:text-gray-100","mb-2"],[1,"text-gray-600","dark:text-gray-400"],[1,"text-gray-700","dark:text-gray-300","mb-6"],[1,"space-y-4"],[1,"flex","items-center","space-x-3"],[1,"w-6","h-6","text-primary-600",3,"img"],[1,"text-gray-700","dark:text-gray-300"],[1,"flex","justify-end"],[1,"btn-primary","whitespace-nowrap",3,"click"],[1,"flex","items-center","space-x-2"],[1,"w-5","h-5","mr-2",3,"img"],[1,"flex","items-center","space-x-3","mb-2"],[1,"text-2xl","font-bold","text-gray-900","dark:text-gray-100"],[1,"border-b","border-gray-200","dark:border-gray-700","mb-6"],[1,"flex","items-center","justify-between"],[1,"flex","space-x-8","overflow-x-auto","hide-scrollbar"],[3,"class"],[1,"btn-primary",3,"click"],[1,"w-5","h-5",3,"img"],[3,"click"],[1,"whitespace-nowrap"],[1,"flex","items-center","space-x-1","ml-2"],["title","Rename tab",1,"p-1","rounded","hover:bg-gray-200","dark:hover:bg-gray-600","text-gray-600","dark:text-gray-400","hover:text-gray-800","dark:hover:text-gray-200","whitespace-nowrap",3,"click"],[1,"w-4","h-4",3,"img"],["title","Delete tab",1,"p-1","rounded","hover:bg-red-100","dark:hover:bg-red-900","text-red-600","dark:text-red-400","hover:text-red-800","dark:hover:text-red-300","whitespace-nowrap",3,"click"],[1,"space-y-6"],[1,"block","text-sm","font-medium","text-gray-700","dark:text-gray-300","mb-2",3,"for"],[1,"relative"],[1,"select-field","pr-10",3,"change","id","value","disabled"],[3,"value"],[1,"absolute","inset-y-0","right-0","flex","items-center","pr-3","pointer-events-none"],[1,"w-5","h-5","text-gray-400",3,"img"],[1,"flex","items-center","space-x-2","mb-3"],[1,"w-5","h-5","text-gray-600","dark:text-gray-400",3,"img"],[1,"font-medium","text-gray-700","dark:text-gray-300"],[1,"bg-gray-100","dark:bg-gray-800","p-4","rounded-lg"],[1,"text-sm","text-gray-800","dark:text-gray-200"],[3,"syncStatus","showTitle"],[1,"flex","flex-wrap","gap-3"],[3,"click","disabled"],[1,"bg-orange-600","hover:bg-orange-700","text-white","font-medium","py-2","px-4","rounded-lg","transition-colors","duration-200","whitespace-nowrap",3,"click","disabled"],["tabindex","0","role","dialog","aria-modal","true","aria-labelledby","rename-dialog-title",1,"fixed","inset-0","bg-black","bg-opacity-50","flex","items-center","justify-center","z-50",3,"click","keyup.escape"],["tabindex","-1",1,"bg-white","dark:bg-gray-800","rounded-lg","p-6","w-96","max-w-md","mx-4",3,"click","keydown"],["id","rename-dialog-title",1,"text-xl","font-bold","text-gray-900","dark:text-gray-100","mb-4"],["for","rename-tab-input",1,"block","text-sm","font-medium","text-gray-700","dark:text-gray-300","mb-2"],["id","rename-tab-input","type","text",1,"input-field",3,"ngModelChange","keydown.enter","ngModel"],[1,"flex","justify-end","space-x-3"],[1,"btn-secondary",3,"click"]],template:function(i,o){i&1&&(H(0,n8,28,4,"div",1),H(1,d8,18,3,"div",2),H(2,l8,14,1,"div",3)),i&2&&(B(o.tabService.tabsValue.length===0?0:-1),p(),B(o.tabService.tabsValue.length>0?1:-1),p(),B(o.showRenameDialog?2:-1))},dependencies:[we,vt,pn,Xr,Kr,hn,Gr,ar,Pe,Re,Iy],styles:[".fade-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-in-out}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.console-output[_ngcontent-%COMP%]::-webkit-scrollbar{width:8px}.console-output[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#0000001a;border-radius:4px}.console-output[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#0000004d;border-radius:4px}.console-output[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#00000080}"],changeDetection:0});let e=t;return e})();function Qr(e,t){return t>=0&&t<e.length}function oe(e,t){return t>=0&&t<e.length}function Tt(e){let t=e.indexOf(":");return t>0?{remote:e.substring(0,t),path:e.substring(t+1)}:{remote:"",path:e}}function Jr(e,t){return e?`${e}:${t||""}`:t||""}function Rt(e){return e.endsWith("/**")?{type:"folder",value:e.slice(0,-3)}:{type:"file",value:e}}function Pt(e){return e.type==="folder"?e.value+"/**":e.value}var Ic=[1,2,5,10,20,50,100],bc=[1,2,4,8,16,32,64];var ei=(()=>{let t=class t{constructor(){this.navigationState$=new J({page:"home"}),console.log("NavigationService constructor called, initial state: home")}get currentState$(){return this.navigationState$.asObservable()}get currentState(){return this.navigationState$.value}navigateToProfiles(){console.log("NavigationService navigateToProfiles called"),this.navigationState$.next({page:"profiles"})}navigateToProfileEdit(r){console.log("NavigationService navigateToProfileEdit called with index:",r),this.navigationState$.next({page:"profile-edit",profileIndex:r})}navigateToRemotes(){console.log("NavigationService navigateToRemotes called"),this.navigationState$.next({page:"remotes"})}navigateToHome(){console.log("NavigationService navigateToHome called"),this.navigationState$.next({page:"home"})}};t.\u0275fac=function(i){return new(i||t)},t.\u0275prov=P({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();function u8(e,t){if(e&1){let n=Q();u(0,"div",16),I("click",function(){let i=b(n).$index,o=k(2);return D(o.editProfile(i))})("keydown.enter",function(){let i=b(n).$index,o=k(2);return D(o.editProfile(i))})("keydown.space",function(){let i=b(n).$index,o=k(2);return D(o.editProfile(i))}),u(1,"div",17),w(2,"lucide-icon",18),h(),u(3,"div",19)(4,"h3",20),m(5),h(),u(6,"p",21),m(7),h()(),u(8,"div",22)(9,"button",23),I("click",function(i){let o=b(n).$index;return k(2).removeProfile(o),D(i.stopPropagation())}),w(10,"lucide-icon",24),h()()()}if(e&2){let n=t.$implicit,r=k(2);Lr("aria-label","Edit profile "+n.name),p(2),v("img",r.FolderOpenIcon),p(3),z(" ",n.name||"Untitled Profile"," "),p(2),z(" ",r.getProfileDescription(n)," "),p(3),v("img",r.Trash2Icon)}}function h8(e,t){if(e&1&&(u(0,"div",11)(1,"div",14),De(2,u8,11,5,"div",15,nt),ne(4,"async"),h()()),e&2){let n,r=k();p(2),Se((n=ce(4,0,r.appService.configInfo$))==null?null:n.profiles)}}function p8(e,t){if(e&1){let n=Q();u(0,"div",0)(1,"div",25)(2,"div",1)(3,"div",26)(4,"div",27)(5,"div",28),w(6,"lucide-icon",29)(7,"lucide-icon",30),h()()(),u(8,"h2",31),m(9," No Profiles Found "),h(),u(10,"p",32),m(11," Create your first sync profile to get started "),h()(),u(12,"div")(13,"p",33),m(14," Profiles allow you to configure different sync settings for various directories and remotes. "),h(),u(15,"button",34),I("click",function(){b(n);let i=k();return D(i.addProfile())}),u(16,"div",6),w(17,"lucide-icon",35),m(18," Create First Profile "),h()()()()()}if(e&2){let n=k();p(6),v("img",n.FolderOpenIcon),p(),v("img",n.XIcon),p(10),v("img",n.PlusIcon)}}var Dy=(()=>{let t=class t{constructor(r,i,o,a){this.appService=r,this.cdr=i,this.navigationService=o,this.errorService=a,this.Date=Date,this.subscriptions=new X,this.UsersIcon=Cc,this.PlusIcon=mn,this.FolderOpenIcon=yn,this.XIcon=At,this.Trash2Icon=gn,this.DownloadIcon=fn,this.UploadIcon=kn,this.saveBtnText$=new J("Save \u2713"),this.bandwidthOptions=Ic,this.parallelOptions=bc}ngOnInit(){this.subscriptions.add(this.appService.configInfo$.subscribe(()=>this.cdr.detectChanges())),this.appService.getConfigInfo()}ngOnDestroy(){this.subscriptions.unsubscribe()}addProfile(){return O(this,null,function*(){try{let r=yield this.appService.addProfile();this.navigationService.navigateToProfileEdit(r)}catch(r){console.error("Error creating profile:",r),this.errorService.handleApiError(r,"create_profile")}})}editProfile(r){this.navigationService.navigateToProfileEdit(r)}removeProfile(r){return O(this,null,function*(){if(!Qr(this.appService.configInfo$.value.profiles,r)){console.error("Invalid profile index:",r);return}try{yield this.appService.removeProfile(r)}catch(i){console.error("Error removing profile:",i),this.errorService.handleApiError(i,"remove_profile")}})}saveConfigInfo(){this.appService.saveConfigInfo(),this.saveBtnText$.next("Saved ~"),setTimeout(()=>this.saveBtnText$.next("Save \u2713"),1e3),this.cdr.detectChanges()}exportProfiles(){return O(this,null,function*(){try{yield this.appService.exportProfiles(),this.errorService.showSuccess("Profiles exported successfully!")}catch(r){console.error("Error exporting profiles:",r),this.errorService.handleApiError(r,"export_profiles")}})}importProfiles(){return O(this,null,function*(){try{yield this.appService.importProfiles(),this.errorService.showSuccess("Profiles imported successfully!")}catch(r){console.error("Error importing profiles:",r),this.errorService.handleApiError(r,"import_profiles")}})}addIncludePath(r){if(!Qr(this.appService.configInfo$.value.profiles,r)){console.error("Invalid profile index:",r);return}this.appService.addIncludePath(r),this.cdr.detectChanges()}removeIncludePath(r,i){let o=this.appService.configInfo$.value.profiles;if(!Qr(o,r)||!oe(o[r].included_paths,i)){console.error("Invalid indices:",{profileIndex:r,pathIndex:i});return}this.appService.removeIncludePath(r,i),this.cdr.detectChanges()}addExcludePath(r){if(!Qr(this.appService.configInfo$.value.profiles,r)){console.error("Invalid profile index:",r);return}this.appService.addExcludePath(r),this.cdr.detectChanges()}removeExcludePath(r,i){let o=this.appService.configInfo$.value.profiles;if(!Qr(o,r)||!oe(o[r].excluded_paths,i)){console.error("Invalid indices:",{profileIndex:r,pathIndex:i});return}this.appService.removeExcludePath(r,i),this.cdr.detectChanges()}trackByFn(r){return r}getNumberRange(r,i){return Array.from({length:i-r+1},(o,a)=>r+a)}getProfileDescription(r){let i=r.from||"Not configured",o=r.to||"Not configured";return`${i} \u2192 ${o}`}getFromRemote(r){return Tt(r.from||"").remote}getFromPath(r){return Tt(r.from||"").path}updateFromPath(r,i,o){r.from=Jr(i,o),this.cdr.detectChanges()}getToRemote(r){return Tt(r.to||"").remote}getToPath(r){return Tt(r.to||"").path}updateToPath(r,i,o){r.to=Jr(i,o),this.cdr.detectChanges()}getIncludePathType(r,i){return oe(r.included_paths,i)?Rt(r.included_paths[i]).type:"folder"}getIncludePathValue(r,i){return oe(r.included_paths,i)?Rt(r.included_paths[i]).value:""}updateIncludePathType(r,i,o){if(!oe(r.included_paths,i)){console.error("Invalid path index:",i);return}let a=this.getIncludePathValue(r,i),s={type:o,value:a};r.included_paths[i]=Pt(s),this.cdr.detectChanges()}updateIncludePathValue(r,i,o){if(!oe(r.included_paths,i)){console.error("Invalid path index:",i);return}let s={type:this.getIncludePathType(r,i),value:o};r.included_paths[i]=Pt(s),this.cdr.detectChanges()}getExcludePathType(r,i){return oe(r.excluded_paths,i)?Rt(r.excluded_paths[i]).type:"folder"}getExcludePathValue(r,i){return oe(r.excluded_paths,i)?Rt(r.excluded_paths[i]).value:""}updateExcludePathType(r,i,o){if(!oe(r.excluded_paths,i)){console.error("Invalid path index:",i);return}let a=this.getExcludePathValue(r,i),s={type:o,value:a};r.excluded_paths[i]=Pt(s),this.cdr.detectChanges()}updateExcludePathValue(r,i,o){if(!oe(r.excluded_paths,i)){console.error("Invalid path index:",i);return}let s={type:this.getExcludePathType(r,i),value:o};r.excluded_paths[i]=Pt(s),this.cdr.detectChanges()}};t.\u0275fac=function(i){return new(i||t)(x(Lt),x(ue),x(ei),x(We))},t.\u0275cmp=me({type:t,selectors:[["app-profiles"]],decls:24,vars:10,consts:[[1,"p-6","max-w-4xl","mx-auto"],[1,"mb-6"],[1,"flex","items-center","justify-between","mb-2"],[1,"flex","items-center","space-x-3"],[1,"w-6","h-6","text-primary-600",3,"img"],[1,"text-2xl","font-bold","text-gray-900","dark:text-gray-100"],[1,"flex","items-center","space-x-2"],["title","Import Profiles",1,"btn-secondary","flex","items-center","space-x-2",3,"click"],[1,"w-4","h-4",3,"img"],["title","Export Profiles",1,"btn-secondary","flex","items-center","space-x-2",3,"click"],[1,"text-gray-600","dark:text-gray-400"],[1,"card"],["title","Add Profile",1,"fixed","bottom-24","right-6","w-14","h-14","bg-primary-600","hover:bg-primary-700","text-white","rounded-full","shadow-lg","hover:shadow-xl","transition-all","duration-200","flex","items-center","justify-center","z-50",3,"click"],[1,"w-6","h-6",3,"img"],[1,"space-y-3"],["tabindex","0","role","button",1,"flex","items-center","p-4","bg-gray-50","dark:bg-gray-800","rounded-lg","hover:bg-gray-100","dark:hover:bg-gray-700","cursor-pointer","transition-colors","duration-200","group"],["tabindex","0","role","button",1,"flex","items-center","p-4","bg-gray-50","dark:bg-gray-800","rounded-lg","hover:bg-gray-100","dark:hover:bg-gray-700","cursor-pointer","transition-colors","duration-200","group",3,"click","keydown.enter","keydown.space"],[1,"flex-shrink-0","mr-4"],[1,"w-8","h-8","text-gray-600","dark:text-gray-400",3,"img"],[1,"flex-1","min-w-0"],[1,"text-lg","font-medium","text-gray-900","dark:text-gray-100","truncate"],[1,"text-sm","text-gray-600","dark:text-gray-400","mt-1"],[1,"flex-shrink-0","ml-4"],["title","Delete profile",1,"p-2","text-red-600","hover:text-red-800","dark:text-red-400","dark:hover:text-red-300","hover:bg-red-100","dark:hover:bg-red-900","rounded-lg","transition-colors","duration-200","opacity-0","group-hover:opacity-100",3,"click"],[1,"w-5","h-5",3,"img"],[1,"card","text-center"],[1,"flex","justify-center","mb-4"],[1,"w-16","h-16","bg-gray-100","dark:bg-gray-800","rounded-full","flex","items-center","justify-center"],[1,"relative"],[1,"w-8","h-8","text-gray-400",3,"img"],[1,"w-4","h-4","text-gray-400","absolute","-top-1","-right-1",3,"img"],[1,"text-xl","font-bold","text-gray-900","dark:text-gray-100","mb-2"],[1,"text-gray-600","dark:text-gray-400","mb-6"],[1,"text-gray-700","dark:text-gray-300","mb-6"],[1,"btn-primary",3,"click"],[1,"w-5","h-5","mr-2",3,"img"]],template:function(i,o){if(i&1&&(u(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3),w(4,"lucide-icon",4),u(5,"h1",5),m(6," Sync Profiles "),h()(),u(7,"div",6)(8,"button",7),I("click",function(){return o.importProfiles()}),w(9,"lucide-icon",8),u(10,"span"),m(11,"Import"),h()(),u(12,"button",9),I("click",function(){return o.exportProfiles()}),w(13,"lucide-icon",8),u(14,"span"),m(15,"Export"),h()()()(),u(16,"p",10),m(17),ne(18,"async"),h()(),H(19,h8,5,2,"div",11),ne(20,"async"),Yn(21,p8,19,3,"div",0),h(),u(22,"button",12),I("click",function(){return o.addProfile()}),w(23,"lucide-icon",13),h()),i&2){let a,s;p(4),v("img",o.UsersIcon),p(5),v("img",o.UploadIcon),p(4),v("img",o.DownloadIcon),p(4),z(" ",((a=ce(18,6,o.appService.configInfo$))==null||a.profiles==null?null:a.profiles.length)||0," profile(s) configured "),p(2),B(!((s=ce(20,8,o.appService.configInfo$))==null||s.profiles==null)&&s.profiles.length?19:21),p(4),v("img",o.PlusIcon)}},dependencies:[we,vt,pn,Pe,Re],styles:[".fade-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-in-out}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}"],changeDetection:0});let e=t;return e})();function f8(e,t){if(e&1){let n=Q();u(0,"div",2)(1,"button",8),I("click",function(){b(n);let i=k();return D(i.saveProfile())}),w(2,"lucide-icon",9),m(3),ne(4,"async"),h()()}if(e&2){let n=k();p(2),v("img",n.SaveIcon),p(),z(" ",ce(4,2,n.saveBtnText$)," ")}}function y8(e,t){if(e&1&&(u(0,"option",25),m(1),h()),e&2){let n=t.$implicit;v("value",n.name),p(),z(" ",n.name," ")}}function m8(e,t){if(e&1&&(u(0,"option",25),m(1),h()),e&2){let n=t.$implicit;v("value",n.name),p(),z(" ",n.name," ")}}function g8(e,t){if(e&1&&(u(0,"div",39),w(1,"lucide-icon",47),u(2,"p"),m(3,"No include paths configured"),h(),u(4,"p",48),m(5,"All files will be included by default"),h()()),e&2){let n=k(2);p(),v("img",n.CheckIcon)}}function k8(e,t){if(e&1){let n=Q();u(0,"div",49)(1,"div",50)(2,"select",51),I("change",function(i){let o=b(n).$index,a=k(3);return D(a.onIncludePathTypeChange(o,i))}),u(3,"option",52),m(4,"File"),h(),u(5,"option",53),m(6,"Folder"),h()()(),u(7,"div",54)(8,"input",55),I("input",function(i){let o=b(n).$index,a=k(3);return D(a.onIncludePathValueChange(o,i))}),h(),u(9,"div",16),w(10,"lucide-icon",56),h()(),u(11,"button",57),I("click",function(){let i=b(n).$index,o=k(3);return D(o.removeIncludePath(i))}),w(12,"lucide-icon",38),h()()}if(e&2){let n=t.$index,r=k(3);p(2),v("value",r.getIncludePathType(n)),p(6),v("value",r.getIncludePathValue(n)),p(2),v("img",r.getIncludePathType(n)==="file"?r.FileIcon:r.FolderIcon),p(2),v("img",r.MinusIcon)}}function v8(e,t){if(e&1&&(u(0,"div",40),De(1,k8,13,4,"div",49,ys),h()),e&2){let n=k(2);p(),Se(n.profile.included_paths)}}function M8(e,t){if(e&1&&(u(0,"div",39),w(1,"lucide-icon",47),u(2,"p"),m(3,"No exclude paths configured"),h(),u(4,"p",48),m(5,"No files will be excluded"),h()()),e&2){let n=k(2);p(),v("img",n.XIcon)}}function w8(e,t){if(e&1){let n=Q();u(0,"div",49)(1,"div",50)(2,"select",51),I("change",function(i){let o=b(n).$index,a=k(3);return D(a.onExcludePathTypeChange(o,i))}),u(3,"option",52),m(4,"File"),h(),u(5,"option",53),m(6,"Folder"),h()()(),u(7,"div",54)(8,"input",55),I("input",function(i){let o=b(n).$index,a=k(3);return D(a.onExcludePathValueChange(o,i))}),h(),u(9,"div",16),w(10,"lucide-icon",56),h()(),u(11,"button",57),I("click",function(){let i=b(n).$index,o=k(3);return D(o.removeExcludePath(i))}),w(12,"lucide-icon",38),h()()}if(e&2){let n=t.$index,r=k(3);p(2),v("value",r.getExcludePathType(n)),p(6),v("value",r.getExcludePathValue(n)),p(2),v("img",r.getExcludePathType(n)==="file"?r.FileIcon:r.FolderIcon),p(2),v("img",r.MinusIcon)}}function C8(e,t){if(e&1&&(u(0,"div",40),De(1,w8,13,4,"div",49,ys),h()),e&2){let n=k(2);p(),Se(n.profile.excluded_paths)}}function x8(e,t){if(e&1&&(u(0,"option",25),m(1),h()),e&2){let n=t.$implicit;v("value",n),p(),z(" ",n," ")}}function I8(e,t){if(e&1&&(u(0,"option",25),m(1),h()),e&2){let n=t.$implicit;v("value",n),p(),z(" ",n," ")}}function b8(e,t){if(e&1){let n=Q();u(0,"div",6)(1,"div",10)(2,"div",11)(3,"h2",12),m(4," Basic Information "),h()(),u(5,"div")(6,"label",13),m(7," Profile Name "),h(),u(8,"div",14)(9,"input",15),gt("ngModelChange",function(i){b(n);let o=k();return _t(o.profile.name,i)||(o.profile.name=i),D(i)}),I("ngModelChange",function(){b(n);let i=k();return D(i.onProfileFieldChange())}),h(),u(10,"div",16),w(11,"lucide-icon",17),h()()()(),u(12,"div",10)(13,"div",11)(14,"h2",12),m(15," Path Configuration "),h(),u(16,"p",18),m(17," Configure source and destination paths "),h()(),u(18,"div",19)(19,"div")(20,"h3",20),m(21," Source Path "),h(),u(22,"div",21)(23,"div")(24,"label",22),m(25," Remote "),h(),u(26,"select",23),I("ngModelChange",function(i){b(n);let o=k();return D(o.updateFromPath(i,o.getFromPath()))}),u(27,"option",24),m(28,"Local"),h(),De(29,y8,2,2,"option",25,nt),ne(31,"async"),h()(),u(32,"div")(33,"label",26),m(34," Path "),h(),u(35,"div",14)(36,"input",27),I("ngModelChange",function(i){b(n);let o=k();return D(o.updateFromPath(o.getFromRemote(),i))}),h(),u(37,"div",16),w(38,"lucide-icon",17),h()()()()(),u(39,"div")(40,"h3",20),m(41," Destination Path "),h(),u(42,"div",21)(43,"div")(44,"label",28),m(45," Remote "),h(),u(46,"select",29),I("ngModelChange",function(i){b(n);let o=k();return D(o.updateToPath(i,o.getToPath()))}),u(47,"option",24),m(48,"Local"),h(),De(49,m8,2,2,"option",25,nt),ne(51,"async"),h()(),u(52,"div")(53,"label",30),m(54," Path "),h(),u(55,"div",14)(56,"input",31),I("ngModelChange",function(i){b(n);let o=k();return D(o.updateToPath(o.getToRemote(),i))}),h(),u(57,"div",16),w(58,"lucide-icon",17),h()()()()()()(),u(59,"div",10)(60,"div",11)(61,"h2",12),m(62," Filter Settings "),h(),u(63,"p",18),m(64," Configure which files and folders to include or exclude from sync "),h()(),u(65,"div",32)(66,"div",33)(67,"div",34),w(68,"lucide-icon",35),u(69,"h3",36),m(70," Include Paths "),h()(),u(71,"button",37),I("click",function(){b(n);let i=k();return D(i.addIncludePath())}),w(72,"lucide-icon",38),u(73,"span"),m(74,"Add Path"),h()()(),H(75,g8,6,1,"div",39),H(76,v8,3,0,"div",40),h(),u(77,"div",11)(78,"div",33)(79,"div",34),w(80,"lucide-icon",41),u(81,"h3",36),m(82," Exclude Paths "),h()(),u(83,"button",37),I("click",function(){b(n);let i=k();return D(i.addExcludePath())}),w(84,"lucide-icon",38),u(85,"span"),m(86,"Add Path"),h()()(),H(87,M8,6,1,"div",39),H(88,C8,3,0,"div",40),h()(),u(89,"div",10)(90,"div",11)(91,"h2",12),m(92," Performance Settings "),h(),u(93,"p",18),m(94," Configure parallel transfers and bandwidth limits "),h()(),u(95,"div",42)(96,"div")(97,"label",43),m(98," Parallel Transfers "),h(),u(99,"div",14)(100,"select",44),gt("ngModelChange",function(i){b(n);let o=k();return _t(o.profile.parallel,i)||(o.profile.parallel=i),D(i)}),I("ngModelChange",function(){b(n);let i=k();return D(i.onProfileFieldChange())}),De(101,x8,2,2,"option",25,nt),h(),u(103,"div",16),w(104,"lucide-icon",17),h()()(),u(105,"div")(106,"label",45),m(107," Bandwidth Limit (MB/s) "),h(),u(108,"div",14)(109,"select",46),gt("ngModelChange",function(i){b(n);let o=k();return _t(o.profile.bandwidth,i)||(o.profile.bandwidth=i),D(i)}),I("ngModelChange",function(){b(n);let i=k();return D(i.onProfileFieldChange())}),De(110,I8,2,2,"option",25,nt),h(),u(112,"div",16),w(113,"lucide-icon",17),h()()()()()()}if(e&2){let n=k();p(9),mt("ngModel",n.profile.name),p(2),v("img",n.EditIcon),p(15),v("ngModel",n.getFromRemote()),p(3),Se(ce(31,20,n.appService.remotes$)),p(7),v("ngModel",n.getFromPath()),p(2),v("img",n.FolderOpenIcon),p(8),v("ngModel",n.getToRemote()),p(3),Se(ce(51,22,n.appService.remotes$)),p(7),v("ngModel",n.getToPath()),p(2),v("img",n.FolderOpenIcon),p(10),v("img",n.CheckIcon),p(4),v("img",n.PlusIcon),p(3),B(n.profile.included_paths.length===0?75:-1),p(),B(n.profile.included_paths.length>0?76:-1),p(4),v("img",n.XIcon),p(4),v("img",n.PlusIcon),p(3),B(n.profile.excluded_paths.length===0?87:-1),p(),B(n.profile.excluded_paths.length>0?88:-1),p(12),mt("ngModel",n.profile.parallel),p(),Se(n.getNumberRange(1,32)),p(3),v("img",n.ZapIcon),p(5),mt("ngModel",n.profile.bandwidth),p(),Se(n.getNumberRange(1,100)),p(3),v("img",n.WifiIcon)}}function D8(e,t){e&1&&(u(0,"div",7)(1,"div",58)(2,"div",59),w(3,"div",60),h(),u(4,"p",61),m(5,"Loading profile..."),h()()())}var Sy=(()=>{let t=class t{constructor(r,i,o){this.appService=r,this.cdr=i,this.navigationService=o,this.Date=Date,this.subscriptions=new X,this.ArrowLeftIcon=ty,this.EditIcon=wc,this.FolderOpenIcon=yn,this.ZapIcon=xc,this.WifiIcon=xy,this.SaveIcon=gy,this.TrashIcon=wy,this.PlusIcon=mn,this.MinusIcon=uy,this.FileIcon=ay,this.FolderIcon=sy,this.CheckIcon=fc,this.XIcon=At,this.saveBtnText$=new J("Save \u2713"),this.profileIndex=0,this.profile=null,this.bandwidthOptions=Ic,this.parallelOptions=bc}ngOnInit(){this.subscriptions.add(this.navigationService.currentState$.subscribe(r=>{r.page==="profile-edit"&&(this.profileIndex=r.profileIndex)})),this.subscriptions.add(this.appService.configInfo$.subscribe(r=>{r?.profiles&&this.profileIndex<r.profiles.length&&(this.profile=r.profiles[this.profileIndex]),this.cdr.detectChanges()})),this.appService.getConfigInfo()}ngOnDestroy(){this.subscriptions.unsubscribe()}saveProfile(){this.profile&&this.appService.updateProfile(this.profileIndex,this.profile),this.appService.saveConfigInfo(),this.saveBtnText$.next("Saved ~"),setTimeout(()=>this.saveBtnText$.next("Save \u2713"),1e3),this.cdr.detectChanges()}onProfileFieldChange(){this.profile&&this.appService.updateProfile(this.profileIndex,this.profile)}goBack(){this.navigationService.navigateToProfiles()}getNumberRange(r,i){return Array.from({length:i-r+1},(o,a)=>r+a)}getFromRemote(){return this.profile?Tt(this.profile.from||"").remote:""}getFromPath(){return this.profile?Tt(this.profile.from||"").path:""}updateFromPath(r,i){this.profile&&(this.profile.from=Jr(r,i),this.appService.updateProfile(this.profileIndex,this.profile),this.cdr.detectChanges())}getToRemote(){return this.profile?Tt(this.profile.to||"").remote:""}getToPath(){return this.profile?Tt(this.profile.to||"").path:""}updateToPath(r,i){this.profile&&(this.profile.to=Jr(r,i),this.appService.updateProfile(this.profileIndex,this.profile),this.cdr.detectChanges())}addIncludePath(){this.profile&&(this.profile.included_paths=[...this.profile.included_paths,"/**"],this.appService.updateProfile(this.profileIndex,this.profile),this.cdr.detectChanges())}removeIncludePath(r){!this.profile||!oe(this.profile.included_paths,r)||(this.profile.included_paths=this.profile.included_paths.filter((i,o)=>o!==r),this.appService.updateProfile(this.profileIndex,this.profile),this.cdr.detectChanges())}getIncludePathType(r){return!this.profile||!oe(this.profile.included_paths,r)?"folder":Rt(this.profile.included_paths[r]).type}getIncludePathValue(r){return!this.profile||!oe(this.profile.included_paths,r)?"":Rt(this.profile.included_paths[r]).value}updateIncludePathType(r,i){if(!this.profile||!oe(this.profile.included_paths,r))return;let o=this.getIncludePathValue(r),a={type:i,value:o};this.profile.included_paths[r]=Pt(a),this.appService.updateProfile(this.profileIndex,this.profile),this.cdr.detectChanges()}updateIncludePathValue(r,i){if(!this.profile||!oe(this.profile.included_paths,r))return;let a={type:this.getIncludePathType(r),value:i};this.profile.included_paths[r]=Pt(a),this.appService.updateProfile(this.profileIndex,this.profile),this.cdr.detectChanges()}addExcludePath(){this.profile&&(this.profile.excluded_paths=[...this.profile.excluded_paths,"/**"],this.appService.updateProfile(this.profileIndex,this.profile),this.cdr.detectChanges())}removeExcludePath(r){!this.profile||!oe(this.profile.excluded_paths,r)||(this.profile.excluded_paths=this.profile.excluded_paths.filter((i,o)=>o!==r),this.appService.updateProfile(this.profileIndex,this.profile),this.cdr.detectChanges())}getExcludePathType(r){return!this.profile||!oe(this.profile.excluded_paths,r)?"folder":Rt(this.profile.excluded_paths[r]).type}getExcludePathValue(r){return!this.profile||!oe(this.profile.excluded_paths,r)?"":Rt(this.profile.excluded_paths[r]).value}updateExcludePathType(r,i){if(!this.profile||!oe(this.profile.excluded_paths,r))return;let o=this.getExcludePathValue(r),a={type:i,value:o};this.profile.excluded_paths[r]=Pt(a),this.appService.updateProfile(this.profileIndex,this.profile),this.cdr.detectChanges()}updateExcludePathValue(r,i){if(!this.profile||!oe(this.profile.excluded_paths,r))return;let a={type:this.getExcludePathType(r),value:i};this.profile.excluded_paths[r]=Pt(a),this.appService.updateProfile(this.profileIndex,this.profile),this.cdr.detectChanges()}onIncludePathTypeChange(r,i){let o=i.target;this.updateIncludePathType(r,o.value)}onIncludePathValueChange(r,i){let o=i.target;this.updateIncludePathValue(r,o.value)}onExcludePathTypeChange(r,i){let o=i.target;this.updateExcludePathType(r,o.value)}onExcludePathValueChange(r,i){let o=i.target;this.updateExcludePathValue(r,o.value)}trackByIndex(r){return r}};t.\u0275fac=function(i){return new(i||t)(x(Lt),x(ue),x(ei))},t.\u0275cmp=me({type:t,selectors:[["app-profile-edit"]],decls:10,vars:4,consts:[[1,"fixed","top-0","left-0","right-0","z-50","bg-white","dark:bg-gray-800","border-b","border-gray-200","dark:border-gray-700","px-4","py-3"],[1,"flex","items-center","justify-between","max-w-4xl","mx-auto"],[1,"flex","items-center","space-x-3"],["title","Back to Profiles",1,"p-2","text-gray-600","hover:text-gray-900","dark:text-gray-400","dark:hover:text-gray-100","hover:bg-gray-100","dark:hover:bg-gray-700","rounded-lg","transition-colors","duration-200","whitespace-nowrap",3,"click"],[1,"w-6","h-6",3,"img"],[1,"text-xl","font-semibold","text-gray-900","dark:text-gray-100"],[1,"pt-20","p-6","max-w-4xl","mx-auto","space-y-6"],[1,"p-6","max-w-4xl","mx-auto"],[1,"btn-primary","flex","items-center",3,"click"],[1,"w-5","h-5","mr-2",3,"img"],[1,"card"],[1,"mb-6"],[1,"text-lg","font-semibold","text-gray-900","dark:text-gray-100"],["for","profile-name-input",1,"block","text-sm","font-medium","text-gray-700","dark:text-gray-300","mb-2"],[1,"relative"],["id","profile-name-input","type","text","placeholder","Enter profile name",1,"input-field","pr-10",3,"ngModelChange","ngModel"],[1,"absolute","inset-y-0","right-0","flex","items-center","pr-3","pointer-events-none"],[1,"w-5","h-5","text-gray-400",3,"img"],[1,"text-sm","text-gray-600","dark:text-gray-400","mt-1"],[1,"space-y-8"],[1,"text-md","font-medium","text-gray-900","dark:text-gray-100","mb-4"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-4"],["for","from-remote-select",1,"block","text-sm","font-medium","text-gray-700","dark:text-gray-300","mb-2"],["id","from-remote-select",1,"select-field",3,"ngModelChange","ngModel"],["value",""],[3,"value"],["for","from-path-input",1,"block","text-sm","font-medium","text-gray-700","dark:text-gray-300","mb-2"],["id","from-path-input","type","text","placeholder","/source/path",1,"input-field","pr-10",3,"ngModelChange","ngModel"],["for","to-remote-select",1,"block","text-sm","font-medium","text-gray-700","dark:text-gray-300","mb-2"],["id","to-remote-select",1,"select-field",3,"ngModelChange","ngModel"],["for","to-path-input",1,"block","text-sm","font-medium","text-gray-700","dark:text-gray-300","mb-2"],["id","to-path-input","type","text","placeholder","/destination/path",1,"input-field","pr-10",3,"ngModelChange","ngModel"],[1,"mb-8"],[1,"flex","items-center","justify-between","mb-4"],[1,"flex","items-center","space-x-2"],[1,"w-5","h-5","text-green-600",3,"img"],[1,"text-md","font-medium","text-gray-900","dark:text-gray-100"],["type","button",1,"btn-secondary","flex","items-center","space-x-2",3,"click"],[1,"w-4","h-4",3,"img"],[1,"text-center","py-8","text-gray-500","dark:text-gray-400"],[1,"space-y-3"],[1,"w-5","h-5","text-red-600",3,"img"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-6"],["for","parallel-select",1,"block","text-sm","font-medium","text-gray-700","dark:text-gray-300","mb-2"],["id","parallel-select",1,"select-field","pr-10",3,"ngModelChange","ngModel"],["for","bandwidth-select",1,"block","text-sm","font-medium","text-gray-700","dark:text-gray-300","mb-2"],["id","bandwidth-select",1,"select-field","pr-10",3,"ngModelChange","ngModel"],[1,"w-12","h-12","mx-auto","mb-2","opacity-50",3,"img"],[1,"text-sm"],[1,"flex","items-center","space-x-3","p-3","bg-gray-50","dark:bg-gray-700","rounded-lg"],[1,"flex-shrink-0"],[1,"select-field","w-24",3,"change","value"],["value","file"],["value","folder"],[1,"flex-1","relative"],["type","text","placeholder","Enter path pattern",1,"input-field","pr-10",3,"input","value"],[1,"w-4","h-4","text-gray-400",3,"img"],["type","button","title","Remove path",1,"flex-shrink-0","p-2","text-red-600","hover:text-red-800","hover:!bg-red-100","dark:hover:bg-red-900","rounded-lg","transition-colors","duration-200",3,"click"],[1,"card","text-center"],[1,"flex","justify-center","mb-4"],[1,"animate-spin","rounded-full","h-8","w-8","border-b-2","border-primary-600"],[1,"text-gray-600","dark:text-gray-400"]],template:function(i,o){i&1&&(u(0,"div",0)(1,"div",1)(2,"div",2)(3,"button",3),I("click",function(){return o.goBack()}),w(4,"lucide-icon",4),h(),u(5,"h1",5),m(6," Edit Profile "),h()(),H(7,f8,5,4,"div",2),h()(),H(8,b8,114,24,"div",6),H(9,D8,6,0,"div",7)),i&2&&(p(4),v("img",o.ArrowLeftIcon),p(3),B(o.profile?7:-1),p(),B(o.profile?8:-1),p(),B(o.profile?-1:9))},dependencies:[we,vt,pn,Xr,Kr,hn,Zr,Gr,ar,Pe,Re],styles:[".fade-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-in-out}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}"],changeDetection:0});let e=t;return e})();var _y=[{value:"drive",label:"Google Drive",icon:"cloud"},{value:"dropbox",label:"Dropbox",icon:"cloud_queue"},{value:"onedrive",label:"OneDrive",icon:"cloud_circle"},{value:"yandex",label:"Yandex Disk",icon:"cloud_sync"},{value:"gphotos",label:"Google Photos",icon:"photo_library"},{value:"iclouddrive",label:"iCloud Drive",icon:"cloud_upload"}];function S8(e,t){if(e&1){let n=Q();u(0,"div",17)(1,"div",18),w(2,"lucide-icon",19),h(),u(3,"div",20)(4,"h3",21),m(5),h(),u(6,"p",22),m(7),h()(),u(8,"div",23)(9,"button",24),I("click",function(){let i=b(n).$implicit,o=k(2);return D(o.confirmDeleteRemote(i))}),w(10,"lucide-icon",25),h()()()}if(e&2){let n=t.$implicit,r=k(2);p(2),v("img",r.DownloadIcon),p(3),z(" ",n.name," "),p(2),z(" ",r.getRemoteTypeLabel(n.type)," "),p(3),v("img",r.Trash2Icon)}}function _8(e,t){if(e&1&&(u(0,"div",11)(1,"div",16),De(2,S8,11,4,"div",17,nt),ne(4,"async"),h()()),e&2){let n=k();p(2),Se(ce(4,0,n.appService.remotes$))}}function E8(e,t){if(e&1){let n=Q();u(0,"div",0)(1,"div",26)(2,"div",1)(3,"div",27)(4,"div",28)(5,"div",29),w(6,"lucide-icon",30)(7,"lucide-icon",31),h()()(),u(8,"h2",32),m(9," No Remotes Configured "),h(),u(10,"p",33),m(11," Add your first cloud storage connection "),h()(),u(12,"div")(13,"p",34),m(14," Remotes allow you to sync files with cloud storage services like Google Drive, Dropbox, OneDrive, and more. "),h(),u(15,"button",35),I("click",function(){b(n);let i=k();return D(i.openAddRemoteDialog())}),u(16,"div",6),w(17,"lucide-icon",36),m(18," Add First Remote "),h()()()()()}if(e&2){let n=k();p(6),v("img",n.CloudIcon),p(),v("img",n.XIcon),p(10),v("img",n.PlusIcon)}}function L8(e,t){e&1&&(u(0,"span"),m(1,"Add Remote"),h())}function A8(e,t){e&1&&(u(0,"span",53),w(1,"div",54),m(2," Adding... "),h())}function T8(e,t){if(e&1){let n=Q();u(0,"div",37),I("click",function(){b(n);let i=k();return D(i.closeAddRemoteModal())})("keyup.escape",function(){b(n);let i=k();return D(i.closeAddRemoteModal())}),u(1,"div",38),I("click",function(i){return b(n),D(i.stopPropagation())})("keydown",function(i){return b(n),D(i.stopPropagation())}),u(2,"h2",39),m(3," Add New Remote "),h(),u(4,"form",40),I("ngSubmit",function(){b(n);let i=k();return D(i.saveRemote())}),u(5,"div")(6,"label",41),m(7," Remote Name "),h(),u(8,"input",42),gt("ngModelChange",function(i){b(n);let o=k();return _t(o.addRemoteData.name,i)||(o.addRemoteData.name=i),D(i)}),h()(),u(9,"div")(10,"label",43),m(11," Remote Type "),h(),u(12,"select",44),gt("ngModelChange",function(i){b(n);let o=k();return _t(o.addRemoteData.type,i)||(o.addRemoteData.type=i),D(i)}),u(13,"option",45),m(14,"Google Drive"),h(),u(15,"option",46),m(16,"Dropbox"),h(),u(17,"option",47),m(18,"OneDrive"),h(),u(19,"option",48),m(20,"Yandex Disk"),h(),u(21,"option",49),m(22,"Google Photos"),h()()(),u(23,"div",50)(24,"button",51),I("click",function(){b(n);let i=k();return D(i.closeAddRemoteModal())}),m(25," Cancel "),h(),u(26,"button",52),ne(27,"async"),H(28,L8,2,0,"span"),ne(29,"async"),H(30,A8,3,0,"span",53),ne(31,"async"),h()()()()()}if(e&2){let n=k();p(8),mt("ngModel",n.addRemoteData.name),p(4),mt("ngModel",n.addRemoteData.type),p(14),v("disabled",!n.addRemoteData.name||!n.addRemoteData.type||ce(27,5,n.isAddingRemote$)),p(2),B(ce(29,7,n.isAddingRemote$)===!1?28:-1),p(2),B(ce(31,9,n.isAddingRemote$)?30:-1)}}function R8(e,t){if(e&1){let n=Q();u(0,"div",55),I("click",function(){b(n);let i=k();return D(i.closeDeleteConfirmModal())})("keyup.escape",function(){b(n);let i=k();return D(i.closeDeleteConfirmModal())}),u(1,"div",38),I("click",function(i){return b(n),D(i.stopPropagation())})("keydown",function(i){return b(n),D(i.stopPropagation())}),u(2,"h2",56),m(3," Confirm Delete "),h(),u(4,"div",1)(5,"p",57),m(6," Are you sure you want to delete remote "),u(7,"strong",58),m(8),h(),m(9,"? "),h(),u(10,"p",59),m(11," This action cannot be undone. "),h()(),u(12,"div",60)(13,"button",61),I("click",function(){b(n);let i=k();return D(i.closeDeleteConfirmModal())}),m(14," Cancel "),h(),u(15,"button",62),I("click",function(){b(n);let i=k();return D(i.deleteRemote())}),m(16," Delete "),h()()()()}if(e&2){let n=k();p(8),z('"',n.remoteToDelete==null?null:n.remoteToDelete.name,'"')}}var Ey=(()=>{let t=class t{constructor(r,i,o){this.appService=r,this.cdr=i,this.errorService=o,this.Date=Date,this.CloudIcon=Yr,this.PlusIcon=mn,this.DownloadIcon=fn,this.UploadIcon=kn,this.XIcon=At,this.Trash2Icon=gn,this.subscriptions=new X,this.isAddingRemote$=new J(!1),this.saveBtnText$=new J("Save \u2713"),this.remoteTypeOptions=_y,this.showAddRemoteModal=!1,this.showDeleteConfirmModal=!1,this.remoteToDelete=null,this.addRemoteData={name:"",type:"drive"}}ngOnInit(){this.subscriptions.add(oi([this.appService.configInfo$,this.appService.remotes$]).subscribe(()=>this.cdr.detectChanges())),this.appService.getConfigInfo()}ngOnDestroy(){this.subscriptions.unsubscribe()}addRemote(r){return O(this,null,function*(){if(!this.isAddingRemote$.value)try{this.isAddingRemote$.next(!0);let i=r.target,o=new FormData(i),a={};o.forEach((c,d)=>{a[d]=c.toString()}),yield this.appService.addRemote(a),i.parentElement.hidePopover()}catch(i){console.error("Error adding remote:",i),this.errorService.handleApiError(i,"add_remote_form")}finally{this.isAddingRemote$.next(!1)}})}stopAddingRemote(){this.appService.stopAddingRemote()}saveConfigInfo(){this.appService.saveConfigInfo(),this.saveBtnText$.next("Saved ~"),setTimeout(()=>this.saveBtnText$.next("Save \u2713"),1e3),this.cdr.detectChanges()}openAddRemoteDialog(){this.addRemoteData={name:"",type:"drive"},this.showAddRemoteModal=!0}closeAddRemoteModal(){this.showAddRemoteModal=!1,this.addRemoteData={name:"",type:"drive"}}saveRemote(){return O(this,null,function*(){if(this.addRemoteData.name.trim()){this.isAddingRemote$.next(!0),this.cdr.detectChanges();try{yield this.appService.addRemote({name:this.addRemoteData.name,type:this.addRemoteData.type}),console.log(`Remote "${this.addRemoteData.name}" added successfully!`),this.errorService.showSuccess(`Remote "${this.addRemoteData.name}" added successfully!`),this.closeAddRemoteModal()}catch(r){console.error("Error adding remote:",r),this.errorService.handleApiError(r,"add_remote_modal")}finally{this.isAddingRemote$.next(!1),this.cdr.detectChanges()}}})}confirmDeleteRemote(r){this.remoteToDelete=r,this.showDeleteConfirmModal=!0}closeDeleteConfirmModal(){this.showDeleteConfirmModal=!1,this.remoteToDelete=null}deleteRemote(){return O(this,null,function*(){if(this.remoteToDelete)try{yield this.appService.deleteRemote(this.remoteToDelete.name),console.log(`Remote "${this.remoteToDelete.name}" deleted successfully!`),this.errorService.showSuccess(`Remote "${this.remoteToDelete.name}" deleted successfully!`)}catch(r){console.error("Error deleting remote:",r),this.errorService.handleApiError(r,"delete_remote")}finally{this.closeDeleteConfirmModal()}})}getRemoteIcon(r){return this.remoteTypeOptions.find(o=>o.value===r)?.icon??"cloud"}getRemoteTypeLabel(r){return this.remoteTypeOptions.find(o=>o.value===r)?.label??r}exportRemotes(){return O(this,null,function*(){try{yield this.appService.exportRemotes(),this.errorService.showSuccess("Remotes exported successfully!")}catch(r){console.error("Error exporting remotes:",r),this.errorService.handleApiError(r,"export_remotes")}})}importRemotes(){return O(this,null,function*(){try{yield this.appService.importRemotes(),this.errorService.showSuccess("Remotes imported successfully!")}catch(r){console.error("Error importing remotes:",r),this.errorService.handleApiError(r,"import_remotes")}})}};t.\u0275fac=function(i){return new(i||t)(x(Lt),x(ue),x(We))},t.\u0275cmp=me({type:t,selectors:[["app-remotes"]],decls:26,vars:12,consts:[[1,"p-6","max-w-4xl","mx-auto"],[1,"mb-6"],[1,"flex","items-center","justify-between","mb-2"],[1,"flex","items-center","space-x-3"],[1,"w-6","h-6","text-primary-600",3,"img"],[1,"text-2xl","font-bold","text-gray-900","dark:text-gray-100"],[1,"flex","items-center","space-x-2"],["title","Import Remotes",1,"btn-secondary","flex","items-center","space-x-2",3,"click"],[1,"w-4","h-4",3,"img"],["title","Export Remotes",1,"btn-secondary","flex","items-center","space-x-2",3,"click"],[1,"text-gray-600","dark:text-gray-400"],[1,"card"],["title","Add Remote",1,"fixed","bottom-24","right-6","w-14","h-14","bg-primary-600","hover:bg-primary-700","text-white","rounded-full","shadow-lg","hover:shadow-xl","transition-all","duration-200","flex","items-center","justify-center","z-50",3,"click"],[1,"w-6","h-6",3,"img"],["tabindex","0","role","dialog","aria-modal","true","aria-labelledby","add-remote-title",1,"fixed","inset-0","bg-black","bg-opacity-50","flex","items-center","justify-center","z-50"],["tabindex","0","role","dialog","aria-modal","true","aria-labelledby","delete-confirm-title",1,"fixed","inset-0","bg-black","bg-opacity-50","flex","items-center","justify-center","z-50"],[1,"space-y-3"],[1,"flex","items-center","p-4","bg-gray-50","dark:bg-gray-800","rounded-lg","hover:bg-gray-100","dark:hover:bg-gray-700","transition-colors","duration-200","group"],[1,"flex-shrink-0","mr-4"],[1,"w-8","h-8","text-gray-600","dark:text-gray-400",3,"img"],[1,"flex-1","min-w-0"],[1,"text-lg","font-medium","text-gray-900","dark:text-gray-100","truncate"],[1,"text-sm","text-gray-600","dark:text-gray-400","mt-1"],[1,"flex-shrink-0","ml-4"],["title","Delete remote",1,"p-2","text-red-600","hover:text-red-800","dark:text-red-400","dark:hover:text-red-300","hover:bg-red-100","dark:hover:bg-red-900","rounded-lg","transition-colors","duration-200","opacity-0","group-hover:opacity-100",3,"click"],[1,"w-5","h-5",3,"img"],[1,"card","text-center"],[1,"flex","justify-center","mb-4"],[1,"w-16","h-16","bg-gray-100","dark:bg-gray-800","rounded-full","flex","items-center","justify-center"],[1,"relative"],[1,"w-8","h-8","text-gray-400",3,"img"],[1,"w-4","h-4","text-gray-400","absolute","-top-1","-right-1",3,"img"],[1,"text-xl","font-bold","text-gray-900","dark:text-gray-100","mb-2"],[1,"text-gray-600","dark:text-gray-400","mb-6"],[1,"text-gray-700","dark:text-gray-300","mb-6"],[1,"btn-primary",3,"click"],[1,"w-5","h-5","mr-2",3,"img"],["tabindex","0","role","dialog","aria-modal","true","aria-labelledby","add-remote-title",1,"fixed","inset-0","bg-black","bg-opacity-50","flex","items-center","justify-center","z-50",3,"click","keyup.escape"],["tabindex","-1",1,"bg-white","dark:bg-gray-800","rounded-lg","p-6","w-96","max-w-md","mx-4",3,"click","keydown"],["id","add-remote-title",1,"text-xl","font-bold","text-gray-900","dark:text-gray-100","mb-4"],[1,"space-y-4",3,"ngSubmit"],["for","remote-name-input",1,"block","text-sm","font-medium","text-gray-700","dark:text-gray-300","mb-2"],["id","remote-name-input","type","text","name","name","placeholder","Enter remote name","required","",1,"input-field",3,"ngModelChange","ngModel"],["for","remote-type-select",1,"block","text-sm","font-medium","text-gray-700","dark:text-gray-300","mb-2"],["id","remote-type-select","name","type","required","",1,"select-field",3,"ngModelChange","ngModel"],["value","drive"],["value","dropbox"],["value","onedrive"],["value","yandex"],["value","gphotos"],[1,"flex","justify-end","space-x-3","pt-4"],["type","button",1,"btn-secondary",3,"click"],["type","submit",1,"btn-primary",3,"disabled"],[1,"flex","items-center"],[1,"animate-spin","rounded-full","h-4","w-4","border-b-2","border-white","mr-2"],["tabindex","0","role","dialog","aria-modal","true","aria-labelledby","delete-confirm-title",1,"fixed","inset-0","bg-black","bg-opacity-50","flex","items-center","justify-center","z-50",3,"click","keyup.escape"],["id","delete-confirm-title",1,"text-xl","font-bold","text-gray-900","dark:text-gray-100","mb-4"],[1,"text-gray-700","dark:text-gray-300"],[1,"text-gray-900","dark:text-gray-100"],[1,"text-red-600","dark:text-red-400","text-sm","mt-2"],[1,"flex","justify-end","space-x-3"],[1,"btn-secondary",3,"click"],[1,"bg-red-600","hover:bg-red-700","text-white","font-medium","py-2","px-4","rounded-lg","transition-colors","duration-200",3,"click"]],template:function(i,o){if(i&1&&(u(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3),w(4,"lucide-icon",4),u(5,"h1",5),m(6," Connected Remotes "),h()(),u(7,"div",6)(8,"button",7),I("click",function(){return o.importRemotes()}),w(9,"lucide-icon",8),u(10,"span"),m(11,"Import"),h()(),u(12,"button",9),I("click",function(){return o.exportRemotes()}),w(13,"lucide-icon",8),u(14,"span"),m(15,"Export"),h()()()(),u(16,"p",10),m(17),ne(18,"async"),h()(),H(19,_8,5,2,"div",11),ne(20,"async"),Yn(21,E8,19,3,"div",0),h(),u(22,"button",12),I("click",function(){return o.openAddRemoteDialog()}),w(23,"lucide-icon",13),h(),H(24,T8,32,11,"div",14),H(25,R8,17,1,"div",15)),i&2){let a,s;p(4),v("img",o.CloudIcon),p(5),v("img",o.UploadIcon),p(4),v("img",o.DownloadIcon),p(4),z(" ",((a=ce(18,8,o.appService.remotes$))==null?null:a.length)||0," remote(s) configured "),p(2),B((s=ce(20,10,o.appService.remotes$))!=null&&s.length?19:21),p(4),v("img",o.PlusIcon),p(),B(o.showAddRemoteModal?24:-1),p(),B(o.showDeleteConfirmModal?25:-1)}},dependencies:[we,vt,pn,Yf,Xr,Kr,hn,Zr,Gr,Wf,uu,ar,lu,Pe,Re],styles:[".fade-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .3s ease-in-out}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}"],changeDetection:0});let e=t;return e})();function P8(e,t){if(e&1&&(u(0,"div",19),m(1),h()),e&2){let n=k(2).$implicit;p(),z(" ",n.details," ")}}function F8(e,t){if(e&1){let n=Q();u(0,"div",16)(1,"button",17),I("click",function(){b(n);let i=k().$implicit,o=k(2);return D(o.toggleDetails(i.id))}),u(2,"span"),m(3,"Details"),h(),w(4,"lucide-icon",7),h(),St(5,P8,2,1,"div",18),h()}if(e&2){let n=k().$implicit,r=k(2);p(4),v("img",r.isDetailsExpanded(n.id)?r.ChevronUpIcon:r.ChevronDownIcon)("size",16),p(),v("ngIf",r.isDetailsExpanded(n.id))}}function N8(e,t){if(e&1){let n=Q();u(0,"div",3)(1,"div",4)(2,"div",5)(3,"div",6),w(4,"lucide-icon",7),h(),u(5,"div",8)(6,"h4",9),m(7),h(),u(8,"div",10)(9,"span",11),m(10),ne(11,"date"),h(),u(12,"span",12),m(13),h()()()(),u(14,"button",13),I("click",function(){let i=b(n).$implicit,o=k(2);return D(o.dismissError(i.id))}),w(15,"lucide-icon",7),h()(),u(16,"p",14),m(17),h(),St(18,F8,6,3,"div",15),h()}if(e&2){let n=t.$implicit,r=k(2);_e(r.getErrorCardClasses(n.severity)),p(3),_e(r.getIconClasses(n.severity)),p(),v("img",r.getErrorIcon(n.severity))("size",16),p(3),z(" ",n.title," "),p(3),Ee(d1(11,15,n.timestamp,"short")),p(2),_e(r.getSeverityChipClasses(n.severity)),p(),z(" ",n.severity.toUpperCase()," "),p(2),v("img",r.XIcon)("size",16),p(2),z(" ",n.message," "),p(),v("ngIf",n.details)}}function O8(e,t){if(e&1&&(u(0,"div",1),St(1,N8,19,18,"div",2),h()),e&2){let n=k();p(),v("ngForOf",n.activeErrors)("ngForTrackBy",n.trackByErrorId)}}var Ly=(()=>{let t=class t{constructor(r,i){this.errorService=r,this.cdr=i,this.activeErrors=[],this.expandedDetails=new Set,this.AlertCircleIcon=mc,this.AlertTriangleIcon=Cy,this.InfoIcon=ly,this.XIcon=At,this.ChevronDownIcon=yc,this.ChevronUpIcon=ny}ngOnInit(){this.subscription=this.errorService.errors$.subscribe(r=>{this.activeErrors=r.filter(i=>!i.dismissed&&!i.autoHide),this.cdr.detectChanges()})}ngOnDestroy(){this.subscription?.unsubscribe()}dismissError(r){this.errorService.dismissError(r)}toggleDetails(r){this.expandedDetails.has(r)?this.expandedDetails.delete(r):this.expandedDetails.add(r),this.cdr.detectChanges()}isDetailsExpanded(r){return this.expandedDetails.has(r)}getErrorIcon(r){switch(r){case N.INFO:return this.InfoIcon;case N.WARNING:return this.AlertTriangleIcon;case N.ERROR:case N.CRITICAL:return this.AlertCircleIcon;default:return this.AlertCircleIcon}}getErrorCardClasses(r){switch(r){case N.INFO:return"border-l-blue-500";case N.WARNING:return"border-l-yellow-500";case N.ERROR:return"border-l-red-500";case N.CRITICAL:return"border-l-purple-500 shadow-purple-200 dark:shadow-purple-900";default:return"border-l-red-500"}}getIconClasses(r){switch(r){case N.INFO:return"bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300";case N.WARNING:return"bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-300";case N.ERROR:return"bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300";case N.CRITICAL:return"bg-purple-100 text-purple-600 dark:bg-purple-900 dark:text-purple-300";default:return"bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300"}}getSeverityChipClasses(r){switch(r){case N.INFO:return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case N.WARNING:return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case N.ERROR:return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";case N.CRITICAL:return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";default:return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"}}trackByErrorId(r,i){return i.id}};t.\u0275fac=function(i){return new(i||t)(x(We),x(ue))},t.\u0275cmp=me({type:t,selectors:[["app-error-display"]],decls:1,vars:1,consts:[["class","fixed top-20 right-4 w-96 max-h-[calc(100vh-100px)] overflow-y-auto z-50 space-y-2",4,"ngIf"],[1,"fixed","top-20","right-4","w-96","max-h-[calc(100vh-100px)]","overflow-y-auto","z-50","space-y-2"],["class","bg-white dark:bg-gray-800 rounded-lg shadow-lg border-l-4 p-4 animate-slide-in",3,"class",4,"ngFor","ngForOf","ngForTrackBy"],[1,"bg-white","dark:bg-gray-800","rounded-lg","shadow-lg","border-l-4","p-4","animate-slide-in"],[1,"flex","items-start","justify-between","mb-3"],[1,"flex","items-center","space-x-3"],[1,"flex-shrink-0","w-8","h-8","rounded-full","flex","items-center","justify-center"],[3,"img","size"],[1,"flex-1","min-w-0"],[1,"text-sm","font-semibold","text-gray-900","dark:text-gray-100"],[1,"flex","items-center","space-x-2","mt-1"],[1,"text-xs","text-gray-500","dark:text-gray-400"],[1,"px-2","py-1","text-xs","font-medium","rounded-full"],["aria-label","Dismiss error",1,"flex-shrink-0","ml-2","text-gray-400","hover:text-gray-600","dark:hover:text-gray-300","transition-colors",3,"click"],[1,"text-sm","text-gray-700","dark:text-gray-300","mb-3"],["class","border-t border-gray-200 dark:border-gray-600 pt-3",4,"ngIf"],[1,"border-t","border-gray-200","dark:border-gray-600","pt-3"],[1,"flex","items-center","space-x-2","text-sm","text-gray-600","dark:text-gray-400","hover:text-gray-800","dark:hover:text-gray-200","transition-colors",3,"click"],["class","mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded text-xs font-mono text-gray-600 dark:text-gray-300 whitespace-pre-wrap max-h-32 overflow-y-auto",4,"ngIf"],[1,"mt-2","p-3","bg-gray-50","dark:bg-gray-700","rounded","text-xs","font-mono","text-gray-600","dark:text-gray-300","whitespace-pre-wrap","max-h-32","overflow-y-auto"]],template:function(i,o){i&1&&St(0,O8,2,2,"div",0),i&2&&v("ngIf",o.activeErrors.length>0)},dependencies:[we,Tr,Gi,x1,Pe,Re],styles:[".animate-slide-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideIn .3s ease-out}@keyframes _ngcontent-%COMP%_slideIn{0%{transform:translate(100%);opacity:0}to{transform:translate(0);opacity:1}}@media (max-width: 768px){.fixed.top-20.right-4.w-96[_ngcontent-%COMP%]{width:calc(100vw - 2rem);right:1rem;left:1rem}}"],changeDetection:0});let e=t;return e})();function V8(e,t){if(e&1){let n=Q();u(0,"div",3)(1,"div",4)(2,"p",5),m(3),h(),u(4,"p",6),m(5),h()(),u(6,"button",7),I("click",function(){let i=b(n).$implicit,o=k(2);return D(o.dismissToast(i.id))}),w(7,"lucide-icon",8),h()()}if(e&2){let n=t.$implicit,r=k(2);_e(r.getToastClasses(n.severity)),p(3),Ee(n.title),p(2),Ee(n.message),p(2),v("img",r.XIcon)("size",16)}}function j8(e,t){if(e&1&&(u(0,"div",1),St(1,V8,8,6,"div",2),h()),e&2){let n=k();p(),v("ngForOf",n.toastNotifications)("ngForTrackBy",n.trackByNotificationId)}}var Ay=(()=>{let t=class t{constructor(r,i){this.errorService=r,this.cdr=i,this.toastNotifications=[],this.XIcon=At}ngOnInit(){this.subscription=this.errorService.errors$.subscribe(r=>{this.toastNotifications=r.filter(i=>!i.dismissed&&i.autoHide),this.toastNotifications.forEach(i=>{i.duration&&i.duration>0&&setTimeout(()=>{this.dismissToast(i.id)},i.duration)}),this.cdr.detectChanges()})}ngOnDestroy(){this.subscription?.unsubscribe()}dismissToast(r){this.errorService.dismissError(r)}getToastClasses(r){switch(r){case N.INFO:return"bg-blue-500";case N.WARNING:return"bg-yellow-500";case N.ERROR:return"bg-red-500";case N.CRITICAL:return"bg-purple-500";default:return"bg-gray-500"}}trackByNotificationId(r,i){return i.id}};t.\u0275fac=function(i){return new(i||t)(x(We),x(ue))},t.\u0275cmp=me({type:t,selectors:[["app-toast"]],decls:1,vars:1,consts:[["class","fixed top-4 right-4 z-50 space-y-2 w-80",4,"ngIf"],[1,"fixed","top-4","right-4","z-50","space-y-2","w-80"],["class","flex items-center p-4 rounded-lg shadow-lg animate-slide-in",3,"class",4,"ngFor","ngForOf","ngForTrackBy"],[1,"flex","items-center","p-4","rounded-lg","shadow-lg","animate-slide-in"],[1,"flex-1","min-w-0"],[1,"text-sm","font-medium","text-white"],[1,"text-sm","text-white/90"],["aria-label","Dismiss notification",1,"ml-3","text-white/70","hover:text-white","transition-colors",3,"click"],[3,"img","size"]],template:function(i,o){i&1&&St(0,j8,2,2,"div",0),i&2&&v("ngIf",o.toastNotifications.length>0)},dependencies:[we,Tr,Gi,Pe,Re],styles:[".animate-slide-in[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideIn .3s ease-out}@keyframes _ngcontent-%COMP%_slideIn{0%{transform:translate(100%);opacity:0}to{transform:translate(0);opacity:1}}@media (max-width: 768px){.fixed.top-4.right-4.w-80[_ngcontent-%COMP%]{width:calc(100vw - 2rem);right:1rem;left:1rem}}"],changeDetection:0});let e=t;return e})();function H8(e,t){e&1&&(u(0,"div",2),w(1,"app-home"),h())}function B8(e,t){e&1&&w(0,"app-profiles")}function q8(e,t){e&1&&w(0,"app-profile-edit")}function z8(e,t){if(e&1&&(u(0,"div",2),H(1,B8,1,0,"app-profiles"),ne(2,"async"),H(3,q8,1,0,"app-profile-edit"),ne(4,"async"),h()),e&2){let n,r,i=k();p(),B(((n=ce(2,2,i.navigationService.currentState$))==null?null:n.page)==="profiles"?1:-1),p(2),B(((r=ce(4,4,i.navigationService.currentState$))==null?null:r.page)==="profile-edit"?3:-1)}}function U8(e,t){e&1&&(u(0,"div",2),w(1,"app-remotes"),h())}var Ty=(()=>{let t=class t{constructor(r,i,o){this.appService=r,this.cdr=i,this.navigationService=o,this.Action=W,this.HomeIcon=dy,this.UsersIcon=Cc,this.CloudIcon=Yr,this.SettingsIcon=Mc,this.subscriptions=new X,this.isInitialized=!1,console.log("AppComponent constructor called"),console.log("AppComponent navigationService initial state:",this.navigationService.currentState)}ngOnInit(){if(console.log("AppComponent ngOnInit called, isInitialized:",this.isInitialized),this.isInitialized){console.warn("AppComponent already initialized, skipping");return}this.isInitialized=!0,this.subscriptions.add(oi([this.appService.currentAction$,this.navigationService.currentState$]).subscribe(()=>{console.log("AppComponent combineLatest triggered"),this.cdr.detectChanges()}))}ngOnDestroy(){console.log("AppComponent ngOnDestroy called"),this.isInitialized=!1,this.subscriptions.unsubscribe()}pull(r){return O(this,null,function*(){this.navigationService.navigateToHome(),this.appService.pull(r)})}push(r){return O(this,null,function*(){this.navigationService.navigateToHome(),this.appService.push(r)})}bi(r){return O(this,null,function*(){this.navigationService.navigateToHome(),this.appService.bi(r)})}stopCommand(){this.appService.stopCommand()}openHome(){this.navigationService.navigateToHome()}openProfiles(){this.navigationService.navigateToProfiles()}openRemotes(){this.navigationService.navigateToRemotes()}getSelectedTabIndex(){switch(this.navigationService.currentState.page){case"home":return 0;case"profiles":case"profile-edit":return 1;case"remotes":return 2;default:return 0}}onTabChange(r){switch(console.log("AppComponent onTabChange called with index:",r),r){case 0:this.navigationService.navigateToHome();break;case 1:this.navigationService.navigateToProfiles();break;case 2:this.navigationService.navigateToRemotes();break}}};t.\u0275fac=function(i){return new(i||t)(x(Lt),x(ue),x(ei))},t.\u0275cmp=me({type:t,selectors:[["app-root"]],decls:21,vars:12,consts:[[1,"flex","flex-col","h-screen","bg-gray-50","dark:bg-gray-900"],[1,"flex-1","overflow-hidden"],[1,"h-full","overflow-auto"],[1,"bg-white","dark:bg-gray-800","border-t","border-gray-200","dark:border-gray-700","px-4","py-2"],[1,"flex","justify-around"],[3,"click"],[1,"w-6","h-6","mb-1",3,"img"]],template:function(i,o){i&1&&(u(0,"div",0)(1,"div",1),H(2,H8,2,0,"div",2),H(3,z8,5,6,"div",2),H(4,U8,2,0,"div",2),h(),u(5,"nav",3)(6,"div",4)(7,"button",5),I("click",function(){return o.openHome()}),w(8,"lucide-icon",6),u(9,"span"),m(10,"Operations"),h()(),u(11,"button",5),I("click",function(){return o.openProfiles()}),w(12,"lucide-icon",6),u(13,"span"),m(14,"Profiles"),h()(),u(15,"button",5),I("click",function(){return o.openRemotes()}),w(16,"lucide-icon",6),u(17,"span"),m(18,"Remotes"),h()()()()(),w(19,"app-error-display")(20,"app-toast")),i&2&&(p(2),B(o.getSelectedTabIndex()===0?2:-1),p(),B(o.getSelectedTabIndex()===1?3:-1),p(),B(o.getSelectedTabIndex()===2?4:-1),p(3),_e(o.getSelectedTabIndex()===0?"bottom-nav-item-active":"bottom-nav-item"),p(),v("img",o.HomeIcon),p(3),_e(o.getSelectedTabIndex()===1?"bottom-nav-item-active":"bottom-nav-item"),p(),v("img",o.UsersIcon),p(3),_e(o.getSelectedTabIndex()===2?"bottom-nav-item-active":"bottom-nav-item"),p(),v("img",o.CloudIcon))},dependencies:[we,vt,by,Dy,Sy,Ey,Ly,Ay,Pe,Re],encapsulation:2,changeDetection:0});let e=t;return e})();A1(Ty,ef).catch(e=>console.error(e));
