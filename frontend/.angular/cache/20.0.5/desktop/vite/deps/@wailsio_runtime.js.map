{"version": 3, "sources": ["../../../../../../node_modules/@wailsio/runtime/dist/nanoid.js", "../../../../../../node_modules/@wailsio/runtime/dist/runtime.js", "../../../../../../node_modules/@wailsio/runtime/dist/system.js", "../../../../../../node_modules/@wailsio/runtime/dist/utils.js", "../../../../../../node_modules/@wailsio/runtime/dist/contextmenu.js", "../../../../../../node_modules/@wailsio/runtime/dist/flags.js", "../../../../../../node_modules/@wailsio/runtime/dist/drag.js", "../../../../../../node_modules/@wailsio/runtime/dist/application.js", "../../../../../../node_modules/@wailsio/runtime/dist/browser.js", "../../../../../../node_modules/@wailsio/runtime/dist/calls.js", "../../../../../../node_modules/@wailsio/runtime/dist/callable.js", "../../../../../../node_modules/@wailsio/runtime/dist/cancellable.js", "../../../../../../node_modules/@wailsio/runtime/dist/clipboard.js", "../../../../../../node_modules/@wailsio/runtime/dist/create.js", "../../../../../../node_modules/@wailsio/runtime/dist/dialogs.js", "../../../../../../node_modules/@wailsio/runtime/dist/events.js", "../../../../../../node_modules/@wailsio/runtime/dist/listener.js", "../../../../../../node_modules/@wailsio/runtime/dist/event_types.js", "../../../../../../node_modules/@wailsio/runtime/dist/screens.js", "../../../../../../node_modules/@wailsio/runtime/dist/window.js", "../../../../../../node_modules/@wailsio/runtime/dist/wml.js", "../../../../../../node_modules/@wailsio/runtime/dist/index.js"], "sourcesContent": ["// Source: https://github.com/ai/nanoid\n// The MIT License (MIT)\n//\n// Copyright 2017 <PERSON><PERSON> <<EMAIL>>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy of\n// this software and associated documentation files (the \"Software\"), to deal in\n// the Software without restriction, including without limitation the rights to\n// use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\n// the Software, and to permit persons to whom the Software is furnished to do so,\n//     subject to the following conditions:\n//\n//     The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n//\n//     THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\n// FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\n// IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n// CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n// This alphabet uses `A-Za-z0-9_-` symbols.\n// The order of characters is optimized for better gzip and brotli compression.\n// References to the same file (works both for gzip and brotli):\n// `'use`, `andom`, and `rict'`\n// References to the brotli default dictionary:\n// `-26T`, `1983`, `40px`, `75px`, `bush`, `jack`, `mind`, `very`, and `wolf`\nconst urlAlphabet = 'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict';\nexport function nanoid(size = 21) {\n    let id = '';\n    // A compact alternative for `for (var i = 0; i < step; i++)`.\n    let i = size | 0;\n    while (i--) {\n        // `| 0` is more compact and faster than `Math.floor()`.\n        id += urlAlphabet[(Math.random() * 64) | 0];\n    }\n    return id;\n}\n", "/*\n _     __     _ __\n| |  / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) <PERSON> 2019-present\n*/\nimport { nanoid } from './nanoid.js';\nconst runtimeURL = window.location.origin + \"/wails/runtime\";\n// Object Names\nexport const objectNames = Object.freeze({\n    Call: 0,\n    Clipboard: 1,\n    Application: 2,\n    Events: 3,\n    ContextMenu: 4,\n    Dialog: 5,\n    Window: 6,\n    Screens: 7,\n    System: 8,\n    Browser: 9,\n    CancelCall: 10,\n});\nexport let clientId = nanoid();\n/**\n * Creates a new runtime caller with specified ID.\n *\n * @param object - The object to invoke the method on.\n * @param windowName - The name of the window.\n * @return The new runtime caller function.\n */\nexport function newRuntimeCaller(object, windowName = '') {\n    return function (method, args = null) {\n        return runtimeCallWithID(object, method, windowName, args);\n    };\n}\nasync function runtimeCallWithID(objectID, method, windowName, args) {\n    var _a, _b;\n    let url = new URL(runtimeURL);\n    url.searchParams.append(\"object\", objectID.toString());\n    url.searchParams.append(\"method\", method.toString());\n    if (args) {\n        url.searchParams.append(\"args\", JSON.stringify(args));\n    }\n    let headers = {\n        [\"x-wails-client-id\"]: clientId\n    };\n    if (windowName) {\n        headers[\"x-wails-window-name\"] = windowName;\n    }\n    let response = await fetch(url, { headers });\n    if (!response.ok) {\n        throw new Error(await response.text());\n    }\n    if (((_b = (_a = response.headers.get(\"Content-Type\")) === null || _a === void 0 ? void 0 : _a.indexOf(\"application/json\")) !== null && _b !== void 0 ? _b : -1) !== -1) {\n        return response.json();\n    }\n    else {\n        return response.text();\n    }\n}\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) <PERSON> Anthony 2019-present\n*/\nimport { newRuntimeCaller, objectNames } from \"./runtime.js\";\nconst call = newRuntimeCaller(objectNames.System);\nconst SystemIsDarkMode = 0;\nconst SystemEnvironment = 1;\nconst _invoke = (function () {\n    var _a, _b, _c, _d, _e;\n    try {\n        if ((_b = (_a = window.chrome) === null || _a === void 0 ? void 0 : _a.webview) === null || _b === void 0 ? void 0 : _b.postMessage) {\n            return window.chrome.webview.postMessage.bind(window.chrome.webview);\n        }\n        else if ((_e = (_d = (_c = window.webkit) === null || _c === void 0 ? void 0 : _c.messageHandlers) === null || _d === void 0 ? void 0 : _d['external']) === null || _e === void 0 ? void 0 : _e.postMessage) {\n            return window.webkit.messageHandlers['external'].postMessage.bind(window.webkit.messageHandlers['external']);\n        }\n    }\n    catch (e) { }\n    console.warn('\\n%c⚠️ Browser Environment Detected %c\\n\\n%cOnly UI previews are available in the browser. For full functionality, please run the application in desktop mode.\\nMore information at: https://v3.wails.io/learn/build/#using-a-browser-for-development\\n', 'background: #ffffff; color: #000000; font-weight: bold; padding: 4px 8px; border-radius: 4px; border: 2px solid #000000;', 'background: transparent;', 'color: #ffffff; font-style: italic; font-weight: bold;');\n    return null;\n})();\nexport function invoke(msg) {\n    _invoke === null || _invoke === void 0 ? void 0 : _invoke(msg);\n}\n/**\n * Retrieves the system dark mode status.\n *\n * @returns A promise that resolves to a boolean value indicating if the system is in dark mode.\n */\nexport function IsDarkMode() {\n    return call(SystemIsDarkMode);\n}\n/**\n * Fetches the capabilities of the application from the server.\n *\n * @returns A promise that resolves to an object containing the capabilities.\n */\nexport async function Capabilities() {\n    let response = await fetch(\"/wails/capabilities\");\n    if (response.ok) {\n        return response.json();\n    }\n    else {\n        throw new Error(\"could not fetch capabilities: \" + response.statusText);\n    }\n}\n/**\n * Retrieves environment details.\n *\n * @returns A promise that resolves to an object containing OS and system architecture.\n */\nexport function Environment() {\n    return call(SystemEnvironment);\n}\n/**\n * Checks if the current operating system is Windows.\n *\n * @return True if the operating system is Windows, otherwise false.\n */\nexport function IsWindows() {\n    return window._wails.environment.OS === \"windows\";\n}\n/**\n * Checks if the current operating system is Linux.\n *\n * @returns Returns true if the current operating system is Linux, false otherwise.\n */\nexport function IsLinux() {\n    return window._wails.environment.OS === \"linux\";\n}\n/**\n * Checks if the current environment is a macOS operating system.\n *\n * @returns True if the environment is macOS, false otherwise.\n */\nexport function IsMac() {\n    return window._wails.environment.OS === \"darwin\";\n}\n/**\n * Checks if the current environment architecture is AMD64.\n *\n * @returns True if the current environment architecture is AMD64, false otherwise.\n */\nexport function IsAMD64() {\n    return window._wails.environment.Arch === \"amd64\";\n}\n/**\n * Checks if the current architecture is ARM.\n *\n * @returns True if the current architecture is ARM, false otherwise.\n */\nexport function IsARM() {\n    return window._wails.environment.Arch === \"arm\";\n}\n/**\n * Checks if the current environment is ARM64 architecture.\n *\n * @returns Returns true if the environment is ARM64 architecture, otherwise returns false.\n */\nexport function IsARM64() {\n    return window._wails.environment.Arch === \"arm64\";\n}\n/**\n * Reports whether the app is being run in debug mode.\n *\n * @returns True if the app is being run in debug mode.\n */\nexport function IsDebug() {\n    return Boolean(window._wails.environment.Debug);\n}\n", "/*\n _     __     _ __\n| |  / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) <PERSON> Anthony 2019-present\n*/\n/**\n * Logs a message to the console with custom formatting.\n *\n * @param message - The message to be logged.\n */\nexport function debugLog(message) {\n    // eslint-disable-next-line\n    console.log('%c wails3 %c ' + message + ' ', 'background: #aa0000; color: #fff; border-radius: 3px 0px 0px 3px; padding: 1px; font-size: 0.7rem', 'background: #009900; color: #fff; border-radius: 0px 3px 3px 0px; padding: 1px; font-size: 0.7rem');\n}\n/**\n * Checks whether the webview supports the {@link MouseEvent#buttons} property.\n * Looking at you macOS High Sierra!\n */\nexport function canTrackButtons() {\n    return (new MouseEvent('mousedown')).buttons === 0;\n}\n/**\n * Checks whether the browser supports removing listeners by triggering an AbortSignal\n * (see https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#signal).\n */\nexport function canAbortListeners() {\n    if (!EventTarget || !AbortSignal || !AbortController)\n        return false;\n    let result = true;\n    const target = new EventTarget();\n    const controller = new AbortController();\n    target.addEventListener('test', () => { result = false; }, { signal: controller.signal });\n    controller.abort();\n    target.dispatchEvent(new CustomEvent('test'));\n    return result;\n}\n/**\n * Resolves the closest HTMLElement ancestor of an event's target.\n */\nexport function eventTarget(event) {\n    var _a;\n    if (event.target instanceof HTMLElement) {\n        return event.target;\n    }\n    else if (!(event.target instanceof HTMLElement) && event.target instanceof Node) {\n        return (_a = event.target.parentElement) !== null && _a !== void 0 ? _a : document.body;\n    }\n    else {\n        return document.body;\n    }\n}\n/***\n This technique for proper load detection is taken from HTMX:\n\n BSD 2-Clause License\n\n Copyright (c) 2020, Big Sky Software\n All rights reserved.\n\n Redistribution and use in source and binary forms, with or without\n modification, are permitted provided that the following conditions are met:\n\n 1. Redistributions of source code must retain the above copyright notice, this\n list of conditions and the following disclaimer.\n\n 2. Redistributions in binary form must reproduce the above copyright notice,\n this list of conditions and the following disclaimer in the documentation\n and/or other materials provided with the distribution.\n\n THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\n FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL\n DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\n SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\n CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\n OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\n ***/\nlet isReady = false;\ndocument.addEventListener('DOMContentLoaded', () => { isReady = true; });\nexport function whenReady(callback) {\n    if (isReady || document.readyState === 'complete') {\n        callback();\n    }\n    else {\n        document.addEventListener('DOMContentLoaded', callback);\n    }\n}\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) Lea Anthony 2019-present\n*/\nimport { newRuntimeCaller, objectNames } from \"./runtime.js\";\nimport { IsDebug } from \"./system.js\";\nimport { eventTarget } from \"./utils\";\n// setup\nwindow.addEventListener('contextmenu', contextMenuHandler);\nconst call = newRuntimeCaller(objectNames.ContextMenu);\nconst ContextMenuOpen = 0;\nfunction openContextMenu(id, x, y, data) {\n    void call(ContextMenuOpen, { id, x, y, data });\n}\nfunction contextMenuHandler(event) {\n    const target = eventTarget(event);\n    // Check for custom context menu\n    const customContextMenu = window.getComputedStyle(target).getPropertyValue(\"--custom-contextmenu\").trim();\n    if (customContextMenu) {\n        event.preventDefault();\n        const data = window.getComputedStyle(target).getPropertyValue(\"--custom-contextmenu-data\");\n        openContextMenu(customContextMenu, event.clientX, event.clientY, data);\n    }\n    else {\n        processDefaultContextMenu(event, target);\n    }\n}\n/*\n--default-contextmenu: auto; (default) will show the default context menu if contentEditable is true OR text has been selected OR element is input or textarea\n--default-contextmenu: show; will always show the default context menu\n--default-contextmenu: hide; will always hide the default context menu\n\nThis rule is inherited like normal CSS rules, so nesting works as expected\n*/\nfunction processDefaultContextMenu(event, target) {\n    // Debug builds always show the menu\n    if (IsDebug()) {\n        return;\n    }\n    // Process default context menu\n    switch (window.getComputedStyle(target).getPropertyValue(\"--default-contextmenu\").trim()) {\n        case 'show':\n            return;\n        case 'hide':\n            event.preventDefault();\n            return;\n    }\n    // Check if contentEditable is true\n    if (target.isContentEditable) {\n        return;\n    }\n    // Check if text has been selected\n    const selection = window.getSelection();\n    const hasSelection = selection && selection.toString().length > 0;\n    if (hasSelection) {\n        for (let i = 0; i < selection.rangeCount; i++) {\n            const range = selection.getRangeAt(i);\n            const rects = range.getClientRects();\n            for (let j = 0; j < rects.length; j++) {\n                const rect = rects[j];\n                if (document.elementFromPoint(rect.left, rect.top) === target) {\n                    return;\n                }\n            }\n        }\n    }\n    // Check if tag is input or textarea.\n    if (target instanceof HTMLInputElement || target instanceof HTMLTextAreaElement) {\n        if (hasSelection || (!target.readOnly && !target.disabled)) {\n            return;\n        }\n    }\n    // hide default context menu\n    event.preventDefault();\n}\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) <PERSON> 2019-present\n*/\n/**\n * Retrieves the value associated with the specified key from the flag map.\n *\n * @param key - The key to retrieve the value for.\n * @return The value associated with the specified key.\n */\nexport function GetFlag(key) {\n    try {\n        return window._wails.flags[key];\n    }\n    catch (e) {\n        throw new Error(\"Unable to retrieve flag '\" + key + \"': \" + e, { cause: e });\n    }\n}\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) <PERSON> Anthony 2019-present\n*/\nimport { invoke, IsWindows } from \"./system.js\";\nimport { GetFlag } from \"./flags.js\";\nimport { canTrackButtons, eventTarget } from \"./utils.js\";\n// Setup\nlet canDrag = false;\nlet dragging = false;\nlet resizable = false;\nlet canResize = false;\nlet resizing = false;\nlet resizeEdge = \"\";\nlet defaultCursor = \"auto\";\nlet buttons = 0;\nconst buttonsTracked = canTrackButtons();\nwindow._wails = window._wails || {};\nwindow._wails.setResizable = (value) => {\n    resizable = value;\n    if (!resizable) {\n        // Stop resizing if in progress.\n        canResize = resizing = false;\n        setResize();\n    }\n};\nwindow.addEventListener('mousedown', update, { capture: true });\nwindow.addEventListener('mousemove', update, { capture: true });\nwindow.addEventListener('mouseup', update, { capture: true });\nfor (const ev of ['click', 'contextmenu', 'dblclick']) {\n    window.addEventListener(ev, suppressEvent, { capture: true });\n}\nfunction suppressEvent(event) {\n    // Suppress click events while resizing or dragging.\n    if (dragging || resizing) {\n        event.stopImmediatePropagation();\n        event.stopPropagation();\n        event.preventDefault();\n    }\n}\n// Use constants to avoid comparing strings multiple times.\nconst MouseDown = 0;\nconst MouseUp = 1;\nconst MouseMove = 2;\nfunction update(event) {\n    // Windows suppresses mouse events at the end of dragging or resizing,\n    // so we need to be smart and synthesize button events.\n    let eventType, eventButtons = event.buttons;\n    switch (event.type) {\n        case 'mousedown':\n            eventType = MouseDown;\n            if (!buttonsTracked) {\n                eventButtons = buttons | (1 << event.button);\n            }\n            break;\n        case 'mouseup':\n            eventType = MouseUp;\n            if (!buttonsTracked) {\n                eventButtons = buttons & ~(1 << event.button);\n            }\n            break;\n        default:\n            eventType = MouseMove;\n            if (!buttonsTracked) {\n                eventButtons = buttons;\n            }\n            break;\n    }\n    let released = buttons & ~eventButtons;\n    let pressed = eventButtons & ~buttons;\n    buttons = eventButtons;\n    // Synthesize a release-press sequence if we detect a press of an already pressed button.\n    if (eventType === MouseDown && !(pressed & event.button)) {\n        released |= (1 << event.button);\n        pressed |= (1 << event.button);\n    }\n    // Suppress all button events during dragging and resizing,\n    // unless this is a mouseup event that is ending a drag action.\n    if (eventType !== MouseMove // Fast path for mousemove\n        && resizing\n        || (dragging\n            && (eventType === MouseDown\n                || event.button !== 0))) {\n        event.stopImmediatePropagation();\n        event.stopPropagation();\n        event.preventDefault();\n    }\n    // Handle releases\n    if (released & 1) {\n        primaryUp(event);\n    }\n    // Handle presses\n    if (pressed & 1) {\n        primaryDown(event);\n    }\n    // Handle mousemove\n    if (eventType === MouseMove) {\n        onMouseMove(event);\n    }\n    ;\n}\nfunction primaryDown(event) {\n    // Reset readiness state.\n    canDrag = false;\n    canResize = false;\n    // Ignore repeated clicks on macOS and Linux.\n    if (!IsWindows()) {\n        if (event.type === 'mousedown' && event.button === 0 && event.detail !== 1) {\n            return;\n        }\n    }\n    if (resizeEdge) {\n        // Ready to resize if the primary button was pressed for the first time.\n        canResize = true;\n        // Do not start drag operations when on resize edges.\n        return;\n    }\n    // Retrieve target element\n    const target = eventTarget(event);\n    // Ready to drag if the primary button was pressed for the first time on a draggable element.\n    // Ignore clicks on the scrollbar.\n    const style = window.getComputedStyle(target);\n    canDrag = (style.getPropertyValue(\"--wails-draggable\").trim() === \"drag\"\n        && (event.offsetX - parseFloat(style.paddingLeft) < target.clientWidth\n            && event.offsetY - parseFloat(style.paddingTop) < target.clientHeight));\n}\nfunction primaryUp(event) {\n    // Stop dragging and resizing.\n    canDrag = false;\n    dragging = false;\n    canResize = false;\n    resizing = false;\n}\nconst cursorForEdge = Object.freeze({\n    \"se-resize\": \"nwse-resize\",\n    \"sw-resize\": \"nesw-resize\",\n    \"nw-resize\": \"nwse-resize\",\n    \"ne-resize\": \"nesw-resize\",\n    \"w-resize\": \"ew-resize\",\n    \"n-resize\": \"ns-resize\",\n    \"s-resize\": \"ns-resize\",\n    \"e-resize\": \"ew-resize\",\n});\nfunction setResize(edge) {\n    if (edge) {\n        if (!resizeEdge) {\n            defaultCursor = document.body.style.cursor;\n        }\n        document.body.style.cursor = cursorForEdge[edge];\n    }\n    else if (!edge && resizeEdge) {\n        document.body.style.cursor = defaultCursor;\n    }\n    resizeEdge = edge || \"\";\n}\nfunction onMouseMove(event) {\n    if (canResize && resizeEdge) {\n        // Start resizing.\n        resizing = true;\n        invoke(\"wails:resize:\" + resizeEdge);\n    }\n    else if (canDrag) {\n        // Start dragging.\n        dragging = true;\n        invoke(\"wails:drag\");\n    }\n    if (dragging || resizing) {\n        // Either drag or resize is ongoing,\n        // reset readiness and stop processing.\n        canDrag = canResize = false;\n        return;\n    }\n    if (!resizable || !IsWindows()) {\n        if (resizeEdge) {\n            setResize();\n        }\n        return;\n    }\n    const resizeHandleHeight = GetFlag(\"system.resizeHandleHeight\") || 5;\n    const resizeHandleWidth = GetFlag(\"system.resizeHandleWidth\") || 5;\n    // Extra pixels for the corner areas.\n    const cornerExtra = GetFlag(\"resizeCornerExtra\") || 10;\n    const rightBorder = (window.outerWidth - event.clientX) < resizeHandleWidth;\n    const leftBorder = event.clientX < resizeHandleWidth;\n    const topBorder = event.clientY < resizeHandleHeight;\n    const bottomBorder = (window.outerHeight - event.clientY) < resizeHandleHeight;\n    // Adjust for corner areas.\n    const rightCorner = (window.outerWidth - event.clientX) < (resizeHandleWidth + cornerExtra);\n    const leftCorner = event.clientX < (resizeHandleWidth + cornerExtra);\n    const topCorner = event.clientY < (resizeHandleHeight + cornerExtra);\n    const bottomCorner = (window.outerHeight - event.clientY) < (resizeHandleHeight + cornerExtra);\n    if (!leftCorner && !topCorner && !bottomCorner && !rightCorner) {\n        // Optimisation: out of all corner areas implies out of borders.\n        setResize();\n    }\n    // Detect corners.\n    else if (rightCorner && bottomCorner)\n        setResize(\"se-resize\");\n    else if (leftCorner && bottomCorner)\n        setResize(\"sw-resize\");\n    else if (leftCorner && topCorner)\n        setResize(\"nw-resize\");\n    else if (topCorner && rightCorner)\n        setResize(\"ne-resize\");\n    // Detect borders.\n    else if (leftBorder)\n        setResize(\"w-resize\");\n    else if (topBorder)\n        setResize(\"n-resize\");\n    else if (bottomBorder)\n        setResize(\"s-resize\");\n    else if (rightBorder)\n        setResize(\"e-resize\");\n    // Out of border area.\n    else\n        setResize();\n}\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) <PERSON> Anthony 2019-present\n*/\nimport { newRuntimeCaller, objectNames } from \"./runtime.js\";\nconst call = newRuntimeCaller(objectNames.Application);\nconst HideMethod = 0;\nconst ShowMethod = 1;\nconst QuitMethod = 2;\n/**\n * Hides a certain method by calling the HideMethod function.\n */\nexport function Hide() {\n    return call(HideMethod);\n}\n/**\n * Calls the ShowMethod and returns the result.\n */\nexport function Show() {\n    return call(ShowMethod);\n}\n/**\n * Calls the QuitMethod to terminate the program.\n */\nexport function Quit() {\n    return call(QuitMethod);\n}\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) Lea Anthony 2019-present\n*/\nimport { newRuntimeCaller, objectNames } from \"./runtime.js\";\nconst call = newRuntimeCaller(objectNames.Browser);\nconst BrowserOpenURL = 0;\n/**\n * Open a browser window to the given URL.\n *\n * @param url - The URL to open\n */\nexport function OpenURL(url) {\n    return call(BrowserOpenURL, { url: url.toString() });\n}\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) Lea Anthony 2019-present\n*/\nimport { CancellablePromise } from \"./cancellable.js\";\nimport { newRuntimeCaller, objectNames } from \"./runtime.js\";\nimport { nanoid } from \"./nanoid.js\";\n// Setup\nwindow._wails = window._wails || {};\nwindow._wails.callResultHandler = resultHandler;\nwindow._wails.callErrorHandler = errorHandler;\nconst call = newRuntimeCaller(objectNames.Call);\nconst cancelCall = newRuntimeCaller(objectNames.CancelCall);\nconst callResponses = new Map();\nconst CallBinding = 0;\nconst CancelMethod = 0;\n/**\n * Exception class that will be thrown in case the bound method returns an error.\n * The value of the {@link RuntimeError#name} property is \"RuntimeError\".\n */\nexport class RuntimeError extends Error {\n    /**\n     * Constructs a new RuntimeError instance.\n     * @param message - The error message.\n     * @param options - Options to be forwarded to the Error constructor.\n     */\n    constructor(message, options) {\n        super(message, options);\n        this.name = \"RuntimeError\";\n    }\n}\n/**\n * Handles the result of a call request.\n *\n * @param id - The id of the request to handle the result for.\n * @param data - The result data of the request.\n * @param isJSON - Indicates whether the data is JSON or not.\n */\nfunction resultHandler(id, data, isJSON) {\n    const resolvers = getAndDeleteResponse(id);\n    if (!resolvers) {\n        return;\n    }\n    if (!data) {\n        resolvers.resolve(undefined);\n    }\n    else if (!isJSON) {\n        resolvers.resolve(data);\n    }\n    else {\n        try {\n            resolvers.resolve(JSON.parse(data));\n        }\n        catch (err) {\n            resolvers.reject(new TypeError(\"could not parse result: \" + err.message, { cause: err }));\n        }\n    }\n}\n/**\n * Handles the error from a call request.\n *\n * @param id - The id of the promise handler.\n * @param data - The error data to reject the promise handler with.\n * @param isJSON - Indicates whether the data is JSON or not.\n */\nfunction errorHandler(id, data, isJSON) {\n    const resolvers = getAndDeleteResponse(id);\n    if (!resolvers) {\n        return;\n    }\n    if (!isJSON) {\n        resolvers.reject(new Error(data));\n    }\n    else {\n        let error;\n        try {\n            error = JSON.parse(data);\n        }\n        catch (err) {\n            resolvers.reject(new TypeError(\"could not parse error: \" + err.message, { cause: err }));\n            return;\n        }\n        let options = {};\n        if (error.cause) {\n            options.cause = error.cause;\n        }\n        let exception;\n        switch (error.kind) {\n            case \"ReferenceError\":\n                exception = new ReferenceError(error.message, options);\n                break;\n            case \"TypeError\":\n                exception = new TypeError(error.message, options);\n                break;\n            case \"RuntimeError\":\n                exception = new RuntimeError(error.message, options);\n                break;\n            default:\n                exception = new Error(error.message, options);\n                break;\n        }\n        resolvers.reject(exception);\n    }\n}\n/**\n * Retrieves and removes the response associated with the given ID from the callResponses map.\n *\n * @param id - The ID of the response to be retrieved and removed.\n * @returns The response object associated with the given ID, if any.\n */\nfunction getAndDeleteResponse(id) {\n    const response = callResponses.get(id);\n    callResponses.delete(id);\n    return response;\n}\n/**\n * Generates a unique ID using the nanoid library.\n *\n * @returns A unique ID that does not exist in the callResponses set.\n */\nfunction generateID() {\n    let result;\n    do {\n        result = nanoid();\n    } while (callResponses.has(result));\n    return result;\n}\n/**\n * Call a bound method according to the given call options.\n *\n * In case of failure, the returned promise will reject with an exception\n * among ReferenceError (unknown method), TypeError (wrong argument count or type),\n * {@link RuntimeError} (method returned an error), or other (network or internal errors).\n * The exception might have a \"cause\" field with the value returned\n * by the application- or service-level error marshaling functions.\n *\n * @param options - A method call descriptor.\n * @returns The result of the call.\n */\nexport function Call(options) {\n    const id = generateID();\n    const result = CancellablePromise.withResolvers();\n    callResponses.set(id, { resolve: result.resolve, reject: result.reject });\n    const request = call(CallBinding, Object.assign({ \"call-id\": id }, options));\n    let running = false;\n    request.then(() => {\n        running = true;\n    }, (err) => {\n        callResponses.delete(id);\n        result.reject(err);\n    });\n    const cancel = () => {\n        callResponses.delete(id);\n        return cancelCall(CancelMethod, { \"call-id\": id }).catch((err) => {\n            console.error(\"Error while requesting binding call cancellation:\", err);\n        });\n    };\n    result.oncancelled = () => {\n        if (running) {\n            return cancel();\n        }\n        else {\n            return request.then(cancel);\n        }\n    };\n    return result.promise;\n}\n/**\n * Calls a bound method by name with the specified arguments.\n * See {@link Call} for details.\n *\n * @param methodName - The name of the method in the format 'package.struct.method'.\n * @param args - The arguments to pass to the method.\n * @returns The result of the method call.\n */\nexport function ByName(methodName, ...args) {\n    return Call({ methodName, args });\n}\n/**\n * Calls a method by its numeric ID with the specified arguments.\n * See {@link Call} for details.\n *\n * @param methodID - The ID of the method to call.\n * @param args - The arguments to pass to the method.\n * @return The result of the method call.\n */\nexport function ByID(methodID, ...args) {\n    return Call({ methodID, args });\n}\n", "// Source: https://github.com/inspect-js/is-callable\n// The MIT License (MIT)\n//\n// Copyright (c) 2015 Jordan Harband\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\nvar fnToStr = Function.prototype.toString;\nvar reflectApply = typeof Reflect === 'object' && Reflect !== null && Reflect.apply;\nvar badArrayLike;\nvar isCallableMarker;\nif (typeof reflectApply === 'function' && typeof Object.defineProperty === 'function') {\n    try {\n        badArrayLike = Object.defineProperty({}, 'length', {\n            get: function () {\n                throw isCallableMarker;\n            }\n        });\n        isCallableMarker = {};\n        // eslint-disable-next-line no-throw-literal\n        reflectApply(function () { throw 42; }, null, badArrayLike);\n    }\n    catch (_) {\n        if (_ !== isCallableMarker) {\n            reflectApply = null;\n        }\n    }\n}\nelse {\n    reflectApply = null;\n}\nvar constructorRegex = /^\\s*class\\b/;\nvar isES6ClassFn = function isES6ClassFunction(value) {\n    try {\n        var fnStr = fnToStr.call(value);\n        return constructorRegex.test(fnStr);\n    }\n    catch (e) {\n        return false; // not a function\n    }\n};\nvar tryFunctionObject = function tryFunctionToStr(value) {\n    try {\n        if (isES6ClassFn(value)) {\n            return false;\n        }\n        fnToStr.call(value);\n        return true;\n    }\n    catch (e) {\n        return false;\n    }\n};\nvar toStr = Object.prototype.toString;\nvar objectClass = '[object Object]';\nvar fnClass = '[object Function]';\nvar genClass = '[object GeneratorFunction]';\nvar ddaClass = '[object HTMLAllCollection]'; // IE 11\nvar ddaClass2 = '[object HTML document.all class]';\nvar ddaClass3 = '[object HTMLCollection]'; // IE 9-10\nvar hasToStringTag = typeof Symbol === 'function' && !!Symbol.toStringTag; // better: use `has-tostringtag`\nvar isIE68 = !(0 in [,]); // eslint-disable-line no-sparse-arrays, comma-spacing\nvar isDDA = function isDocumentDotAll() { return false; };\nif (typeof document === 'object') {\n    // Firefox 3 canonicalizes DDA to undefined when it's not accessed directly\n    var all = document.all;\n    if (toStr.call(all) === toStr.call(document.all)) {\n        isDDA = function isDocumentDotAll(value) {\n            /* globals document: false */\n            // in IE 6-8, typeof document.all is \"object\" and it's truthy\n            if ((isIE68 || !value) && (typeof value === 'undefined' || typeof value === 'object')) {\n                try {\n                    var str = toStr.call(value);\n                    return (str === ddaClass\n                        || str === ddaClass2\n                        || str === ddaClass3 // opera 12.16\n                        || str === objectClass // IE 6-8\n                    ) && value('') == null; // eslint-disable-line eqeqeq\n                }\n                catch (e) { /**/ }\n            }\n            return false;\n        };\n    }\n}\nfunction isCallableRefApply(value) {\n    if (isDDA(value)) {\n        return true;\n    }\n    if (!value) {\n        return false;\n    }\n    if (typeof value !== 'function' && typeof value !== 'object') {\n        return false;\n    }\n    try {\n        reflectApply(value, null, badArrayLike);\n    }\n    catch (e) {\n        if (e !== isCallableMarker) {\n            return false;\n        }\n    }\n    return !isES6ClassFn(value) && tryFunctionObject(value);\n}\nfunction isCallableNoRefApply(value) {\n    if (isDDA(value)) {\n        return true;\n    }\n    if (!value) {\n        return false;\n    }\n    if (typeof value !== 'function' && typeof value !== 'object') {\n        return false;\n    }\n    if (hasToStringTag) {\n        return tryFunctionObject(value);\n    }\n    if (isES6ClassFn(value)) {\n        return false;\n    }\n    var strClass = toStr.call(value);\n    if (strClass !== fnClass && strClass !== genClass && !(/^\\[object HTML/).test(strClass)) {\n        return false;\n    }\n    return tryFunctionObject(value);\n}\n;\nexport default reflectApply ? isCallableRefApply : isCallableNoRefApply;\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) Lea Anthony 2019-present\n*/\nvar _a;\nimport isCallable from \"./callable.js\";\n/**\n * Exception class that will be used as rejection reason\n * in case a {@link CancellablePromise} is cancelled successfully.\n *\n * The value of the {@link name} property is the string `\"CancelError\"`.\n * The value of the {@link cause} property is the cause passed to the cancel method, if any.\n */\nexport class CancelError extends Error {\n    /**\n     * Constructs a new `CancelError` instance.\n     * @param message - The error message.\n     * @param options - Options to be forwarded to the Error constructor.\n     */\n    constructor(message, options) {\n        super(message, options);\n        this.name = \"CancelError\";\n    }\n}\n/**\n * Exception class that will be reported as an unhandled rejection\n * in case a {@link CancellablePromise} rejects after being cancelled,\n * or when the `oncancelled` callback throws or rejects.\n *\n * The value of the {@link name} property is the string `\"CancelledRejectionError\"`.\n * The value of the {@link cause} property is the reason the promise rejected with.\n *\n * Because the original promise was cancelled,\n * a wrapper promise will be passed to the unhandled rejection listener instead.\n * The {@link promise} property holds a reference to the original promise.\n */\nexport class CancelledRejectionError extends Error {\n    /**\n     * Constructs a new `CancelledRejectionError` instance.\n     * @param promise - The promise that caused the error originally.\n     * @param reason - The rejection reason.\n     * @param info - An optional informative message specifying the circumstances in which the error was thrown.\n     *               Defaults to the string `\"Unhandled rejection in cancelled promise.\"`.\n     */\n    constructor(promise, reason, info) {\n        super((info !== null && info !== void 0 ? info : \"Unhandled rejection in cancelled promise.\") + \" Reason: \" + errorMessage(reason), { cause: reason });\n        this.promise = promise;\n        this.name = \"CancelledRejectionError\";\n    }\n}\n// Private field names.\nconst barrierSym = Symbol(\"barrier\");\nconst cancelImplSym = Symbol(\"cancelImpl\");\nconst species = (_a = Symbol.species) !== null && _a !== void 0 ? _a : Symbol(\"speciesPolyfill\");\n/**\n * A promise with an attached method for cancelling long-running operations (see {@link CancellablePromise#cancel}).\n * Cancellation can optionally be bound to an {@link AbortSignal}\n * for better composability (see {@link CancellablePromise#cancelOn}).\n *\n * Cancelling a pending promise will result in an immediate rejection\n * with an instance of {@link CancelError} as reason,\n * but whoever started the promise will be responsible\n * for actually aborting the underlying operation.\n * To this purpose, the constructor and all chaining methods\n * accept optional cancellation callbacks.\n *\n * If a `CancellablePromise` still resolves after having been cancelled,\n * the result will be discarded. If it rejects, the reason\n * will be reported as an unhandled rejection,\n * wrapped in a {@link CancelledRejectionError} instance.\n * To facilitate the handling of cancellation requests,\n * cancelled `CancellablePromise`s will _not_ report unhandled `CancelError`s\n * whose `cause` field is the same as the one with which the current promise was cancelled.\n *\n * All usual promise methods are defined and return a `CancellablePromise`\n * whose cancel method will cancel the parent operation as well, propagating the cancellation reason\n * upwards through promise chains.\n * Conversely, cancelling a promise will not automatically cancel dependent promises downstream:\n * ```ts\n * let root = new CancellablePromise((resolve, reject) => { ... });\n * let child1 = root.then(() => { ... });\n * let child2 = child1.then(() => { ... });\n * let child3 = root.catch(() => { ... });\n * child1.cancel(); // Cancels child1 and root, but not child2 or child3\n * ```\n * Cancelling a promise that has already settled is safe and has no consequence.\n *\n * The `cancel` method returns a promise that _always fulfills_\n * after the whole chain has processed the cancel request\n * and all attached callbacks up to that moment have run.\n *\n * All ES2024 promise methods (static and instance) are defined on CancellablePromise,\n * but actual availability may vary with OS/webview version.\n *\n * In line with the proposal at https://github.com/tc39/proposal-rm-builtin-subclassing,\n * `CancellablePromise` does not support transparent subclassing.\n * Extenders should take care to provide their own method implementations.\n * This might be reconsidered in case the proposal is retired.\n *\n * CancellablePromise is a wrapper around the DOM Promise object\n * and is compliant with the [Promises/A+ specification](https://promisesaplus.com/)\n * (it passes the [compliance suite](https://github.com/promises-aplus/promises-tests))\n * if so is the underlying implementation.\n */\nexport class CancellablePromise extends Promise {\n    /**\n     * Creates a new `CancellablePromise`.\n     *\n     * @param executor - A callback used to initialize the promise. This callback is passed two arguments:\n     *                   a `resolve` callback used to resolve the promise with a value\n     *                   or the result of another promise (possibly cancellable),\n     *                   and a `reject` callback used to reject the promise with a provided reason or error.\n     *                   If the value provided to the `resolve` callback is a thenable _and_ cancellable object\n     *                   (it has a `then` _and_ a `cancel` method),\n     *                   cancellation requests will be forwarded to that object and the oncancelled will not be invoked anymore.\n     *                   If any one of the two callbacks is called _after_ the promise has been cancelled,\n     *                   the provided values will be cancelled and resolved as usual,\n     *                   but their results will be discarded.\n     *                   However, if the resolution process ultimately ends up in a rejection\n     *                   that is not due to cancellation, the rejection reason\n     *                   will be wrapped in a {@link CancelledRejectionError}\n     *                   and bubbled up as an unhandled rejection.\n     * @param oncancelled - It is the caller's responsibility to ensure that any operation\n     *                      started by the executor is properly halted upon cancellation.\n     *                      This optional callback can be used to that purpose.\n     *                      It will be called _synchronously_ with a cancellation cause\n     *                      when cancellation is requested, _after_ the promise has already rejected\n     *                      with a {@link CancelError}, but _before_\n     *                      any {@link then}/{@link catch}/{@link finally} callback runs.\n     *                      If the callback returns a thenable, the promise returned from {@link cancel}\n     *                      will only fulfill after the former has settled.\n     *                      Unhandled exceptions or rejections from the callback will be wrapped\n     *                      in a {@link CancelledRejectionError} and bubbled up as unhandled rejections.\n     *                      If the `resolve` callback is called before cancellation with a cancellable promise,\n     *                      cancellation requests on this promise will be diverted to that promise,\n     *                      and the original `oncancelled` callback will be discarded.\n     */\n    constructor(executor, oncancelled) {\n        let resolve;\n        let reject;\n        super((res, rej) => { resolve = res; reject = rej; });\n        if (this.constructor[species] !== Promise) {\n            throw new TypeError(\"CancellablePromise does not support transparent subclassing. Please refrain from overriding the [Symbol.species] static property.\");\n        }\n        let promise = {\n            promise: this,\n            resolve,\n            reject,\n            get oncancelled() { return oncancelled !== null && oncancelled !== void 0 ? oncancelled : null; },\n            set oncancelled(cb) { oncancelled = cb !== null && cb !== void 0 ? cb : undefined; }\n        };\n        const state = {\n            get root() { return state; },\n            resolving: false,\n            settled: false\n        };\n        // Setup cancellation system.\n        void Object.defineProperties(this, {\n            [barrierSym]: {\n                configurable: false,\n                enumerable: false,\n                writable: true,\n                value: null\n            },\n            [cancelImplSym]: {\n                configurable: false,\n                enumerable: false,\n                writable: false,\n                value: cancellerFor(promise, state)\n            }\n        });\n        // Run the actual executor.\n        const rejector = rejectorFor(promise, state);\n        try {\n            executor(resolverFor(promise, state), rejector);\n        }\n        catch (err) {\n            if (state.resolving) {\n                console.log(\"Unhandled exception in CancellablePromise executor.\", err);\n            }\n            else {\n                rejector(err);\n            }\n        }\n    }\n    /**\n     * Cancels immediately the execution of the operation associated with this promise.\n     * The promise rejects with a {@link CancelError} instance as reason,\n     * with the {@link CancelError#cause} property set to the given argument, if any.\n     *\n     * Has no effect if called after the promise has already settled;\n     * repeated calls in particular are safe, but only the first one\n     * will set the cancellation cause.\n     *\n     * The `CancelError` exception _need not_ be handled explicitly _on the promises that are being cancelled:_\n     * cancelling a promise with no attached rejection handler does not trigger an unhandled rejection event.\n     * Therefore, the following idioms are all equally correct:\n     * ```ts\n     * new CancellablePromise((resolve, reject) => { ... }).cancel();\n     * new CancellablePromise((resolve, reject) => { ... }).then(...).cancel();\n     * new CancellablePromise((resolve, reject) => { ... }).then(...).catch(...).cancel();\n     * ```\n     * Whenever some cancelled promise in a chain rejects with a `CancelError`\n     * with the same cancellation cause as itself, the error will be discarded silently.\n     * However, the `CancelError` _will still be delivered_ to all attached rejection handlers\n     * added by {@link then} and related methods:\n     * ```ts\n     * let cancellable = new CancellablePromise((resolve, reject) => { ... });\n     * cancellable.then(() => { ... }).catch(console.log);\n     * cancellable.cancel(); // A CancelError is printed to the console.\n     * ```\n     * If the `CancelError` is not handled downstream by the time it reaches\n     * a _non-cancelled_ promise, it _will_ trigger an unhandled rejection event,\n     * just like normal rejections would:\n     * ```ts\n     * let cancellable = new CancellablePromise((resolve, reject) => { ... });\n     * let chained = cancellable.then(() => { ... }).then(() => { ... }); // No catch...\n     * cancellable.cancel(); // Unhandled rejection event on chained!\n     * ```\n     * Therefore, it is important to either cancel whole promise chains from their tail,\n     * as shown in the correct idioms above, or take care of handling errors everywhere.\n     *\n     * @returns A cancellable promise that _fulfills_ after the cancel callback (if any)\n     * and all handlers attached up to the call to cancel have run.\n     * If the cancel callback returns a thenable, the promise returned by `cancel`\n     * will also wait for that thenable to settle.\n     * This enables callers to wait for the cancelled operation to terminate\n     * without being forced to handle potential errors at the call site.\n     * ```ts\n     * cancellable.cancel().then(() => {\n     *     // Cleanup finished, it's safe to do something else.\n     * }, (err) => {\n     *     // Unreachable: the promise returned from cancel will never reject.\n     * });\n     * ```\n     * Note that the returned promise will _not_ handle implicitly any rejection\n     * that might have occurred already in the cancelled chain.\n     * It will just track whether registered handlers have been executed or not.\n     * Therefore, unhandled rejections will never be silently handled by calling cancel.\n     */\n    cancel(cause) {\n        return new CancellablePromise((resolve) => {\n            // INVARIANT: the result of this[cancelImplSym] and the barrier do not ever reject.\n            // Unfortunately macOS High Sierra does not support Promise.allSettled.\n            Promise.all([\n                this[cancelImplSym](new CancelError(\"Promise cancelled.\", { cause })),\n                currentBarrier(this)\n            ]).then(() => resolve(), () => resolve());\n        });\n    }\n    /**\n     * Binds promise cancellation to the abort event of the given {@link AbortSignal}.\n     * If the signal has already aborted, the promise will be cancelled immediately.\n     * When either condition is verified, the cancellation cause will be set\n     * to the signal's abort reason (see {@link AbortSignal#reason}).\n     *\n     * Has no effect if called (or if the signal aborts) _after_ the promise has already settled.\n     * Only the first signal to abort will set the cancellation cause.\n     *\n     * For more details about the cancellation process,\n     * see {@link cancel} and the `CancellablePromise` constructor.\n     *\n     * This method enables `await`ing cancellable promises without having\n     * to store them for future cancellation, e.g.:\n     * ```ts\n     * await longRunningOperation().cancelOn(signal);\n     * ```\n     * instead of:\n     * ```ts\n     * let promiseToBeCancelled = longRunningOperation();\n     * await promiseToBeCancelled;\n     * ```\n     *\n     * @returns This promise, for method chaining.\n     */\n    cancelOn(signal) {\n        if (signal.aborted) {\n            void this.cancel(signal.reason);\n        }\n        else {\n            signal.addEventListener('abort', () => void this.cancel(signal.reason), { capture: true });\n        }\n        return this;\n    }\n    /**\n     * Attaches callbacks for the resolution and/or rejection of the `CancellablePromise`.\n     *\n     * The optional `oncancelled` argument will be invoked when the returned promise is cancelled,\n     * with the same semantics as the `oncancelled` argument of the constructor.\n     * When the parent promise rejects or is cancelled, the `onrejected` callback will run,\n     * _even after the returned promise has been cancelled:_\n     * in that case, should it reject or throw, the reason will be wrapped\n     * in a {@link CancelledRejectionError} and bubbled up as an unhandled rejection.\n     *\n     * @param onfulfilled The callback to execute when the Promise is resolved.\n     * @param onrejected The callback to execute when the Promise is rejected.\n     * @returns A `CancellablePromise` for the completion of whichever callback is executed.\n     * The returned promise is hooked up to propagate cancellation requests up the chain, but not down:\n     *\n     *   - if the parent promise is cancelled, the `onrejected` handler will be invoked with a `CancelError`\n     *     and the returned promise _will resolve regularly_ with its result;\n     *   - conversely, if the returned promise is cancelled, _the parent promise is cancelled too;_\n     *     the `onrejected` handler will still be invoked with the parent's `CancelError`,\n     *     but its result will be discarded\n     *     and the returned promise will reject with a `CancelError` as well.\n     *\n     * The promise returned from {@link cancel} will fulfill only after all attached handlers\n     * up the entire promise chain have been run.\n     *\n     * If either callback returns a cancellable promise,\n     * cancellation requests will be diverted to it,\n     * and the specified `oncancelled` callback will be discarded.\n     */\n    then(onfulfilled, onrejected, oncancelled) {\n        if (!(this instanceof CancellablePromise)) {\n            throw new TypeError(\"CancellablePromise.prototype.then called on an invalid object.\");\n        }\n        // NOTE: TypeScript's built-in type for then is broken,\n        // as it allows specifying an arbitrary TResult1 != T even when onfulfilled is not a function.\n        // We cannot fix it if we want to CancellablePromise to implement PromiseLike<T>.\n        if (!isCallable(onfulfilled)) {\n            onfulfilled = identity;\n        }\n        if (!isCallable(onrejected)) {\n            onrejected = thrower;\n        }\n        if (onfulfilled === identity && onrejected == thrower) {\n            // Shortcut for trivial arguments.\n            return new CancellablePromise((resolve) => resolve(this));\n        }\n        const barrier = {};\n        this[barrierSym] = barrier;\n        return new CancellablePromise((resolve, reject) => {\n            void super.then((value) => {\n                var _a;\n                if (this[barrierSym] === barrier) {\n                    this[barrierSym] = null;\n                }\n                (_a = barrier.resolve) === null || _a === void 0 ? void 0 : _a.call(barrier);\n                try {\n                    resolve(onfulfilled(value));\n                }\n                catch (err) {\n                    reject(err);\n                }\n            }, (reason) => {\n                var _a;\n                if (this[barrierSym] === barrier) {\n                    this[barrierSym] = null;\n                }\n                (_a = barrier.resolve) === null || _a === void 0 ? void 0 : _a.call(barrier);\n                try {\n                    resolve(onrejected(reason));\n                }\n                catch (err) {\n                    reject(err);\n                }\n            });\n        }, async (cause) => {\n            //cancelled = true;\n            try {\n                return oncancelled === null || oncancelled === void 0 ? void 0 : oncancelled(cause);\n            }\n            finally {\n                await this.cancel(cause);\n            }\n        });\n    }\n    /**\n     * Attaches a callback for only the rejection of the Promise.\n     *\n     * The optional `oncancelled` argument will be invoked when the returned promise is cancelled,\n     * with the same semantics as the `oncancelled` argument of the constructor.\n     * When the parent promise rejects or is cancelled, the `onrejected` callback will run,\n     * _even after the returned promise has been cancelled:_\n     * in that case, should it reject or throw, the reason will be wrapped\n     * in a {@link CancelledRejectionError} and bubbled up as an unhandled rejection.\n     *\n     * It is equivalent to\n     * ```ts\n     * cancellablePromise.then(undefined, onrejected, oncancelled);\n     * ```\n     * and the same caveats apply.\n     *\n     * @returns A Promise for the completion of the callback.\n     * Cancellation requests on the returned promise\n     * will propagate up the chain to the parent promise,\n     * but not in the other direction.\n     *\n     * The promise returned from {@link cancel} will fulfill only after all attached handlers\n     * up the entire promise chain have been run.\n     *\n     * If `onrejected` returns a cancellable promise,\n     * cancellation requests will be diverted to it,\n     * and the specified `oncancelled` callback will be discarded.\n     * See {@link then} for more details.\n     */\n    catch(onrejected, oncancelled) {\n        return this.then(undefined, onrejected, oncancelled);\n    }\n    /**\n     * Attaches a callback that is invoked when the CancellablePromise is settled (fulfilled or rejected). The\n     * resolved value cannot be accessed or modified from the callback.\n     * The returned promise will settle in the same state as the original one\n     * after the provided callback has completed execution,\n     * unless the callback throws or returns a rejecting promise,\n     * in which case the returned promise will reject as well.\n     *\n     * The optional `oncancelled` argument will be invoked when the returned promise is cancelled,\n     * with the same semantics as the `oncancelled` argument of the constructor.\n     * Once the parent promise settles, the `onfinally` callback will run,\n     * _even after the returned promise has been cancelled:_\n     * in that case, should it reject or throw, the reason will be wrapped\n     * in a {@link CancelledRejectionError} and bubbled up as an unhandled rejection.\n     *\n     * This method is implemented in terms of {@link then} and the same caveats apply.\n     * It is polyfilled, hence available in every OS/webview version.\n     *\n     * @returns A Promise for the completion of the callback.\n     * Cancellation requests on the returned promise\n     * will propagate up the chain to the parent promise,\n     * but not in the other direction.\n     *\n     * The promise returned from {@link cancel} will fulfill only after all attached handlers\n     * up the entire promise chain have been run.\n     *\n     * If `onfinally` returns a cancellable promise,\n     * cancellation requests will be diverted to it,\n     * and the specified `oncancelled` callback will be discarded.\n     * See {@link then} for more details.\n     */\n    finally(onfinally, oncancelled) {\n        if (!(this instanceof CancellablePromise)) {\n            throw new TypeError(\"CancellablePromise.prototype.finally called on an invalid object.\");\n        }\n        if (!isCallable(onfinally)) {\n            return this.then(onfinally, onfinally, oncancelled);\n        }\n        return this.then((value) => CancellablePromise.resolve(onfinally()).then(() => value), (reason) => CancellablePromise.resolve(onfinally()).then(() => { throw reason; }), oncancelled);\n    }\n    /**\n     * We use the `[Symbol.species]` static property, if available,\n     * to disable the built-in automatic subclassing features from {@link Promise}.\n     * It is critical for performance reasons that extenders do not override this.\n     * Once the proposal at https://github.com/tc39/proposal-rm-builtin-subclassing\n     * is either accepted or retired, this implementation will have to be revised accordingly.\n     *\n     * @ignore\n     * @internal\n     */\n    static get [species]() {\n        return Promise;\n    }\n    static all(values) {\n        let collected = Array.from(values);\n        const promise = collected.length === 0\n            ? CancellablePromise.resolve(collected)\n            : new CancellablePromise((resolve, reject) => {\n                void Promise.all(collected).then(resolve, reject);\n            }, (cause) => cancelAll(promise, collected, cause));\n        return promise;\n    }\n    static allSettled(values) {\n        let collected = Array.from(values);\n        const promise = collected.length === 0\n            ? CancellablePromise.resolve(collected)\n            : new CancellablePromise((resolve, reject) => {\n                void Promise.allSettled(collected).then(resolve, reject);\n            }, (cause) => cancelAll(promise, collected, cause));\n        return promise;\n    }\n    static any(values) {\n        let collected = Array.from(values);\n        const promise = collected.length === 0\n            ? CancellablePromise.resolve(collected)\n            : new CancellablePromise((resolve, reject) => {\n                void Promise.any(collected).then(resolve, reject);\n            }, (cause) => cancelAll(promise, collected, cause));\n        return promise;\n    }\n    static race(values) {\n        let collected = Array.from(values);\n        const promise = new CancellablePromise((resolve, reject) => {\n            void Promise.race(collected).then(resolve, reject);\n        }, (cause) => cancelAll(promise, collected, cause));\n        return promise;\n    }\n    /**\n     * Creates a new cancelled CancellablePromise for the provided cause.\n     *\n     * @group Static Methods\n     */\n    static cancel(cause) {\n        const p = new CancellablePromise(() => { });\n        p.cancel(cause);\n        return p;\n    }\n    /**\n     * Creates a new CancellablePromise that cancels\n     * after the specified timeout, with the provided cause.\n     *\n     * If the {@link AbortSignal.timeout} factory method is available,\n     * it is used to base the timeout on _active_ time rather than _elapsed_ time.\n     * Otherwise, `timeout` falls back to {@link setTimeout}.\n     *\n     * @group Static Methods\n     */\n    static timeout(milliseconds, cause) {\n        const promise = new CancellablePromise(() => { });\n        if (AbortSignal && typeof AbortSignal === 'function' && AbortSignal.timeout && typeof AbortSignal.timeout === 'function') {\n            AbortSignal.timeout(milliseconds).addEventListener('abort', () => void promise.cancel(cause));\n        }\n        else {\n            setTimeout(() => void promise.cancel(cause), milliseconds);\n        }\n        return promise;\n    }\n    static sleep(milliseconds, value) {\n        return new CancellablePromise((resolve) => {\n            setTimeout(() => resolve(value), milliseconds);\n        });\n    }\n    /**\n     * Creates a new rejected CancellablePromise for the provided reason.\n     *\n     * @group Static Methods\n     */\n    static reject(reason) {\n        return new CancellablePromise((_, reject) => reject(reason));\n    }\n    static resolve(value) {\n        if (value instanceof CancellablePromise) {\n            // Optimise for cancellable promises.\n            return value;\n        }\n        return new CancellablePromise((resolve) => resolve(value));\n    }\n    /**\n     * Creates a new CancellablePromise and returns it in an object, along with its resolve and reject functions\n     * and a getter/setter for the cancellation callback.\n     *\n     * This method is polyfilled, hence available in every OS/webview version.\n     *\n     * @group Static Methods\n     */\n    static withResolvers() {\n        let result = { oncancelled: null };\n        result.promise = new CancellablePromise((resolve, reject) => {\n            result.resolve = resolve;\n            result.reject = reject;\n        }, (cause) => { var _a; (_a = result.oncancelled) === null || _a === void 0 ? void 0 : _a.call(result, cause); });\n        return result;\n    }\n}\n/**\n * Returns a callback that implements the cancellation algorithm for the given cancellable promise.\n * The promise returned from the resulting function does not reject.\n */\nfunction cancellerFor(promise, state) {\n    let cancellationPromise = undefined;\n    return (reason) => {\n        if (!state.settled) {\n            state.settled = true;\n            state.reason = reason;\n            promise.reject(reason);\n            // Attach an error handler that ignores this specific rejection reason and nothing else.\n            // In theory, a sane underlying implementation at this point\n            // should always reject with our cancellation reason,\n            // hence the handler will never throw.\n            void Promise.prototype.then.call(promise.promise, undefined, (err) => {\n                if (err !== reason) {\n                    throw err;\n                }\n            });\n        }\n        // If reason is not set, the promise resolved regularly, hence we must not call oncancelled.\n        // If oncancelled is unset, no need to go any further.\n        if (!state.reason || !promise.oncancelled) {\n            return;\n        }\n        cancellationPromise = new Promise((resolve) => {\n            try {\n                resolve(promise.oncancelled(state.reason.cause));\n            }\n            catch (err) {\n                Promise.reject(new CancelledRejectionError(promise.promise, err, \"Unhandled exception in oncancelled callback.\"));\n            }\n        }).catch((reason) => {\n            Promise.reject(new CancelledRejectionError(promise.promise, reason, \"Unhandled rejection in oncancelled callback.\"));\n        });\n        // Unset oncancelled to prevent repeated calls.\n        promise.oncancelled = null;\n        return cancellationPromise;\n    };\n}\n/**\n * Returns a callback that implements the resolution algorithm for the given cancellable promise.\n */\nfunction resolverFor(promise, state) {\n    return (value) => {\n        if (state.resolving) {\n            return;\n        }\n        state.resolving = true;\n        if (value === promise.promise) {\n            if (state.settled) {\n                return;\n            }\n            state.settled = true;\n            promise.reject(new TypeError(\"A promise cannot be resolved with itself.\"));\n            return;\n        }\n        if (value != null && (typeof value === 'object' || typeof value === 'function')) {\n            let then;\n            try {\n                then = value.then;\n            }\n            catch (err) {\n                state.settled = true;\n                promise.reject(err);\n                return;\n            }\n            if (isCallable(then)) {\n                try {\n                    let cancel = value.cancel;\n                    if (isCallable(cancel)) {\n                        const oncancelled = (cause) => {\n                            Reflect.apply(cancel, value, [cause]);\n                        };\n                        if (state.reason) {\n                            // If already cancelled, propagate cancellation.\n                            // The promise returned from the canceller algorithm does not reject\n                            // so it can be discarded safely.\n                            void cancellerFor(Object.assign(Object.assign({}, promise), { oncancelled }), state)(state.reason);\n                        }\n                        else {\n                            promise.oncancelled = oncancelled;\n                        }\n                    }\n                }\n                catch (_a) { }\n                const newState = {\n                    root: state.root,\n                    resolving: false,\n                    get settled() { return this.root.settled; },\n                    set settled(value) { this.root.settled = value; },\n                    get reason() { return this.root.reason; }\n                };\n                const rejector = rejectorFor(promise, newState);\n                try {\n                    Reflect.apply(then, value, [resolverFor(promise, newState), rejector]);\n                }\n                catch (err) {\n                    rejector(err);\n                }\n                return; // IMPORTANT!\n            }\n        }\n        if (state.settled) {\n            return;\n        }\n        state.settled = true;\n        promise.resolve(value);\n    };\n}\n/**\n * Returns a callback that implements the rejection algorithm for the given cancellable promise.\n */\nfunction rejectorFor(promise, state) {\n    return (reason) => {\n        if (state.resolving) {\n            return;\n        }\n        state.resolving = true;\n        if (state.settled) {\n            try {\n                if (reason instanceof CancelError && state.reason instanceof CancelError && Object.is(reason.cause, state.reason.cause)) {\n                    // Swallow late rejections that are CancelErrors whose cancellation cause is the same as ours.\n                    return;\n                }\n            }\n            catch (_a) { }\n            void Promise.reject(new CancelledRejectionError(promise.promise, reason));\n        }\n        else {\n            state.settled = true;\n            promise.reject(reason);\n        }\n    };\n}\n/**\n * Cancels all values in an array that look like cancellable thenables.\n * Returns a promise that fulfills once all cancellation procedures for the given values have settled.\n */\nfunction cancelAll(parent, values, cause) {\n    const results = [];\n    for (const value of values) {\n        let cancel;\n        try {\n            if (!isCallable(value.then)) {\n                continue;\n            }\n            cancel = value.cancel;\n            if (!isCallable(cancel)) {\n                continue;\n            }\n        }\n        catch (_a) {\n            continue;\n        }\n        let result;\n        try {\n            result = Reflect.apply(cancel, value, [cause]);\n        }\n        catch (err) {\n            Promise.reject(new CancelledRejectionError(parent, err, \"Unhandled exception in cancel method.\"));\n            continue;\n        }\n        if (!result) {\n            continue;\n        }\n        results.push((result instanceof Promise ? result : Promise.resolve(result)).catch((reason) => {\n            Promise.reject(new CancelledRejectionError(parent, reason, \"Unhandled rejection in cancel method.\"));\n        }));\n    }\n    return Promise.all(results);\n}\n/**\n * Returns its argument.\n */\nfunction identity(x) {\n    return x;\n}\n/**\n * Throws its argument.\n */\nfunction thrower(reason) {\n    throw reason;\n}\n/**\n * Attempts various strategies to convert an error to a string.\n */\nfunction errorMessage(err) {\n    try {\n        if (err instanceof Error || typeof err !== 'object' || err.toString !== Object.prototype.toString) {\n            return \"\" + err;\n        }\n    }\n    catch (_a) { }\n    try {\n        return JSON.stringify(err);\n    }\n    catch (_b) { }\n    try {\n        return Object.prototype.toString.call(err);\n    }\n    catch (_c) { }\n    return \"<could not convert error to string>\";\n}\n/**\n * Gets the current barrier promise for the given cancellable promise. If necessary, initialises the barrier.\n */\nfunction currentBarrier(promise) {\n    var _a;\n    let pwr = (_a = promise[barrierSym]) !== null && _a !== void 0 ? _a : {};\n    if (!('promise' in pwr)) {\n        Object.assign(pwr, promiseWithResolvers());\n    }\n    if (promise[barrierSym] == null) {\n        pwr.resolve();\n        promise[barrierSym] = pwr;\n    }\n    return pwr.promise;\n}\n// Polyfill Promise.withResolvers.\nlet promiseWithResolvers = Promise.withResolvers;\nif (promiseWithResolvers && typeof promiseWithResolvers === 'function') {\n    promiseWithResolvers = promiseWithResolvers.bind(Promise);\n}\nelse {\n    promiseWithResolvers = function () {\n        let resolve;\n        let reject;\n        const promise = new Promise((res, rej) => { resolve = res; reject = rej; });\n        return { promise, resolve, reject };\n    };\n}\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) <PERSON> Anthony 2019-present\n*/\nimport { newRuntimeCaller, objectNames } from \"./runtime.js\";\nconst call = newRuntimeCaller(objectNames.Clipboard);\nconst ClipboardSetText = 0;\nconst ClipboardText = 1;\n/**\n * Sets the text to the Clipboard.\n *\n * @param text - The text to be set to the Clipboard.\n * @return A Promise that resolves when the operation is successful.\n */\nexport function SetText(text) {\n    return call(ClipboardSetText, { text });\n}\n/**\n * Get the Clipboard text\n *\n * @returns A promise that resolves with the text from the Clipboard.\n */\nexport function Text() {\n    return call(ClipboardText);\n}\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) Lea Anthony 2019-present\n*/\n/**\n * Any is a dummy creation function for simple or unknown types.\n */\nexport function Any(source) {\n    return source;\n}\n/**\n * ByteSlice is a creation function that replaces\n * null strings with empty strings.\n */\nexport function ByteSlice(source) {\n    return ((source == null) ? \"\" : source);\n}\n/**\n * Array takes a creation function for an arbitrary type\n * and returns an in-place creation function for an array\n * whose elements are of that type.\n */\nexport function Array(element) {\n    if (element === Any) {\n        return (source) => (source === null ? [] : source);\n    }\n    return (source) => {\n        if (source === null) {\n            return [];\n        }\n        for (let i = 0; i < source.length; i++) {\n            source[i] = element(source[i]);\n        }\n        return source;\n    };\n}\n/**\n * Map takes creation functions for two arbitrary types\n * and returns an in-place creation function for an object\n * whose keys and values are of those types.\n */\nexport function Map(key, value) {\n    if (value === Any) {\n        return (source) => (source === null ? {} : source);\n    }\n    return (source) => {\n        if (source === null) {\n            return {};\n        }\n        for (const key in source) {\n            source[key] = value(source[key]);\n        }\n        return source;\n    };\n}\n/**\n * Nullable takes a creation function for an arbitrary type\n * and returns a creation function for a nullable value of that type.\n */\nexport function Nullable(element) {\n    if (element === Any) {\n        return Any;\n    }\n    return (source) => (source === null ? null : element(source));\n}\n/**\n * Struct takes an object mapping field names to creation functions\n * and returns an in-place creation function for a struct.\n */\nexport function Struct(createField) {\n    let allAny = true;\n    for (const name in createField) {\n        if (createField[name] !== Any) {\n            allAny = false;\n            break;\n        }\n    }\n    if (allAny) {\n        return Any;\n    }\n    return (source) => {\n        for (const name in createField) {\n            if (name in source) {\n                source[name] = createField[name](source[name]);\n            }\n        }\n        return source;\n    };\n}\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) <PERSON> Anthony 2019-present\n*/\nimport { newRuntimeCaller, objectNames } from \"./runtime.js\";\nimport { nanoid } from './nanoid.js';\n// setup\nwindow._wails = window._wails || {};\nwindow._wails.dialogErrorCallback = dialogErrorCallback;\nwindow._wails.dialogResultCallback = dialogResultCallback;\nconst call = newRuntimeCaller(objectNames.Dialog);\nconst dialogResponses = new Map();\n// Define constants from the `methods` object in Title Case\nconst DialogInfo = 0;\nconst DialogWarning = 1;\nconst DialogError = 2;\nconst DialogQuestion = 3;\nconst DialogOpenFile = 4;\nconst DialogSaveFile = 5;\n/**\n * Handles the result of a dialog request.\n *\n * @param id - The id of the request to handle the result for.\n * @param data - The result data of the request.\n * @param isJSON - Indicates whether the data is JSON or not.\n */\nfunction dialogResultCallback(id, data, isJSON) {\n    let resolvers = getAndDeleteResponse(id);\n    if (!resolvers) {\n        return;\n    }\n    if (isJSON) {\n        try {\n            resolvers.resolve(JSON.parse(data));\n        }\n        catch (err) {\n            resolvers.reject(new TypeError(\"could not parse result: \" + err.message, { cause: err }));\n        }\n    }\n    else {\n        resolvers.resolve(data);\n    }\n}\n/**\n * Handles the error from a dialog request.\n *\n * @param id - The id of the promise handler.\n * @param message - An error message.\n */\nfunction dialogErrorCallback(id, message) {\n    var _a;\n    (_a = getAndDeleteResponse(id)) === null || _a === void 0 ? void 0 : _a.reject(new window.Error(message));\n}\n/**\n * Retrieves and removes the response associated with the given ID from the dialogResponses map.\n *\n * @param id - The ID of the response to be retrieved and removed.\n * @returns The response object associated with the given ID, if any.\n */\nfunction getAndDeleteResponse(id) {\n    const response = dialogResponses.get(id);\n    dialogResponses.delete(id);\n    return response;\n}\n/**\n * Generates a unique ID using the nanoid library.\n *\n * @returns A unique ID that does not exist in the dialogResponses set.\n */\nfunction generateID() {\n    let result;\n    do {\n        result = nanoid();\n    } while (dialogResponses.has(result));\n    return result;\n}\n/**\n * Presents a dialog of specified type with the given options.\n *\n * @param type - Dialog type.\n * @param options - Options for the dialog.\n * @returns A promise that resolves with result of dialog.\n */\nfunction dialog(type, options = {}) {\n    const id = generateID();\n    return new Promise((resolve, reject) => {\n        dialogResponses.set(id, { resolve, reject });\n        call(type, Object.assign({ \"dialog-id\": id }, options)).catch((err) => {\n            dialogResponses.delete(id);\n            reject(err);\n        });\n    });\n}\n/**\n * Presents an info dialog.\n *\n * @param options - Dialog options\n * @returns A promise that resolves with the label of the chosen button.\n */\nexport function Info(options) { return dialog(DialogInfo, options); }\n/**\n * Presents a warning dialog.\n *\n * @param options - Dialog options.\n * @returns A promise that resolves with the label of the chosen button.\n */\nexport function Warning(options) { return dialog(DialogWarning, options); }\n/**\n * Presents an error dialog.\n *\n * @param options - Dialog options.\n * @returns A promise that resolves with the label of the chosen button.\n */\nexport function Error(options) { return dialog(DialogError, options); }\n/**\n * Presents a question dialog.\n *\n * @param options - Dialog options.\n * @returns A promise that resolves with the label of the chosen button.\n */\nexport function Question(options) { return dialog(DialogQuestion, options); }\nexport function OpenFile(options) { var _a; return (_a = dialog(DialogOpenFile, options)) !== null && _a !== void 0 ? _a : []; }\n/**\n * Presents a file selection dialog to pick a file to save.\n *\n * @param options - Dialog options.\n * @returns Selected file, or a blank string if no file has been selected.\n */\nexport function SaveFile(options) { return dialog(DialogSaveFile, options); }\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) <PERSON> Anthony 2019-present\n*/\nimport { newRuntimeCaller, objectNames } from \"./runtime.js\";\nimport { eventListeners, Listener, listenerOff } from \"./listener.js\";\n// Setup\nwindow._wails = window._wails || {};\nwindow._wails.dispatchWailsEvent = dispatchWailsEvent;\nconst call = newRuntimeCaller(objectNames.Events);\nconst EmitMethod = 0;\nexport { Types } from \"./event_types.js\";\n/**\n * Represents a system event or a custom event emitted through wails-provided facilities.\n */\nexport class WailsEvent {\n    constructor(name, data = null) {\n        this.name = name;\n        this.data = data;\n    }\n}\nfunction dispatchWailsEvent(event) {\n    let listeners = eventListeners.get(event.name);\n    if (!listeners) {\n        return;\n    }\n    let wailsEvent = new WailsEvent(event.name, event.data);\n    if ('sender' in event) {\n        wailsEvent.sender = event.sender;\n    }\n    listeners = listeners.filter(listener => !listener.dispatch(wailsEvent));\n    if (listeners.length === 0) {\n        eventListeners.delete(event.name);\n    }\n    else {\n        eventListeners.set(event.name, listeners);\n    }\n}\n/**\n * Register a callback function to be called multiple times for a specific event.\n *\n * @param eventName - The name of the event to register the callback for.\n * @param callback - The callback function to be called when the event is triggered.\n * @param maxCallbacks - The maximum number of times the callback can be called for the event. Once the maximum number is reached, the callback will no longer be called.\n * @returns A function that, when called, will unregister the callback from the event.\n */\nexport function OnMultiple(eventName, callback, maxCallbacks) {\n    let listeners = eventListeners.get(eventName) || [];\n    const thisListener = new Listener(eventName, callback, maxCallbacks);\n    listeners.push(thisListener);\n    eventListeners.set(eventName, listeners);\n    return () => listenerOff(thisListener);\n}\n/**\n * Registers a callback function to be executed when the specified event occurs.\n *\n * @param eventName - The name of the event to register the callback for.\n * @param callback - The callback function to be called when the event is triggered.\n * @returns A function that, when called, will unregister the callback from the event.\n */\nexport function On(eventName, callback) {\n    return OnMultiple(eventName, callback, -1);\n}\n/**\n * Registers a callback function to be executed only once for the specified event.\n *\n * @param eventName - The name of the event to register the callback for.\n * @param callback - The callback function to be called when the event is triggered.\n * @returns A function that, when called, will unregister the callback from the event.\n */\nexport function Once(eventName, callback) {\n    return OnMultiple(eventName, callback, 1);\n}\n/**\n * Removes event listeners for the specified event names.\n *\n * @param eventNames - The name of the events to remove listeners for.\n */\nexport function Off(...eventNames) {\n    eventNames.forEach(eventName => eventListeners.delete(eventName));\n}\n/**\n * Removes all event listeners.\n */\nexport function OffAll() {\n    eventListeners.clear();\n}\n/**\n * Emits the given event.\n *\n * @param event - The name of the event to emit.\n * @returns A promise that will be fulfilled once the event has been emitted.\n */\nexport function Emit(event) {\n    return call(EmitMethod, event);\n}\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) <PERSON> 2019-present\n*/\n// The following utilities have been factored out of ./events.ts\n// for testing purposes.\nexport const eventListeners = new Map();\nexport class Listener {\n    constructor(eventName, callback, maxCallbacks) {\n        this.eventName = eventName;\n        this.callback = callback;\n        this.maxCallbacks = maxCallbacks || -1;\n    }\n    dispatch(data) {\n        try {\n            this.callback(data);\n        }\n        catch (err) {\n            console.error(err);\n        }\n        if (this.maxCallbacks === -1)\n            return false;\n        this.maxCallbacks -= 1;\n        return this.maxCallbacks === 0;\n    }\n}\nexport function listenerOff(listener) {\n    let listeners = eventListeners.get(listener.eventName);\n    if (!listeners) {\n        return;\n    }\n    listeners = listeners.filter(l => l !== listener);\n    if (listeners.length === 0) {\n        eventListeners.delete(listener.eventName);\n    }\n    else {\n        eventListeners.set(listener.eventName, listeners);\n    }\n}\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) <PERSON> 2019-present\n*/\n// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL\n// This file is automatically generated. DO NOT EDIT\nexport const Types = Object.freeze({\n    Windows: Object.freeze({\n        APMPowerSettingChange: \"windows:APMPowerSettingChange\",\n        APMPowerStatusChange: \"windows:APMPowerStatusChange\",\n        APMResumeAutomatic: \"windows:APMResumeAutomatic\",\n        APMResumeSuspend: \"windows:APMResumeSuspend\",\n        APMSuspend: \"windows:APMSuspend\",\n        ApplicationStarted: \"windows:ApplicationStarted\",\n        SystemThemeChanged: \"windows:SystemThemeChanged\",\n        WebViewNavigationCompleted: \"windows:WebViewNavigationCompleted\",\n        WindowActive: \"windows:WindowActive\",\n        WindowBackgroundErase: \"windows:WindowBackgroundErase\",\n        WindowClickActive: \"windows:WindowClickActive\",\n        WindowClosing: \"windows:WindowClosing\",\n        WindowDidMove: \"windows:WindowDidMove\",\n        WindowDidResize: \"windows:WindowDidResize\",\n        WindowDPIChanged: \"windows:WindowDPIChanged\",\n        WindowDragDrop: \"windows:WindowDragDrop\",\n        WindowDragEnter: \"windows:WindowDragEnter\",\n        WindowDragLeave: \"windows:WindowDragLeave\",\n        WindowDragOver: \"windows:WindowDragOver\",\n        WindowEndMove: \"windows:WindowEndMove\",\n        WindowEndResize: \"windows:WindowEndResize\",\n        WindowFullscreen: \"windows:WindowFullscreen\",\n        WindowHide: \"windows:WindowHide\",\n        WindowInactive: \"windows:WindowInactive\",\n        WindowKeyDown: \"windows:WindowKeyDown\",\n        WindowKeyUp: \"windows:WindowKeyUp\",\n        WindowKillFocus: \"windows:WindowKillFocus\",\n        WindowNonClientHit: \"windows:WindowNonClientHit\",\n        WindowNonClientMouseDown: \"windows:WindowNonClientMouseDown\",\n        WindowNonClientMouseLeave: \"windows:WindowNonClientMouseLeave\",\n        WindowNonClientMouseMove: \"windows:WindowNonClientMouseMove\",\n        WindowNonClientMouseUp: \"windows:WindowNonClientMouseUp\",\n        WindowPaint: \"windows:WindowPaint\",\n        WindowRestore: \"windows:WindowRestore\",\n        WindowSetFocus: \"windows:WindowSetFocus\",\n        WindowShow: \"windows:WindowShow\",\n        WindowStartMove: \"windows:WindowStartMove\",\n        WindowStartResize: \"windows:WindowStartResize\",\n        WindowUnFullscreen: \"windows:WindowUnFullscreen\",\n        WindowZOrderChanged: \"windows:WindowZOrderChanged\",\n        WindowMinimise: \"windows:WindowMinimise\",\n        WindowUnMinimise: \"windows:WindowUnMinimise\",\n        WindowMaximise: \"windows:WindowMaximise\",\n        WindowUnMaximise: \"windows:WindowUnMaximise\",\n    }),\n    Mac: Object.freeze({\n        ApplicationDidBecomeActive: \"mac:ApplicationDidBecomeActive\",\n        ApplicationDidChangeBackingProperties: \"mac:ApplicationDidChangeBackingProperties\",\n        ApplicationDidChangeEffectiveAppearance: \"mac:ApplicationDidChangeEffectiveAppearance\",\n        ApplicationDidChangeIcon: \"mac:ApplicationDidChangeIcon\",\n        ApplicationDidChangeOcclusionState: \"mac:ApplicationDidChangeOcclusionState\",\n        ApplicationDidChangeScreenParameters: \"mac:ApplicationDidChangeScreenParameters\",\n        ApplicationDidChangeStatusBarFrame: \"mac:ApplicationDidChangeStatusBarFrame\",\n        ApplicationDidChangeStatusBarOrientation: \"mac:ApplicationDidChangeStatusBarOrientation\",\n        ApplicationDidChangeTheme: \"mac:ApplicationDidChangeTheme\",\n        ApplicationDidFinishLaunching: \"mac:ApplicationDidFinishLaunching\",\n        ApplicationDidHide: \"mac:ApplicationDidHide\",\n        ApplicationDidResignActive: \"mac:ApplicationDidResignActive\",\n        ApplicationDidUnhide: \"mac:ApplicationDidUnhide\",\n        ApplicationDidUpdate: \"mac:ApplicationDidUpdate\",\n        ApplicationShouldHandleReopen: \"mac:ApplicationShouldHandleReopen\",\n        ApplicationWillBecomeActive: \"mac:ApplicationWillBecomeActive\",\n        ApplicationWillFinishLaunching: \"mac:ApplicationWillFinishLaunching\",\n        ApplicationWillHide: \"mac:ApplicationWillHide\",\n        ApplicationWillResignActive: \"mac:ApplicationWillResignActive\",\n        ApplicationWillTerminate: \"mac:ApplicationWillTerminate\",\n        ApplicationWillUnhide: \"mac:ApplicationWillUnhide\",\n        ApplicationWillUpdate: \"mac:ApplicationWillUpdate\",\n        MenuDidAddItem: \"mac:MenuDidAddItem\",\n        MenuDidBeginTracking: \"mac:MenuDidBeginTracking\",\n        MenuDidClose: \"mac:MenuDidClose\",\n        MenuDidDisplayItem: \"mac:MenuDidDisplayItem\",\n        MenuDidEndTracking: \"mac:MenuDidEndTracking\",\n        MenuDidHighlightItem: \"mac:MenuDidHighlightItem\",\n        MenuDidOpen: \"mac:MenuDidOpen\",\n        MenuDidPopUp: \"mac:MenuDidPopUp\",\n        MenuDidRemoveItem: \"mac:MenuDidRemoveItem\",\n        MenuDidSendAction: \"mac:MenuDidSendAction\",\n        MenuDidSendActionToItem: \"mac:MenuDidSendActionToItem\",\n        MenuDidUpdate: \"mac:MenuDidUpdate\",\n        MenuWillAddItem: \"mac:MenuWillAddItem\",\n        MenuWillBeginTracking: \"mac:MenuWillBeginTracking\",\n        MenuWillDisplayItem: \"mac:MenuWillDisplayItem\",\n        MenuWillEndTracking: \"mac:MenuWillEndTracking\",\n        MenuWillHighlightItem: \"mac:MenuWillHighlightItem\",\n        MenuWillOpen: \"mac:MenuWillOpen\",\n        MenuWillPopUp: \"mac:MenuWillPopUp\",\n        MenuWillRemoveItem: \"mac:MenuWillRemoveItem\",\n        MenuWillSendAction: \"mac:MenuWillSendAction\",\n        MenuWillSendActionToItem: \"mac:MenuWillSendActionToItem\",\n        MenuWillUpdate: \"mac:MenuWillUpdate\",\n        WebViewDidCommitNavigation: \"mac:WebViewDidCommitNavigation\",\n        WebViewDidFinishNavigation: \"mac:WebViewDidFinishNavigation\",\n        WebViewDidReceiveServerRedirectForProvisionalNavigation: \"mac:WebViewDidReceiveServerRedirectForProvisionalNavigation\",\n        WebViewDidStartProvisionalNavigation: \"mac:WebViewDidStartProvisionalNavigation\",\n        WindowDidBecomeKey: \"mac:WindowDidBecomeKey\",\n        WindowDidBecomeMain: \"mac:WindowDidBecomeMain\",\n        WindowDidBeginSheet: \"mac:WindowDidBeginSheet\",\n        WindowDidChangeAlpha: \"mac:WindowDidChangeAlpha\",\n        WindowDidChangeBackingLocation: \"mac:WindowDidChangeBackingLocation\",\n        WindowDidChangeBackingProperties: \"mac:WindowDidChangeBackingProperties\",\n        WindowDidChangeCollectionBehavior: \"mac:WindowDidChangeCollectionBehavior\",\n        WindowDidChangeEffectiveAppearance: \"mac:WindowDidChangeEffectiveAppearance\",\n        WindowDidChangeOcclusionState: \"mac:WindowDidChangeOcclusionState\",\n        WindowDidChangeOrderingMode: \"mac:WindowDidChangeOrderingMode\",\n        WindowDidChangeScreen: \"mac:WindowDidChangeScreen\",\n        WindowDidChangeScreenParameters: \"mac:WindowDidChangeScreenParameters\",\n        WindowDidChangeScreenProfile: \"mac:WindowDidChangeScreenProfile\",\n        WindowDidChangeScreenSpace: \"mac:WindowDidChangeScreenSpace\",\n        WindowDidChangeScreenSpaceProperties: \"mac:WindowDidChangeScreenSpaceProperties\",\n        WindowDidChangeSharingType: \"mac:WindowDidChangeSharingType\",\n        WindowDidChangeSpace: \"mac:WindowDidChangeSpace\",\n        WindowDidChangeSpaceOrderingMode: \"mac:WindowDidChangeSpaceOrderingMode\",\n        WindowDidChangeTitle: \"mac:WindowDidChangeTitle\",\n        WindowDidChangeToolbar: \"mac:WindowDidChangeToolbar\",\n        WindowDidDeminiaturize: \"mac:WindowDidDeminiaturize\",\n        WindowDidEndSheet: \"mac:WindowDidEndSheet\",\n        WindowDidEnterFullScreen: \"mac:WindowDidEnterFullScreen\",\n        WindowDidEnterVersionBrowser: \"mac:WindowDidEnterVersionBrowser\",\n        WindowDidExitFullScreen: \"mac:WindowDidExitFullScreen\",\n        WindowDidExitVersionBrowser: \"mac:WindowDidExitVersionBrowser\",\n        WindowDidExpose: \"mac:WindowDidExpose\",\n        WindowDidFocus: \"mac:WindowDidFocus\",\n        WindowDidMiniaturize: \"mac:WindowDidMiniaturize\",\n        WindowDidMove: \"mac:WindowDidMove\",\n        WindowDidOrderOffScreen: \"mac:WindowDidOrderOffScreen\",\n        WindowDidOrderOnScreen: \"mac:WindowDidOrderOnScreen\",\n        WindowDidResignKey: \"mac:WindowDidResignKey\",\n        WindowDidResignMain: \"mac:WindowDidResignMain\",\n        WindowDidResize: \"mac:WindowDidResize\",\n        WindowDidUpdate: \"mac:WindowDidUpdate\",\n        WindowDidUpdateAlpha: \"mac:WindowDidUpdateAlpha\",\n        WindowDidUpdateCollectionBehavior: \"mac:WindowDidUpdateCollectionBehavior\",\n        WindowDidUpdateCollectionProperties: \"mac:WindowDidUpdateCollectionProperties\",\n        WindowDidUpdateShadow: \"mac:WindowDidUpdateShadow\",\n        WindowDidUpdateTitle: \"mac:WindowDidUpdateTitle\",\n        WindowDidUpdateToolbar: \"mac:WindowDidUpdateToolbar\",\n        WindowDidZoom: \"mac:WindowDidZoom\",\n        WindowFileDraggingEntered: \"mac:WindowFileDraggingEntered\",\n        WindowFileDraggingExited: \"mac:WindowFileDraggingExited\",\n        WindowFileDraggingPerformed: \"mac:WindowFileDraggingPerformed\",\n        WindowHide: \"mac:WindowHide\",\n        WindowMaximise: \"mac:WindowMaximise\",\n        WindowUnMaximise: \"mac:WindowUnMaximise\",\n        WindowMinimise: \"mac:WindowMinimise\",\n        WindowUnMinimise: \"mac:WindowUnMinimise\",\n        WindowShouldClose: \"mac:WindowShouldClose\",\n        WindowShow: \"mac:WindowShow\",\n        WindowWillBecomeKey: \"mac:WindowWillBecomeKey\",\n        WindowWillBecomeMain: \"mac:WindowWillBecomeMain\",\n        WindowWillBeginSheet: \"mac:WindowWillBeginSheet\",\n        WindowWillChangeOrderingMode: \"mac:WindowWillChangeOrderingMode\",\n        WindowWillClose: \"mac:WindowWillClose\",\n        WindowWillDeminiaturize: \"mac:WindowWillDeminiaturize\",\n        WindowWillEnterFullScreen: \"mac:WindowWillEnterFullScreen\",\n        WindowWillEnterVersionBrowser: \"mac:WindowWillEnterVersionBrowser\",\n        WindowWillExitFullScreen: \"mac:WindowWillExitFullScreen\",\n        WindowWillExitVersionBrowser: \"mac:WindowWillExitVersionBrowser\",\n        WindowWillFocus: \"mac:WindowWillFocus\",\n        WindowWillMiniaturize: \"mac:WindowWillMiniaturize\",\n        WindowWillMove: \"mac:WindowWillMove\",\n        WindowWillOrderOffScreen: \"mac:WindowWillOrderOffScreen\",\n        WindowWillOrderOnScreen: \"mac:WindowWillOrderOnScreen\",\n        WindowWillResignMain: \"mac:WindowWillResignMain\",\n        WindowWillResize: \"mac:WindowWillResize\",\n        WindowWillUnfocus: \"mac:WindowWillUnfocus\",\n        WindowWillUpdate: \"mac:WindowWillUpdate\",\n        WindowWillUpdateAlpha: \"mac:WindowWillUpdateAlpha\",\n        WindowWillUpdateCollectionBehavior: \"mac:WindowWillUpdateCollectionBehavior\",\n        WindowWillUpdateCollectionProperties: \"mac:WindowWillUpdateCollectionProperties\",\n        WindowWillUpdateShadow: \"mac:WindowWillUpdateShadow\",\n        WindowWillUpdateTitle: \"mac:WindowWillUpdateTitle\",\n        WindowWillUpdateToolbar: \"mac:WindowWillUpdateToolbar\",\n        WindowWillUpdateVisibility: \"mac:WindowWillUpdateVisibility\",\n        WindowWillUseStandardFrame: \"mac:WindowWillUseStandardFrame\",\n        WindowZoomIn: \"mac:WindowZoomIn\",\n        WindowZoomOut: \"mac:WindowZoomOut\",\n        WindowZoomReset: \"mac:WindowZoomReset\",\n    }),\n    Linux: Object.freeze({\n        ApplicationStartup: \"linux:ApplicationStartup\",\n        SystemThemeChanged: \"linux:SystemThemeChanged\",\n        WindowDeleteEvent: \"linux:WindowDeleteEvent\",\n        WindowDidMove: \"linux:WindowDidMove\",\n        WindowDidResize: \"linux:WindowDidResize\",\n        WindowFocusIn: \"linux:WindowFocusIn\",\n        WindowFocusOut: \"linux:WindowFocusOut\",\n        WindowLoadChanged: \"linux:WindowLoadChanged\",\n    }),\n    Common: Object.freeze({\n        ApplicationOpenedWithFile: \"common:ApplicationOpenedWithFile\",\n        ApplicationStarted: \"common:ApplicationStarted\",\n        ThemeChanged: \"common:ThemeChanged\",\n        WindowClosing: \"common:WindowClosing\",\n        WindowDidMove: \"common:WindowDidMove\",\n        WindowDidResize: \"common:WindowDidResize\",\n        WindowDPIChanged: \"common:WindowDPIChanged\",\n        WindowFilesDropped: \"common:WindowFilesDropped\",\n        WindowFocus: \"common:WindowFocus\",\n        WindowFullscreen: \"common:WindowFullscreen\",\n        WindowHide: \"common:WindowHide\",\n        WindowLostFocus: \"common:WindowLostFocus\",\n        WindowMaximise: \"common:WindowMaximise\",\n        WindowMinimise: \"common:WindowMinimise\",\n        WindowRestore: \"common:WindowRestore\",\n        WindowRuntimeReady: \"common:WindowRuntimeReady\",\n        WindowShow: \"common:WindowShow\",\n        WindowUnFullscreen: \"common:WindowUnFullscreen\",\n        WindowUnMaximise: \"common:WindowUnMaximise\",\n        WindowUnMinimise: \"common:WindowUnMinimise\",\n        WindowZoom: \"common:WindowZoom\",\n        WindowZoomIn: \"common:WindowZoomIn\",\n        WindowZoomOut: \"common:WindowZoomOut\",\n        WindowZoomReset: \"common:WindowZoomReset\",\n    }),\n});\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) Lea Anthony 2019-present\n*/\nimport { newRuntimeCaller, objectNames } from \"./runtime.js\";\nconst call = newRuntimeCaller(objectNames.Screens);\nconst getAll = 0;\nconst getPrimary = 1;\nconst getCurrent = 2;\n/**\n * Gets all screens.\n *\n * @returns A promise that resolves to an array of Screen objects.\n */\nexport function GetAll() {\n    return call(getAll);\n}\n/**\n * Gets the primary screen.\n *\n * @returns A promise that resolves to the primary screen.\n */\nexport function GetPrimary() {\n    return call(getPrimary);\n}\n/**\n * Gets the current active screen.\n *\n * @returns A promise that resolves with the current active screen.\n */\nexport function GetCurrent() {\n    return call(getCurrent);\n}\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) <PERSON> Anthony 2019-present\n*/\nimport { newRuntimeCaller, objectNames } from \"./runtime.js\";\nconst PositionMethod = 0;\nconst CenterMethod = 1;\nconst CloseMethod = 2;\nconst DisableSizeConstraintsMethod = 3;\nconst EnableSizeConstraintsMethod = 4;\nconst FocusMethod = 5;\nconst ForceReloadMethod = 6;\nconst FullscreenMethod = 7;\nconst GetScreenMethod = 8;\nconst GetZoomMethod = 9;\nconst HeightMethod = 10;\nconst HideMethod = 11;\nconst IsFocusedMethod = 12;\nconst IsFullscreenMethod = 13;\nconst IsMaximisedMethod = 14;\nconst IsMinimisedMethod = 15;\nconst MaximiseMethod = 16;\nconst MinimiseMethod = 17;\nconst NameMethod = 18;\nconst OpenDevToolsMethod = 19;\nconst RelativePositionMethod = 20;\nconst ReloadMethod = 21;\nconst ResizableMethod = 22;\nconst RestoreMethod = 23;\nconst SetPositionMethod = 24;\nconst SetAlwaysOnTopMethod = 25;\nconst SetBackgroundColourMethod = 26;\nconst SetFramelessMethod = 27;\nconst SetFullscreenButtonEnabledMethod = 28;\nconst SetMaxSizeMethod = 29;\nconst SetMinSizeMethod = 30;\nconst SetRelativePositionMethod = 31;\nconst SetResizableMethod = 32;\nconst SetSizeMethod = 33;\nconst SetTitleMethod = 34;\nconst SetZoomMethod = 35;\nconst ShowMethod = 36;\nconst SizeMethod = 37;\nconst ToggleFullscreenMethod = 38;\nconst ToggleMaximiseMethod = 39;\nconst UnFullscreenMethod = 40;\nconst UnMaximiseMethod = 41;\nconst UnMinimiseMethod = 42;\nconst WidthMethod = 43;\nconst ZoomMethod = 44;\nconst ZoomInMethod = 45;\nconst ZoomOutMethod = 46;\nconst ZoomResetMethod = 47;\n// Private field names.\nconst callerSym = Symbol(\"caller\");\nclass Window {\n    /**\n     * Initialises a window object with the specified name.\n     *\n     * @private\n     * @param name - The name of the target window.\n     */\n    constructor(name = '') {\n        this[callerSym] = newRuntimeCaller(objectNames.Window, name);\n        // bind instance method to make them easily usable in event handlers\n        for (const method of Object.getOwnPropertyNames(Window.prototype)) {\n            if (method !== \"constructor\"\n                && typeof this[method] === \"function\") {\n                this[method] = this[method].bind(this);\n            }\n        }\n    }\n    /**\n     * Gets the specified window.\n     *\n     * @param name - The name of the window to get.\n     * @returns The corresponding window object.\n     */\n    Get(name) {\n        return new Window(name);\n    }\n    /**\n     * Returns the absolute position of the window.\n     *\n     * @returns The current absolute position of the window.\n     */\n    Position() {\n        return this[callerSym](PositionMethod);\n    }\n    /**\n     * Centers the window on the screen.\n     */\n    Center() {\n        return this[callerSym](CenterMethod);\n    }\n    /**\n     * Closes the window.\n     */\n    Close() {\n        return this[callerSym](CloseMethod);\n    }\n    /**\n     * Disables min/max size constraints.\n     */\n    DisableSizeConstraints() {\n        return this[callerSym](DisableSizeConstraintsMethod);\n    }\n    /**\n     * Enables min/max size constraints.\n     */\n    EnableSizeConstraints() {\n        return this[callerSym](EnableSizeConstraintsMethod);\n    }\n    /**\n     * Focuses the window.\n     */\n    Focus() {\n        return this[callerSym](FocusMethod);\n    }\n    /**\n     * Forces the window to reload the page assets.\n     */\n    ForceReload() {\n        return this[callerSym](ForceReloadMethod);\n    }\n    /**\n     * Switches the window to fullscreen mode.\n     */\n    Fullscreen() {\n        return this[callerSym](FullscreenMethod);\n    }\n    /**\n     * Returns the screen that the window is on.\n     *\n     * @returns The screen the window is currently on.\n     */\n    GetScreen() {\n        return this[callerSym](GetScreenMethod);\n    }\n    /**\n     * Returns the current zoom level of the window.\n     *\n     * @returns The current zoom level.\n     */\n    GetZoom() {\n        return this[callerSym](GetZoomMethod);\n    }\n    /**\n     * Returns the height of the window.\n     *\n     * @returns The current height of the window.\n     */\n    Height() {\n        return this[callerSym](HeightMethod);\n    }\n    /**\n     * Hides the window.\n     */\n    Hide() {\n        return this[callerSym](HideMethod);\n    }\n    /**\n     * Returns true if the window is focused.\n     *\n     * @returns Whether the window is currently focused.\n     */\n    IsFocused() {\n        return this[callerSym](IsFocusedMethod);\n    }\n    /**\n     * Returns true if the window is fullscreen.\n     *\n     * @returns Whether the window is currently fullscreen.\n     */\n    IsFullscreen() {\n        return this[callerSym](IsFullscreenMethod);\n    }\n    /**\n     * Returns true if the window is maximised.\n     *\n     * @returns Whether the window is currently maximised.\n     */\n    IsMaximised() {\n        return this[callerSym](IsMaximisedMethod);\n    }\n    /**\n     * Returns true if the window is minimised.\n     *\n     * @returns Whether the window is currently minimised.\n     */\n    IsMinimised() {\n        return this[callerSym](IsMinimisedMethod);\n    }\n    /**\n     * Maximises the window.\n     */\n    Maximise() {\n        return this[callerSym](MaximiseMethod);\n    }\n    /**\n     * Minimises the window.\n     */\n    Minimise() {\n        return this[callerSym](MinimiseMethod);\n    }\n    /**\n     * Returns the name of the window.\n     *\n     * @returns The name of the window.\n     */\n    Name() {\n        return this[callerSym](NameMethod);\n    }\n    /**\n     * Opens the development tools pane.\n     */\n    OpenDevTools() {\n        return this[callerSym](OpenDevToolsMethod);\n    }\n    /**\n     * Returns the relative position of the window to the screen.\n     *\n     * @returns The current relative position of the window.\n     */\n    RelativePosition() {\n        return this[callerSym](RelativePositionMethod);\n    }\n    /**\n     * Reloads the page assets.\n     */\n    Reload() {\n        return this[callerSym](ReloadMethod);\n    }\n    /**\n     * Returns true if the window is resizable.\n     *\n     * @returns Whether the window is currently resizable.\n     */\n    Resizable() {\n        return this[callerSym](ResizableMethod);\n    }\n    /**\n     * Restores the window to its previous state if it was previously minimised, maximised or fullscreen.\n     */\n    Restore() {\n        return this[callerSym](RestoreMethod);\n    }\n    /**\n     * Sets the absolute position of the window.\n     *\n     * @param x - The desired horizontal absolute position of the window.\n     * @param y - The desired vertical absolute position of the window.\n     */\n    SetPosition(x, y) {\n        return this[callerSym](SetPositionMethod, { x, y });\n    }\n    /**\n     * Sets the window to be always on top.\n     *\n     * @param alwaysOnTop - Whether the window should stay on top.\n     */\n    SetAlwaysOnTop(alwaysOnTop) {\n        return this[callerSym](SetAlwaysOnTopMethod, { alwaysOnTop });\n    }\n    /**\n     * Sets the background colour of the window.\n     *\n     * @param r - The desired red component of the window background.\n     * @param g - The desired green component of the window background.\n     * @param b - The desired blue component of the window background.\n     * @param a - The desired alpha component of the window background.\n     */\n    SetBackgroundColour(r, g, b, a) {\n        return this[callerSym](SetBackgroundColourMethod, { r, g, b, a });\n    }\n    /**\n     * Removes the window frame and title bar.\n     *\n     * @param frameless - Whether the window should be frameless.\n     */\n    SetFrameless(frameless) {\n        return this[callerSym](SetFramelessMethod, { frameless });\n    }\n    /**\n     * Disables the system fullscreen button.\n     *\n     * @param enabled - Whether the fullscreen button should be enabled.\n     */\n    SetFullscreenButtonEnabled(enabled) {\n        return this[callerSym](SetFullscreenButtonEnabledMethod, { enabled });\n    }\n    /**\n     * Sets the maximum size of the window.\n     *\n     * @param width - The desired maximum width of the window.\n     * @param height - The desired maximum height of the window.\n     */\n    SetMaxSize(width, height) {\n        return this[callerSym](SetMaxSizeMethod, { width, height });\n    }\n    /**\n     * Sets the minimum size of the window.\n     *\n     * @param width - The desired minimum width of the window.\n     * @param height - The desired minimum height of the window.\n     */\n    SetMinSize(width, height) {\n        return this[callerSym](SetMinSizeMethod, { width, height });\n    }\n    /**\n     * Sets the relative position of the window to the screen.\n     *\n     * @param x - The desired horizontal relative position of the window.\n     * @param y - The desired vertical relative position of the window.\n     */\n    SetRelativePosition(x, y) {\n        return this[callerSym](SetRelativePositionMethod, { x, y });\n    }\n    /**\n     * Sets whether the window is resizable.\n     *\n     * @param resizable - Whether the window should be resizable.\n     */\n    SetResizable(resizable) {\n        return this[callerSym](SetResizableMethod, { resizable });\n    }\n    /**\n     * Sets the size of the window.\n     *\n     * @param width - The desired width of the window.\n     * @param height - The desired height of the window.\n     */\n    SetSize(width, height) {\n        return this[callerSym](SetSizeMethod, { width, height });\n    }\n    /**\n     * Sets the title of the window.\n     *\n     * @param title - The desired title of the window.\n     */\n    SetTitle(title) {\n        return this[callerSym](SetTitleMethod, { title });\n    }\n    /**\n     * Sets the zoom level of the window.\n     *\n     * @param zoom - The desired zoom level.\n     */\n    SetZoom(zoom) {\n        return this[callerSym](SetZoomMethod, { zoom });\n    }\n    /**\n     * Shows the window.\n     */\n    Show() {\n        return this[callerSym](ShowMethod);\n    }\n    /**\n     * Returns the size of the window.\n     *\n     * @returns The current size of the window.\n     */\n    Size() {\n        return this[callerSym](SizeMethod);\n    }\n    /**\n     * Toggles the window between fullscreen and normal.\n     */\n    ToggleFullscreen() {\n        return this[callerSym](ToggleFullscreenMethod);\n    }\n    /**\n     * Toggles the window between maximised and normal.\n     */\n    ToggleMaximise() {\n        return this[callerSym](ToggleMaximiseMethod);\n    }\n    /**\n     * Un-fullscreens the window.\n     */\n    UnFullscreen() {\n        return this[callerSym](UnFullscreenMethod);\n    }\n    /**\n     * Un-maximises the window.\n     */\n    UnMaximise() {\n        return this[callerSym](UnMaximiseMethod);\n    }\n    /**\n     * Un-minimises the window.\n     */\n    UnMinimise() {\n        return this[callerSym](UnMinimiseMethod);\n    }\n    /**\n     * Returns the width of the window.\n     *\n     * @returns The current width of the window.\n     */\n    Width() {\n        return this[callerSym](WidthMethod);\n    }\n    /**\n     * Zooms the window.\n     */\n    Zoom() {\n        return this[callerSym](ZoomMethod);\n    }\n    /**\n     * Increases the zoom level of the webview content.\n     */\n    ZoomIn() {\n        return this[callerSym](ZoomInMethod);\n    }\n    /**\n     * Decreases the zoom level of the webview content.\n     */\n    ZoomOut() {\n        return this[callerSym](ZoomOutMethod);\n    }\n    /**\n     * Resets the zoom level of the webview content.\n     */\n    ZoomReset() {\n        return this[callerSym](ZoomResetMethod);\n    }\n}\n/**\n * The window within which the script is running.\n */\nconst thisWindow = new Window('');\nexport default thisWindow;\n", "/*\n _     __     _ __\n| |  / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) <PERSON> Anthony 2019-present\n*/\nimport { OpenURL } from \"./browser.js\";\nimport { Question } from \"./dialogs.js\";\nimport { Emit, WailsEvent } from \"./events.js\";\nimport { canAbortListeners, whenReady } from \"./utils.js\";\nimport Window from \"./window.js\";\n/**\n * Sends an event with the given name and optional data.\n *\n * @param eventName - - The name of the event to send.\n * @param [data=null] - - Optional data to send along with the event.\n */\nfunction sendEvent(eventName, data = null) {\n    Emit(new WailsEvent(eventName, data));\n}\n/**\n * Calls a method on a specified window.\n *\n * @param windowName - The name of the window to call the method on.\n * @param methodName - The name of the method to call.\n */\nfunction callWindowMethod(windowName, methodName) {\n    const targetWindow = Window.Get(windowName);\n    const method = targetWindow[methodName];\n    if (typeof method !== \"function\") {\n        console.error(`Window method '${methodName}' not found`);\n        return;\n    }\n    try {\n        method.call(targetWindow);\n    }\n    catch (e) {\n        console.error(`Error calling window method '${methodName}': `, e);\n    }\n}\n/**\n * Responds to a triggering event by running appropriate WML actions for the current target.\n */\nfunction onWMLTriggered(ev) {\n    const element = ev.currentTarget;\n    function runEffect(choice = \"Yes\") {\n        if (choice !== \"Yes\")\n            return;\n        const eventType = element.getAttribute('wml-event') || element.getAttribute('data-wml-event');\n        const targetWindow = element.getAttribute('wml-target-window') || element.getAttribute('data-wml-target-window') || \"\";\n        const windowMethod = element.getAttribute('wml-window') || element.getAttribute('data-wml-window');\n        const url = element.getAttribute('wml-openurl') || element.getAttribute('data-wml-openurl');\n        if (eventType !== null)\n            sendEvent(eventType);\n        if (windowMethod !== null)\n            callWindowMethod(targetWindow, windowMethod);\n        if (url !== null)\n            void OpenURL(url);\n    }\n    const confirm = element.getAttribute('wml-confirm') || element.getAttribute('data-wml-confirm');\n    if (confirm) {\n        Question({\n            Title: \"Confirm\",\n            Message: confirm,\n            Detached: false,\n            Buttons: [\n                { Label: \"Yes\" },\n                { Label: \"No\", IsDefault: true }\n            ]\n        }).then(runEffect);\n    }\n    else {\n        runEffect();\n    }\n}\n// Private field names.\nconst controllerSym = Symbol(\"controller\");\nconst triggerMapSym = Symbol(\"triggerMap\");\nconst elementCountSym = Symbol(\"elementCount\");\n/**\n * AbortControllerRegistry does not actually remember active event listeners: instead\n * it ties them to an AbortSignal and uses an AbortController to remove them all at once.\n */\nclass AbortControllerRegistry {\n    constructor() {\n        this[controllerSym] = new AbortController();\n    }\n    /**\n     * Returns an options object for addEventListener that ties the listener\n     * to the AbortSignal from the current AbortController.\n     *\n     * @param element - An HTML element\n     * @param triggers - The list of active WML trigger events for the specified elements\n     */\n    set(element, triggers) {\n        return { signal: this[controllerSym].signal };\n    }\n    /**\n     * Removes all registered event listeners and resets the registry.\n     */\n    reset() {\n        this[controllerSym].abort();\n        this[controllerSym] = new AbortController();\n    }\n}\n/**\n * WeakMapRegistry maps active trigger events to each DOM element through a WeakMap.\n * This ensures that the mapping remains private to this module, while still allowing garbage\n * collection of the involved elements.\n */\nclass WeakMapRegistry {\n    constructor() {\n        this[triggerMapSym] = new WeakMap();\n        this[elementCountSym] = 0;\n    }\n    /**\n     * Sets active triggers for the specified element.\n     *\n     * @param element - An HTML element\n     * @param triggers - The list of active WML trigger events for the specified element\n     */\n    set(element, triggers) {\n        if (!this[triggerMapSym].has(element)) {\n            this[elementCountSym]++;\n        }\n        this[triggerMapSym].set(element, triggers);\n        return {};\n    }\n    /**\n     * Removes all registered event listeners.\n     */\n    reset() {\n        if (this[elementCountSym] <= 0)\n            return;\n        for (const element of document.body.querySelectorAll('*')) {\n            if (this[elementCountSym] <= 0)\n                break;\n            const triggers = this[triggerMapSym].get(element);\n            if (triggers != null) {\n                this[elementCountSym]--;\n            }\n            for (const trigger of triggers || [])\n                element.removeEventListener(trigger, onWMLTriggered);\n        }\n        this[triggerMapSym] = new WeakMap();\n        this[elementCountSym] = 0;\n    }\n}\nconst triggerRegistry = canAbortListeners() ? new AbortControllerRegistry() : new WeakMapRegistry();\n/**\n * Adds event listeners to the specified element.\n */\nfunction addWMLListeners(element) {\n    const triggerRegExp = /\\S+/g;\n    const triggerAttr = (element.getAttribute('wml-trigger') || element.getAttribute('data-wml-trigger') || \"click\");\n    const triggers = [];\n    let match;\n    while ((match = triggerRegExp.exec(triggerAttr)) !== null)\n        triggers.push(match[0]);\n    const options = triggerRegistry.set(element, triggers);\n    for (const trigger of triggers)\n        element.addEventListener(trigger, onWMLTriggered, options);\n}\n/**\n * Schedules an automatic reload of WML to be performed as soon as the document is fully loaded.\n */\nexport function Enable() {\n    whenReady(Reload);\n}\n/**\n * Reloads the WML page by adding necessary event listeners and browser listeners.\n */\nexport function Reload() {\n    triggerRegistry.reset();\n    document.body.querySelectorAll('[wml-event], [wml-window], [wml-openurl], [data-wml-event], [data-wml-window], [data-wml-openurl]').forEach(addWMLListeners);\n}\n", "/*\n _\t   __\t  _ __\n| |\t / /___ _(_) /____\n| | /| / / __ `/ / / ___/\n| |/ |/ / /_/ / / (__  )\n|__/|__/\\__,_/_/_/____/\nThe electron alternative for Go\n(c) Lea Anthony 2019-present\n*/\n// Setup\nwindow._wails = window._wails || {};\nimport \"./contextmenu.js\";\nimport \"./drag.js\";\n// Re-export public API\nimport * as Application from \"./application.js\";\nimport * as Browser from \"./browser.js\";\nimport * as Call from \"./calls.js\";\nimport * as Clipboard from \"./clipboard.js\";\nimport * as Create from \"./create.js\";\nimport * as Dialogs from \"./dialogs.js\";\nimport * as Events from \"./events.js\";\nimport * as Flags from \"./flags.js\";\nimport * as Screens from \"./screens.js\";\nimport * as System from \"./system.js\";\nimport Window from \"./window.js\";\nimport * as WML from \"./wml.js\";\nexport { Application, Browser, Call, Clipboard, Dialogs, Events, Flags, Screens, System, Window, WML };\n/**\n * An internal utility consumed by the binding generator.\n *\n * @ignore\n * @internal\n */\nexport { Create };\nexport * from \"./cancellable.js\";\n// Notify backend\nwindow._wails.invoke = System.invoke;\nSystem.invoke(\"wails:runtime:ready\");\n"], "mappings": ";;;;;;AA2BA,IAAM,cAAc;AACb,SAAS,OAAO,OAAO,IAAI;AAC9B,MAAI,KAAK;AAET,MAAI,IAAI,OAAO;AACf,SAAO,KAAK;AAER,UAAM,YAAa,KAAK,OAAO,IAAI,KAAM,CAAC;AAAA,EAC9C;AACA,SAAO;AACX;;;AC3BA,IAAM,aAAa,OAAO,SAAS,SAAS;AAErC,IAAM,cAAc,OAAO,OAAO;AAAA,EACrC,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AAChB,CAAC;AACM,IAAI,WAAW,OAAO;AAQtB,SAAS,iBAAiB,QAAQ,aAAa,IAAI;AACtD,SAAO,SAAU,QAAQ,OAAO,MAAM;AAClC,WAAO,kBAAkB,QAAQ,QAAQ,YAAY,IAAI;AAAA,EAC7D;AACJ;AACA,SAAe,kBAAkB,UAAU,QAAQ,YAAY,MAAM;AAAA;AACjE,QAAIA,KAAI;AACR,QAAI,MAAM,IAAI,IAAI,UAAU;AAC5B,QAAI,aAAa,OAAO,UAAU,SAAS,SAAS,CAAC;AACrD,QAAI,aAAa,OAAO,UAAU,OAAO,SAAS,CAAC;AACnD,QAAI,MAAM;AACN,UAAI,aAAa,OAAO,QAAQ,KAAK,UAAU,IAAI,CAAC;AAAA,IACxD;AACA,QAAI,UAAU;AAAA,MACV,CAAC,mBAAmB,GAAG;AAAA,IAC3B;AACA,QAAI,YAAY;AACZ,cAAQ,qBAAqB,IAAI;AAAA,IACrC;AACA,QAAI,WAAW,MAAM,MAAM,KAAK,EAAE,QAAQ,CAAC;AAC3C,QAAI,CAAC,SAAS,IAAI;AACd,YAAM,IAAI,MAAM,MAAM,SAAS,KAAK,CAAC;AAAA,IACzC;AACA,UAAM,MAAMA,MAAK,SAAS,QAAQ,IAAI,cAAc,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,QAAQ,kBAAkB,OAAO,QAAQ,OAAO,SAAS,KAAK,QAAQ,IAAI;AACrK,aAAO,SAAS,KAAK;AAAA,IACzB,OACK;AACD,aAAO,SAAS,KAAK;AAAA,IACzB;AAAA,EACJ;AAAA;;;AC9DA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,IAAM,OAAO,iBAAiB,YAAY,MAAM;AAChD,IAAM,mBAAmB;AACzB,IAAM,oBAAoB;AAC1B,IAAM,UAAW,WAAY;AACzB,MAAIC,KAAI,IAAI,IAAI,IAAI;AACpB,MAAI;AACA,SAAK,MAAMA,MAAK,OAAO,YAAY,QAAQA,QAAO,SAAS,SAASA,IAAG,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa;AACjI,aAAO,OAAO,OAAO,QAAQ,YAAY,KAAK,OAAO,OAAO,OAAO;AAAA,IACvE,YACU,MAAM,MAAM,KAAK,OAAO,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa;AACzM,aAAO,OAAO,OAAO,gBAAgB,UAAU,EAAE,YAAY,KAAK,OAAO,OAAO,gBAAgB,UAAU,CAAC;AAAA,IAC/G;AAAA,EACJ,SACO,GAAG;AAAA,EAAE;AACZ,UAAQ,KAAK,2PAA2P,4HAA4H,4BAA4B,wDAAwD;AACxd,SAAO;AACX,EAAG;AACI,SAAS,OAAO,KAAK;AACxB,cAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,GAAG;AACjE;AAMO,SAAS,aAAa;AACzB,SAAO,KAAK,gBAAgB;AAChC;AAMA,SAAsB,eAAe;AAAA;AACjC,QAAI,WAAW,MAAM,MAAM,qBAAqB;AAChD,QAAI,SAAS,IAAI;AACb,aAAO,SAAS,KAAK;AAAA,IACzB,OACK;AACD,YAAM,IAAI,MAAM,mCAAmC,SAAS,UAAU;AAAA,IAC1E;AAAA,EACJ;AAAA;AAMO,SAAS,cAAc;AAC1B,SAAO,KAAK,iBAAiB;AACjC;AAMO,SAAS,YAAY;AACxB,SAAO,OAAO,OAAO,YAAY,OAAO;AAC5C;AAMO,SAAS,UAAU;AACtB,SAAO,OAAO,OAAO,YAAY,OAAO;AAC5C;AAMO,SAAS,QAAQ;AACpB,SAAO,OAAO,OAAO,YAAY,OAAO;AAC5C;AAMO,SAAS,UAAU;AACtB,SAAO,OAAO,OAAO,YAAY,SAAS;AAC9C;AAMO,SAAS,QAAQ;AACpB,SAAO,OAAO,OAAO,YAAY,SAAS;AAC9C;AAMO,SAAS,UAAU;AACtB,SAAO,OAAO,OAAO,YAAY,SAAS;AAC9C;AAMO,SAAS,UAAU;AACtB,SAAO,QAAQ,OAAO,OAAO,YAAY,KAAK;AAClD;;;AC7FO,SAAS,kBAAkB;AAC9B,SAAQ,IAAI,WAAW,WAAW,EAAG,YAAY;AACrD;AAKO,SAAS,oBAAoB;AAChC,MAAI,CAAC,eAAe,CAAC,eAAe,CAAC;AACjC,WAAO;AACX,MAAI,SAAS;AACb,QAAM,SAAS,IAAI,YAAY;AAC/B,QAAM,aAAa,IAAI,gBAAgB;AACvC,SAAO,iBAAiB,QAAQ,MAAM;AAAE,aAAS;AAAA,EAAO,GAAG,EAAE,QAAQ,WAAW,OAAO,CAAC;AACxF,aAAW,MAAM;AACjB,SAAO,cAAc,IAAI,YAAY,MAAM,CAAC;AAC5C,SAAO;AACX;AAIO,SAAS,YAAY,OAAO;AAC/B,MAAIC;AACJ,MAAI,MAAM,kBAAkB,aAAa;AACrC,WAAO,MAAM;AAAA,EACjB,WACS,EAAE,MAAM,kBAAkB,gBAAgB,MAAM,kBAAkB,MAAM;AAC7E,YAAQA,MAAK,MAAM,OAAO,mBAAmB,QAAQA,QAAO,SAASA,MAAK,SAAS;AAAA,EACvF,OACK;AACD,WAAO,SAAS;AAAA,EACpB;AACJ;AA+BA,IAAI,UAAU;AACd,SAAS,iBAAiB,oBAAoB,MAAM;AAAE,YAAU;AAAM,CAAC;AAChE,SAAS,UAAU,UAAU;AAChC,MAAI,WAAW,SAAS,eAAe,YAAY;AAC/C,aAAS;AAAA,EACb,OACK;AACD,aAAS,iBAAiB,oBAAoB,QAAQ;AAAA,EAC1D;AACJ;;;ACjFA,OAAO,iBAAiB,eAAe,kBAAkB;AACzD,IAAMC,QAAO,iBAAiB,YAAY,WAAW;AACrD,IAAM,kBAAkB;AACxB,SAAS,gBAAgB,IAAI,GAAG,GAAG,MAAM;AACrC,OAAKA,MAAK,iBAAiB,EAAE,IAAI,GAAG,GAAG,KAAK,CAAC;AACjD;AACA,SAAS,mBAAmB,OAAO;AAC/B,QAAM,SAAS,YAAY,KAAK;AAEhC,QAAM,oBAAoB,OAAO,iBAAiB,MAAM,EAAE,iBAAiB,sBAAsB,EAAE,KAAK;AACxG,MAAI,mBAAmB;AACnB,UAAM,eAAe;AACrB,UAAM,OAAO,OAAO,iBAAiB,MAAM,EAAE,iBAAiB,2BAA2B;AACzF,oBAAgB,mBAAmB,MAAM,SAAS,MAAM,SAAS,IAAI;AAAA,EACzE,OACK;AACD,8BAA0B,OAAO,MAAM;AAAA,EAC3C;AACJ;AAQA,SAAS,0BAA0B,OAAO,QAAQ;AAE9C,MAAI,QAAQ,GAAG;AACX;AAAA,EACJ;AAEA,UAAQ,OAAO,iBAAiB,MAAM,EAAE,iBAAiB,uBAAuB,EAAE,KAAK,GAAG;AAAA,IACtF,KAAK;AACD;AAAA,IACJ,KAAK;AACD,YAAM,eAAe;AACrB;AAAA,EACR;AAEA,MAAI,OAAO,mBAAmB;AAC1B;AAAA,EACJ;AAEA,QAAM,YAAY,OAAO,aAAa;AACtC,QAAM,eAAe,aAAa,UAAU,SAAS,EAAE,SAAS;AAChE,MAAI,cAAc;AACd,aAAS,IAAI,GAAG,IAAI,UAAU,YAAY,KAAK;AAC3C,YAAM,QAAQ,UAAU,WAAW,CAAC;AACpC,YAAM,QAAQ,MAAM,eAAe;AACnC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,cAAM,OAAO,MAAM,CAAC;AACpB,YAAI,SAAS,iBAAiB,KAAK,MAAM,KAAK,GAAG,MAAM,QAAQ;AAC3D;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI,kBAAkB,oBAAoB,kBAAkB,qBAAqB;AAC7E,QAAI,gBAAiB,CAAC,OAAO,YAAY,CAAC,OAAO,UAAW;AACxD;AAAA,IACJ;AAAA,EACJ;AAEA,QAAM,eAAe;AACzB;;;AC/EA;AAAA;AAAA;AAAA;AAeO,SAAS,QAAQ,KAAK;AACzB,MAAI;AACA,WAAO,OAAO,OAAO,MAAM,GAAG;AAAA,EAClC,SACO,GAAG;AACN,UAAM,IAAI,MAAM,8BAA8B,MAAM,QAAQ,GAAG,EAAE,OAAO,EAAE,CAAC;AAAA,EAC/E;AACJ;;;ACTA,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,aAAa;AACjB,IAAI,gBAAgB;AACpB,IAAI,UAAU;AACd,IAAM,iBAAiB,gBAAgB;AACvC,OAAO,SAAS,OAAO,UAAU,CAAC;AAClC,OAAO,OAAO,eAAe,CAAC,UAAU;AACpC,cAAY;AACZ,MAAI,CAAC,WAAW;AAEZ,gBAAY,WAAW;AACvB,cAAU;AAAA,EACd;AACJ;AACA,OAAO,iBAAiB,aAAa,QAAQ,EAAE,SAAS,KAAK,CAAC;AAC9D,OAAO,iBAAiB,aAAa,QAAQ,EAAE,SAAS,KAAK,CAAC;AAC9D,OAAO,iBAAiB,WAAW,QAAQ,EAAE,SAAS,KAAK,CAAC;AAC5D,WAAW,MAAM,CAAC,SAAS,eAAe,UAAU,GAAG;AACnD,SAAO,iBAAiB,IAAI,eAAe,EAAE,SAAS,KAAK,CAAC;AAChE;AACA,SAAS,cAAc,OAAO;AAE1B,MAAI,YAAY,UAAU;AACtB,UAAM,yBAAyB;AAC/B,UAAM,gBAAgB;AACtB,UAAM,eAAe;AAAA,EACzB;AACJ;AAEA,IAAM,YAAY;AAClB,IAAM,UAAU;AAChB,IAAM,YAAY;AAClB,SAAS,OAAO,OAAO;AAGnB,MAAI,WAAW,eAAe,MAAM;AACpC,UAAQ,MAAM,MAAM;AAAA,IAChB,KAAK;AACD,kBAAY;AACZ,UAAI,CAAC,gBAAgB;AACjB,uBAAe,UAAW,KAAK,MAAM;AAAA,MACzC;AACA;AAAA,IACJ,KAAK;AACD,kBAAY;AACZ,UAAI,CAAC,gBAAgB;AACjB,uBAAe,UAAU,EAAE,KAAK,MAAM;AAAA,MAC1C;AACA;AAAA,IACJ;AACI,kBAAY;AACZ,UAAI,CAAC,gBAAgB;AACjB,uBAAe;AAAA,MACnB;AACA;AAAA,EACR;AACA,MAAI,WAAW,UAAU,CAAC;AAC1B,MAAI,UAAU,eAAe,CAAC;AAC9B,YAAU;AAEV,MAAI,cAAc,aAAa,EAAE,UAAU,MAAM,SAAS;AACtD,gBAAa,KAAK,MAAM;AACxB,eAAY,KAAK,MAAM;AAAA,EAC3B;AAGA,MAAI,cAAc,aACX,YACC,aACI,cAAc,aACX,MAAM,WAAW,IAAK;AACjC,UAAM,yBAAyB;AAC/B,UAAM,gBAAgB;AACtB,UAAM,eAAe;AAAA,EACzB;AAEA,MAAI,WAAW,GAAG;AACd,cAAU,KAAK;AAAA,EACnB;AAEA,MAAI,UAAU,GAAG;AACb,gBAAY,KAAK;AAAA,EACrB;AAEA,MAAI,cAAc,WAAW;AACzB,gBAAY,KAAK;AAAA,EACrB;AACA;AACJ;AACA,SAAS,YAAY,OAAO;AAExB,YAAU;AACV,cAAY;AAEZ,MAAI,CAAC,UAAU,GAAG;AACd,QAAI,MAAM,SAAS,eAAe,MAAM,WAAW,KAAK,MAAM,WAAW,GAAG;AACxE;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,YAAY;AAEZ,gBAAY;AAEZ;AAAA,EACJ;AAEA,QAAM,SAAS,YAAY,KAAK;AAGhC,QAAM,QAAQ,OAAO,iBAAiB,MAAM;AAC5C,YAAW,MAAM,iBAAiB,mBAAmB,EAAE,KAAK,MAAM,WAC1D,MAAM,UAAU,WAAW,MAAM,WAAW,IAAI,OAAO,eACpD,MAAM,UAAU,WAAW,MAAM,UAAU,IAAI,OAAO;AACrE;AACA,SAAS,UAAU,OAAO;AAEtB,YAAU;AACV,aAAW;AACX,cAAY;AACZ,aAAW;AACf;AACA,IAAM,gBAAgB,OAAO,OAAO;AAAA,EAChC,aAAa;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AAAA,EACb,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,YAAY;AAChB,CAAC;AACD,SAAS,UAAU,MAAM;AACrB,MAAI,MAAM;AACN,QAAI,CAAC,YAAY;AACb,sBAAgB,SAAS,KAAK,MAAM;AAAA,IACxC;AACA,aAAS,KAAK,MAAM,SAAS,cAAc,IAAI;AAAA,EACnD,WACS,CAAC,QAAQ,YAAY;AAC1B,aAAS,KAAK,MAAM,SAAS;AAAA,EACjC;AACA,eAAa,QAAQ;AACzB;AACA,SAAS,YAAY,OAAO;AACxB,MAAI,aAAa,YAAY;AAEzB,eAAW;AACX,WAAO,kBAAkB,UAAU;AAAA,EACvC,WACS,SAAS;AAEd,eAAW;AACX,WAAO,YAAY;AAAA,EACvB;AACA,MAAI,YAAY,UAAU;AAGtB,cAAU,YAAY;AACtB;AAAA,EACJ;AACA,MAAI,CAAC,aAAa,CAAC,UAAU,GAAG;AAC5B,QAAI,YAAY;AACZ,gBAAU;AAAA,IACd;AACA;AAAA,EACJ;AACA,QAAM,qBAAqB,QAAQ,2BAA2B,KAAK;AACnE,QAAM,oBAAoB,QAAQ,0BAA0B,KAAK;AAEjE,QAAM,cAAc,QAAQ,mBAAmB,KAAK;AACpD,QAAM,cAAe,OAAO,aAAa,MAAM,UAAW;AAC1D,QAAM,aAAa,MAAM,UAAU;AACnC,QAAM,YAAY,MAAM,UAAU;AAClC,QAAM,eAAgB,OAAO,cAAc,MAAM,UAAW;AAE5D,QAAM,cAAe,OAAO,aAAa,MAAM,UAAY,oBAAoB;AAC/E,QAAM,aAAa,MAAM,UAAW,oBAAoB;AACxD,QAAM,YAAY,MAAM,UAAW,qBAAqB;AACxD,QAAM,eAAgB,OAAO,cAAc,MAAM,UAAY,qBAAqB;AAClF,MAAI,CAAC,cAAc,CAAC,aAAa,CAAC,gBAAgB,CAAC,aAAa;AAE5D,cAAU;AAAA,EACd,WAES,eAAe;AACpB,cAAU,WAAW;AAAA,WAChB,cAAc;AACnB,cAAU,WAAW;AAAA,WAChB,cAAc;AACnB,cAAU,WAAW;AAAA,WAChB,aAAa;AAClB,cAAU,WAAW;AAAA,WAEhB;AACL,cAAU,UAAU;AAAA,WACf;AACL,cAAU,UAAU;AAAA,WACf;AACL,cAAU,UAAU;AAAA,WACf;AACL,cAAU,UAAU;AAAA;AAGpB,cAAU;AAClB;;;AC7NA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,IAAMC,QAAO,iBAAiB,YAAY,WAAW;AACrD,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,aAAa;AAIZ,SAAS,OAAO;AACnB,SAAOA,MAAK,UAAU;AAC1B;AAIO,SAAS,OAAO;AACnB,SAAOA,MAAK,UAAU;AAC1B;AAIO,SAAS,OAAO;AACnB,SAAOA,MAAK,UAAU;AAC1B;;;AC/BA;AAAA;AAAA;AAAA;AAUA,IAAMC,QAAO,iBAAiB,YAAY,OAAO;AACjD,IAAM,iBAAiB;AAMhB,SAAS,QAAQ,KAAK;AACzB,SAAOA,MAAK,gBAAgB,EAAE,KAAK,IAAI,SAAS,EAAE,CAAC;AACvD;;;ACnBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACsBA,IAAI,UAAU,SAAS,UAAU;AACjC,IAAI,eAAe,OAAO,YAAY,YAAY,YAAY,QAAQ,QAAQ;AAC9E,IAAI;AACJ,IAAI;AACJ,IAAI,OAAO,iBAAiB,cAAc,OAAO,OAAO,mBAAmB,YAAY;AACnF,MAAI;AACA,mBAAe,OAAO,eAAe,CAAC,GAAG,UAAU;AAAA,MAC/C,KAAK,WAAY;AACb,cAAM;AAAA,MACV;AAAA,IACJ,CAAC;AACD,uBAAmB,CAAC;AAEpB,iBAAa,WAAY;AAAE,YAAM;AAAA,IAAI,GAAG,MAAM,YAAY;AAAA,EAC9D,SACO,GAAG;AACN,QAAI,MAAM,kBAAkB;AACxB,qBAAe;AAAA,IACnB;AAAA,EACJ;AACJ,OACK;AACD,iBAAe;AACnB;AACA,IAAI,mBAAmB;AACvB,IAAI,eAAe,SAAS,mBAAmB,OAAO;AAClD,MAAI;AACA,QAAI,QAAQ,QAAQ,KAAK,KAAK;AAC9B,WAAO,iBAAiB,KAAK,KAAK;AAAA,EACtC,SACO,GAAG;AACN,WAAO;AAAA,EACX;AACJ;AACA,IAAI,oBAAoB,SAAS,iBAAiB,OAAO;AACrD,MAAI;AACA,QAAI,aAAa,KAAK,GAAG;AACrB,aAAO;AAAA,IACX;AACA,YAAQ,KAAK,KAAK;AAClB,WAAO;AAAA,EACX,SACO,GAAG;AACN,WAAO;AAAA,EACX;AACJ;AACA,IAAI,QAAQ,OAAO,UAAU;AAC7B,IAAI,cAAc;AAClB,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,IAAI,iBAAiB,OAAO,WAAW,cAAc,CAAC,CAAC,OAAO;AAC9D,IAAI,SAAS,EAAE,KAAK,CAAC,CAAC;AACtB,IAAI,QAAQ,SAAS,mBAAmB;AAAE,SAAO;AAAO;AACxD,IAAI,OAAO,aAAa,UAAU;AAE1B,QAAM,SAAS;AACnB,MAAI,MAAM,KAAK,GAAG,MAAM,MAAM,KAAK,SAAS,GAAG,GAAG;AAC9C,YAAQ,SAASC,kBAAiB,OAAO;AAGrC,WAAK,UAAU,CAAC,WAAW,OAAO,UAAU,eAAe,OAAO,UAAU,WAAW;AACnF,YAAI;AACA,cAAI,MAAM,MAAM,KAAK,KAAK;AAC1B,kBAAQ,QAAQ,YACT,QAAQ,aACR,QAAQ,aACR,QAAQ,gBACV,MAAM,EAAE,KAAK;AAAA,QACtB,SACO,GAAG;AAAA,QAAO;AAAA,MACrB;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAnBQ;AAoBR,SAAS,mBAAmB,OAAO;AAC/B,MAAI,MAAM,KAAK,GAAG;AACd,WAAO;AAAA,EACX;AACA,MAAI,CAAC,OAAO;AACR,WAAO;AAAA,EACX;AACA,MAAI,OAAO,UAAU,cAAc,OAAO,UAAU,UAAU;AAC1D,WAAO;AAAA,EACX;AACA,MAAI;AACA,iBAAa,OAAO,MAAM,YAAY;AAAA,EAC1C,SACO,GAAG;AACN,QAAI,MAAM,kBAAkB;AACxB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO,CAAC,aAAa,KAAK,KAAK,kBAAkB,KAAK;AAC1D;AACA,SAAS,qBAAqB,OAAO;AACjC,MAAI,MAAM,KAAK,GAAG;AACd,WAAO;AAAA,EACX;AACA,MAAI,CAAC,OAAO;AACR,WAAO;AAAA,EACX;AACA,MAAI,OAAO,UAAU,cAAc,OAAO,UAAU,UAAU;AAC1D,WAAO;AAAA,EACX;AACA,MAAI,gBAAgB;AAChB,WAAO,kBAAkB,KAAK;AAAA,EAClC;AACA,MAAI,aAAa,KAAK,GAAG;AACrB,WAAO;AAAA,EACX;AACA,MAAI,WAAW,MAAM,KAAK,KAAK;AAC/B,MAAI,aAAa,WAAW,aAAa,YAAY,CAAE,iBAAkB,KAAK,QAAQ,GAAG;AACrF,WAAO;AAAA,EACX;AACA,SAAO,kBAAkB,KAAK;AAClC;AAEA,IAAO,mBAAQ,eAAe,qBAAqB;;;ACtInD,IAAI;AASG,IAAM,cAAN,cAA0B,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnC,YAAY,SAAS,SAAS;AAC1B,UAAM,SAAS,OAAO;AACtB,SAAK,OAAO;AAAA,EAChB;AACJ;AAaO,IAAM,0BAAN,cAAsC,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ/C,YAAY,SAAS,QAAQ,MAAM;AAC/B,WAAO,SAAS,QAAQ,SAAS,SAAS,OAAO,+CAA+C,cAAc,aAAa,MAAM,GAAG,EAAE,OAAO,OAAO,CAAC;AACrJ,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EAChB;AACJ;AAEA,IAAM,aAAa,OAAO,SAAS;AACnC,IAAM,gBAAgB,OAAO,YAAY;AACzC,IAAM,WAAW,KAAK,OAAO,aAAa,QAAQ,OAAO,SAAS,KAAK,OAAO,iBAAiB;AAmDxF,IAAM,qBAAN,MAAM,4BAA2B,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiC5C,YAAY,UAAU,aAAa;AAC/B,QAAI;AACJ,QAAI;AACJ,UAAM,CAAC,KAAK,QAAQ;AAAE,gBAAU;AAAK,eAAS;AAAA,IAAK,CAAC;AACpD,QAAI,KAAK,YAAY,OAAO,MAAM,SAAS;AACvC,YAAM,IAAI,UAAU,mIAAmI;AAAA,IAC3J;AACA,QAAI,UAAU;AAAA,MACV,SAAS;AAAA,MACT;AAAA,MACA;AAAA,MACA,IAAI,cAAc;AAAE,eAAO,gBAAgB,QAAQ,gBAAgB,SAAS,cAAc;AAAA,MAAM;AAAA,MAChG,IAAI,YAAY,IAAI;AAAE,sBAAc,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,MAAW;AAAA,IACvF;AACA,UAAM,QAAQ;AAAA,MACV,IAAI,OAAO;AAAE,eAAO;AAAA,MAAO;AAAA,MAC3B,WAAW;AAAA,MACX,SAAS;AAAA,IACb;AAEA,SAAK,OAAO,iBAAiB,MAAM;AAAA,MAC/B,CAAC,UAAU,GAAG;AAAA,QACV,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO;AAAA,MACX;AAAA,MACA,CAAC,aAAa,GAAG;AAAA,QACb,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO,aAAa,SAAS,KAAK;AAAA,MACtC;AAAA,IACJ,CAAC;AAED,UAAM,WAAW,YAAY,SAAS,KAAK;AAC3C,QAAI;AACA,eAAS,YAAY,SAAS,KAAK,GAAG,QAAQ;AAAA,IAClD,SACO,KAAK;AACR,UAAI,MAAM,WAAW;AACjB,gBAAQ,IAAI,uDAAuD,GAAG;AAAA,MAC1E,OACK;AACD,iBAAS,GAAG;AAAA,MAChB;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwDA,OAAO,OAAO;AACV,WAAO,IAAI,oBAAmB,CAAC,YAAY;AAGvC,cAAQ,IAAI;AAAA,QACR,KAAK,aAAa,EAAE,IAAI,YAAY,sBAAsB,EAAE,MAAM,CAAC,CAAC;AAAA,QACpE,eAAe,IAAI;AAAA,MACvB,CAAC,EAAE,KAAK,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC;AAAA,IAC5C,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BA,SAAS,QAAQ;AACb,QAAI,OAAO,SAAS;AAChB,WAAK,KAAK,OAAO,OAAO,MAAM;AAAA,IAClC,OACK;AACD,aAAO,iBAAiB,SAAS,MAAM,KAAK,KAAK,OAAO,OAAO,MAAM,GAAG,EAAE,SAAS,KAAK,CAAC;AAAA,IAC7F;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8BA,KAAK,aAAa,YAAY,aAAa;AACvC,QAAI,EAAE,gBAAgB,sBAAqB;AACvC,YAAM,IAAI,UAAU,gEAAgE;AAAA,IACxF;AAIA,QAAI,CAAC,iBAAW,WAAW,GAAG;AAC1B,oBAAc;AAAA,IAClB;AACA,QAAI,CAAC,iBAAW,UAAU,GAAG;AACzB,mBAAa;AAAA,IACjB;AACA,QAAI,gBAAgB,YAAY,cAAc,SAAS;AAEnD,aAAO,IAAI,oBAAmB,CAAC,YAAY,QAAQ,IAAI,CAAC;AAAA,IAC5D;AACA,UAAM,UAAU,CAAC;AACjB,SAAK,UAAU,IAAI;AACnB,WAAO,IAAI,oBAAmB,CAAC,SAAS,WAAW;AAC/C,WAAK,MAAM,KAAK,CAAC,UAAU;AACvB,YAAIC;AACJ,YAAI,KAAK,UAAU,MAAM,SAAS;AAC9B,eAAK,UAAU,IAAI;AAAA,QACvB;AACA,SAACA,MAAK,QAAQ,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,OAAO;AAC3E,YAAI;AACA,kBAAQ,YAAY,KAAK,CAAC;AAAA,QAC9B,SACO,KAAK;AACR,iBAAO,GAAG;AAAA,QACd;AAAA,MACJ,GAAG,CAAC,WAAW;AACX,YAAIA;AACJ,YAAI,KAAK,UAAU,MAAM,SAAS;AAC9B,eAAK,UAAU,IAAI;AAAA,QACvB;AACA,SAACA,MAAK,QAAQ,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,OAAO;AAC3E,YAAI;AACA,kBAAQ,WAAW,MAAM,CAAC;AAAA,QAC9B,SACO,KAAK;AACR,iBAAO,GAAG;AAAA,QACd;AAAA,MACJ,CAAC;AAAA,IACL,GAAG,CAAO,UAAU;AAEhB,UAAI;AACA,eAAO,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,KAAK;AAAA,MACtF,UACA;AACI,cAAM,KAAK,OAAO,KAAK;AAAA,MAC3B;AAAA,IACJ,EAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8BA,MAAM,YAAY,aAAa;AAC3B,WAAO,KAAK,KAAK,QAAW,YAAY,WAAW;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgCA,QAAQ,WAAW,aAAa;AAC5B,QAAI,EAAE,gBAAgB,sBAAqB;AACvC,YAAM,IAAI,UAAU,mEAAmE;AAAA,IAC3F;AACA,QAAI,CAAC,iBAAW,SAAS,GAAG;AACxB,aAAO,KAAK,KAAK,WAAW,WAAW,WAAW;AAAA,IACtD;AACA,WAAO,KAAK,KAAK,CAAC,UAAU,oBAAmB,QAAQ,UAAU,CAAC,EAAE,KAAK,MAAM,KAAK,GAAG,CAAC,WAAW,oBAAmB,QAAQ,UAAU,CAAC,EAAE,KAAK,MAAM;AAAE,YAAM;AAAA,IAAQ,CAAC,GAAG,WAAW;AAAA,EACzL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,YAAY,OAAO,IAAI;AACnB,WAAO;AAAA,EACX;AAAA,EACA,OAAO,IAAI,QAAQ;AACf,QAAI,YAAY,MAAM,KAAK,MAAM;AACjC,UAAM,UAAU,UAAU,WAAW,IAC/B,oBAAmB,QAAQ,SAAS,IACpC,IAAI,oBAAmB,CAAC,SAAS,WAAW;AAC1C,WAAK,QAAQ,IAAI,SAAS,EAAE,KAAK,SAAS,MAAM;AAAA,IACpD,GAAG,CAAC,UAAU,UAAU,SAAS,WAAW,KAAK,CAAC;AACtD,WAAO;AAAA,EACX;AAAA,EACA,OAAO,WAAW,QAAQ;AACtB,QAAI,YAAY,MAAM,KAAK,MAAM;AACjC,UAAM,UAAU,UAAU,WAAW,IAC/B,oBAAmB,QAAQ,SAAS,IACpC,IAAI,oBAAmB,CAAC,SAAS,WAAW;AAC1C,WAAK,QAAQ,WAAW,SAAS,EAAE,KAAK,SAAS,MAAM;AAAA,IAC3D,GAAG,CAAC,UAAU,UAAU,SAAS,WAAW,KAAK,CAAC;AACtD,WAAO;AAAA,EACX;AAAA,EACA,OAAO,IAAI,QAAQ;AACf,QAAI,YAAY,MAAM,KAAK,MAAM;AACjC,UAAM,UAAU,UAAU,WAAW,IAC/B,oBAAmB,QAAQ,SAAS,IACpC,IAAI,oBAAmB,CAAC,SAAS,WAAW;AAC1C,WAAK,QAAQ,IAAI,SAAS,EAAE,KAAK,SAAS,MAAM;AAAA,IACpD,GAAG,CAAC,UAAU,UAAU,SAAS,WAAW,KAAK,CAAC;AACtD,WAAO;AAAA,EACX;AAAA,EACA,OAAO,KAAK,QAAQ;AAChB,QAAI,YAAY,MAAM,KAAK,MAAM;AACjC,UAAM,UAAU,IAAI,oBAAmB,CAAC,SAAS,WAAW;AACxD,WAAK,QAAQ,KAAK,SAAS,EAAE,KAAK,SAAS,MAAM;AAAA,IACrD,GAAG,CAAC,UAAU,UAAU,SAAS,WAAW,KAAK,CAAC;AAClD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO,OAAO;AACjB,UAAM,IAAI,IAAI,oBAAmB,MAAM;AAAA,IAAE,CAAC;AAC1C,MAAE,OAAO,KAAK;AACd,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,QAAQ,cAAc,OAAO;AAChC,UAAM,UAAU,IAAI,oBAAmB,MAAM;AAAA,IAAE,CAAC;AAChD,QAAI,eAAe,OAAO,gBAAgB,cAAc,YAAY,WAAW,OAAO,YAAY,YAAY,YAAY;AACtH,kBAAY,QAAQ,YAAY,EAAE,iBAAiB,SAAS,MAAM,KAAK,QAAQ,OAAO,KAAK,CAAC;AAAA,IAChG,OACK;AACD,iBAAW,MAAM,KAAK,QAAQ,OAAO,KAAK,GAAG,YAAY;AAAA,IAC7D;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,MAAM,cAAc,OAAO;AAC9B,WAAO,IAAI,oBAAmB,CAAC,YAAY;AACvC,iBAAW,MAAM,QAAQ,KAAK,GAAG,YAAY;AAAA,IACjD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO,QAAQ;AAClB,WAAO,IAAI,oBAAmB,CAAC,GAAG,WAAW,OAAO,MAAM,CAAC;AAAA,EAC/D;AAAA,EACA,OAAO,QAAQ,OAAO;AAClB,QAAI,iBAAiB,qBAAoB;AAErC,aAAO;AAAA,IACX;AACA,WAAO,IAAI,oBAAmB,CAAC,YAAY,QAAQ,KAAK,CAAC;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,gBAAgB;AACnB,QAAI,SAAS,EAAE,aAAa,KAAK;AACjC,WAAO,UAAU,IAAI,oBAAmB,CAAC,SAAS,WAAW;AACzD,aAAO,UAAU;AACjB,aAAO,SAAS;AAAA,IACpB,GAAG,CAAC,UAAU;AAAE,UAAIA;AAAI,OAACA,MAAK,OAAO,iBAAiB,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,QAAQ,KAAK;AAAA,IAAG,CAAC;AAChH,WAAO;AAAA,EACX;AACJ;AAKA,SAAS,aAAa,SAAS,OAAO;AAClC,MAAI,sBAAsB;AAC1B,SAAO,CAAC,WAAW;AACf,QAAI,CAAC,MAAM,SAAS;AAChB,YAAM,UAAU;AAChB,YAAM,SAAS;AACf,cAAQ,OAAO,MAAM;AAKrB,WAAK,QAAQ,UAAU,KAAK,KAAK,QAAQ,SAAS,QAAW,CAAC,QAAQ;AAClE,YAAI,QAAQ,QAAQ;AAChB,gBAAM;AAAA,QACV;AAAA,MACJ,CAAC;AAAA,IACL;AAGA,QAAI,CAAC,MAAM,UAAU,CAAC,QAAQ,aAAa;AACvC;AAAA,IACJ;AACA,0BAAsB,IAAI,QAAQ,CAAC,YAAY;AAC3C,UAAI;AACA,gBAAQ,QAAQ,YAAY,MAAM,OAAO,KAAK,CAAC;AAAA,MACnD,SACO,KAAK;AACR,gBAAQ,OAAO,IAAI,wBAAwB,QAAQ,SAAS,KAAK,8CAA8C,CAAC;AAAA,MACpH;AAAA,IACJ,CAAC,EAAE,MAAM,CAACC,YAAW;AACjB,cAAQ,OAAO,IAAI,wBAAwB,QAAQ,SAASA,SAAQ,8CAA8C,CAAC;AAAA,IACvH,CAAC;AAED,YAAQ,cAAc;AACtB,WAAO;AAAA,EACX;AACJ;AAIA,SAAS,YAAY,SAAS,OAAO;AACjC,SAAO,CAAC,UAAU;AACd,QAAI,MAAM,WAAW;AACjB;AAAA,IACJ;AACA,UAAM,YAAY;AAClB,QAAI,UAAU,QAAQ,SAAS;AAC3B,UAAI,MAAM,SAAS;AACf;AAAA,MACJ;AACA,YAAM,UAAU;AAChB,cAAQ,OAAO,IAAI,UAAU,2CAA2C,CAAC;AACzE;AAAA,IACJ;AACA,QAAI,SAAS,SAAS,OAAO,UAAU,YAAY,OAAO,UAAU,aAAa;AAC7E,UAAI;AACJ,UAAI;AACA,eAAO,MAAM;AAAA,MACjB,SACO,KAAK;AACR,cAAM,UAAU;AAChB,gBAAQ,OAAO,GAAG;AAClB;AAAA,MACJ;AACA,UAAI,iBAAW,IAAI,GAAG;AAClB,YAAI;AACA,cAAI,SAAS,MAAM;AACnB,cAAI,iBAAW,MAAM,GAAG;AACpB,kBAAM,cAAc,CAAC,UAAU;AAC3B,sBAAQ,MAAM,QAAQ,OAAO,CAAC,KAAK,CAAC;AAAA,YACxC;AACA,gBAAI,MAAM,QAAQ;AAId,mBAAK,aAAa,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,YAAY,CAAC,GAAG,KAAK,EAAE,MAAM,MAAM;AAAA,YACrG,OACK;AACD,sBAAQ,cAAc;AAAA,YAC1B;AAAA,UACJ;AAAA,QACJ,SACOD,KAAI;AAAA,QAAE;AACb,cAAM,WAAW;AAAA,UACb,MAAM,MAAM;AAAA,UACZ,WAAW;AAAA,UACX,IAAI,UAAU;AAAE,mBAAO,KAAK,KAAK;AAAA,UAAS;AAAA,UAC1C,IAAI,QAAQE,QAAO;AAAE,iBAAK,KAAK,UAAUA;AAAA,UAAO;AAAA,UAChD,IAAI,SAAS;AAAE,mBAAO,KAAK,KAAK;AAAA,UAAQ;AAAA,QAC5C;AACA,cAAM,WAAW,YAAY,SAAS,QAAQ;AAC9C,YAAI;AACA,kBAAQ,MAAM,MAAM,OAAO,CAAC,YAAY,SAAS,QAAQ,GAAG,QAAQ,CAAC;AAAA,QACzE,SACO,KAAK;AACR,mBAAS,GAAG;AAAA,QAChB;AACA;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,MAAM,SAAS;AACf;AAAA,IACJ;AACA,UAAM,UAAU;AAChB,YAAQ,QAAQ,KAAK;AAAA,EACzB;AACJ;AAIA,SAAS,YAAY,SAAS,OAAO;AACjC,SAAO,CAAC,WAAW;AACf,QAAI,MAAM,WAAW;AACjB;AAAA,IACJ;AACA,UAAM,YAAY;AAClB,QAAI,MAAM,SAAS;AACf,UAAI;AACA,YAAI,kBAAkB,eAAe,MAAM,kBAAkB,eAAe,OAAO,GAAG,OAAO,OAAO,MAAM,OAAO,KAAK,GAAG;AAErH;AAAA,QACJ;AAAA,MACJ,SACOF,KAAI;AAAA,MAAE;AACb,WAAK,QAAQ,OAAO,IAAI,wBAAwB,QAAQ,SAAS,MAAM,CAAC;AAAA,IAC5E,OACK;AACD,YAAM,UAAU;AAChB,cAAQ,OAAO,MAAM;AAAA,IACzB;AAAA,EACJ;AACJ;AAKA,SAAS,UAAU,QAAQ,QAAQ,OAAO;AACtC,QAAM,UAAU,CAAC;AACjB,aAAW,SAAS,QAAQ;AACxB,QAAI;AACJ,QAAI;AACA,UAAI,CAAC,iBAAW,MAAM,IAAI,GAAG;AACzB;AAAA,MACJ;AACA,eAAS,MAAM;AACf,UAAI,CAAC,iBAAW,MAAM,GAAG;AACrB;AAAA,MACJ;AAAA,IACJ,SACOA,KAAI;AACP;AAAA,IACJ;AACA,QAAI;AACJ,QAAI;AACA,eAAS,QAAQ,MAAM,QAAQ,OAAO,CAAC,KAAK,CAAC;AAAA,IACjD,SACO,KAAK;AACR,cAAQ,OAAO,IAAI,wBAAwB,QAAQ,KAAK,uCAAuC,CAAC;AAChG;AAAA,IACJ;AACA,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,YAAQ,MAAM,kBAAkB,UAAU,SAAS,QAAQ,QAAQ,MAAM,GAAG,MAAM,CAAC,WAAW;AAC1F,cAAQ,OAAO,IAAI,wBAAwB,QAAQ,QAAQ,uCAAuC,CAAC;AAAA,IACvG,CAAC,CAAC;AAAA,EACN;AACA,SAAO,QAAQ,IAAI,OAAO;AAC9B;AAIA,SAAS,SAAS,GAAG;AACjB,SAAO;AACX;AAIA,SAAS,QAAQ,QAAQ;AACrB,QAAM;AACV;AAIA,SAAS,aAAa,KAAK;AACvB,MAAI;AACA,QAAI,eAAe,SAAS,OAAO,QAAQ,YAAY,IAAI,aAAa,OAAO,UAAU,UAAU;AAC/F,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ,SACOA,KAAI;AAAA,EAAE;AACb,MAAI;AACA,WAAO,KAAK,UAAU,GAAG;AAAA,EAC7B,SACO,IAAI;AAAA,EAAE;AACb,MAAI;AACA,WAAO,OAAO,UAAU,SAAS,KAAK,GAAG;AAAA,EAC7C,SACO,IAAI;AAAA,EAAE;AACb,SAAO;AACX;AAIA,SAAS,eAAe,SAAS;AAC7B,MAAIA;AACJ,MAAI,OAAOA,MAAK,QAAQ,UAAU,OAAO,QAAQA,QAAO,SAASA,MAAK,CAAC;AACvE,MAAI,EAAE,aAAa,MAAM;AACrB,WAAO,OAAO,KAAK,qBAAqB,CAAC;AAAA,EAC7C;AACA,MAAI,QAAQ,UAAU,KAAK,MAAM;AAC7B,QAAI,QAAQ;AACZ,YAAQ,UAAU,IAAI;AAAA,EAC1B;AACA,SAAO,IAAI;AACf;AAEA,IAAI,uBAAuB,QAAQ;AACnC,IAAI,wBAAwB,OAAO,yBAAyB,YAAY;AACpE,yBAAuB,qBAAqB,KAAK,OAAO;AAC5D,OACK;AACD,yBAAuB,WAAY;AAC/B,QAAI;AACJ,QAAI;AACJ,UAAM,UAAU,IAAI,QAAQ,CAAC,KAAK,QAAQ;AAAE,gBAAU;AAAK,eAAS;AAAA,IAAK,CAAC;AAC1E,WAAO,EAAE,SAAS,SAAS,OAAO;AAAA,EACtC;AACJ;;;AF1wBA,OAAO,SAAS,OAAO,UAAU,CAAC;AAClC,OAAO,OAAO,oBAAoB;AAClC,OAAO,OAAO,mBAAmB;AACjC,IAAMG,QAAO,iBAAiB,YAAY,IAAI;AAC9C,IAAM,aAAa,iBAAiB,YAAY,UAAU;AAC1D,IAAM,gBAAgB,oBAAI,IAAI;AAC9B,IAAM,cAAc;AACpB,IAAM,eAAe;AAKd,IAAM,eAAN,cAA2B,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpC,YAAY,SAAS,SAAS;AAC1B,UAAM,SAAS,OAAO;AACtB,SAAK,OAAO;AAAA,EAChB;AACJ;AAQA,SAAS,cAAc,IAAI,MAAM,QAAQ;AACrC,QAAM,YAAY,qBAAqB,EAAE;AACzC,MAAI,CAAC,WAAW;AACZ;AAAA,EACJ;AACA,MAAI,CAAC,MAAM;AACP,cAAU,QAAQ,MAAS;AAAA,EAC/B,WACS,CAAC,QAAQ;AACd,cAAU,QAAQ,IAAI;AAAA,EAC1B,OACK;AACD,QAAI;AACA,gBAAU,QAAQ,KAAK,MAAM,IAAI,CAAC;AAAA,IACtC,SACO,KAAK;AACR,gBAAU,OAAO,IAAI,UAAU,6BAA6B,IAAI,SAAS,EAAE,OAAO,IAAI,CAAC,CAAC;AAAA,IAC5F;AAAA,EACJ;AACJ;AAQA,SAAS,aAAa,IAAI,MAAM,QAAQ;AACpC,QAAM,YAAY,qBAAqB,EAAE;AACzC,MAAI,CAAC,WAAW;AACZ;AAAA,EACJ;AACA,MAAI,CAAC,QAAQ;AACT,cAAU,OAAO,IAAI,MAAM,IAAI,CAAC;AAAA,EACpC,OACK;AACD,QAAI;AACJ,QAAI;AACA,cAAQ,KAAK,MAAM,IAAI;AAAA,IAC3B,SACO,KAAK;AACR,gBAAU,OAAO,IAAI,UAAU,4BAA4B,IAAI,SAAS,EAAE,OAAO,IAAI,CAAC,CAAC;AACvF;AAAA,IACJ;AACA,QAAI,UAAU,CAAC;AACf,QAAI,MAAM,OAAO;AACb,cAAQ,QAAQ,MAAM;AAAA,IAC1B;AACA,QAAI;AACJ,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AACD,oBAAY,IAAI,eAAe,MAAM,SAAS,OAAO;AACrD;AAAA,MACJ,KAAK;AACD,oBAAY,IAAI,UAAU,MAAM,SAAS,OAAO;AAChD;AAAA,MACJ,KAAK;AACD,oBAAY,IAAI,aAAa,MAAM,SAAS,OAAO;AACnD;AAAA,MACJ;AACI,oBAAY,IAAI,MAAM,MAAM,SAAS,OAAO;AAC5C;AAAA,IACR;AACA,cAAU,OAAO,SAAS;AAAA,EAC9B;AACJ;AAOA,SAAS,qBAAqB,IAAI;AAC9B,QAAM,WAAW,cAAc,IAAI,EAAE;AACrC,gBAAc,OAAO,EAAE;AACvB,SAAO;AACX;AAMA,SAAS,aAAa;AAClB,MAAI;AACJ,KAAG;AACC,aAAS,OAAO;AAAA,EACpB,SAAS,cAAc,IAAI,MAAM;AACjC,SAAO;AACX;AAaO,SAAS,KAAK,SAAS;AAC1B,QAAM,KAAK,WAAW;AACtB,QAAM,SAAS,mBAAmB,cAAc;AAChD,gBAAc,IAAI,IAAI,EAAE,SAAS,OAAO,SAAS,QAAQ,OAAO,OAAO,CAAC;AACxE,QAAM,UAAUA,MAAK,aAAa,OAAO,OAAO,EAAE,WAAW,GAAG,GAAG,OAAO,CAAC;AAC3E,MAAI,UAAU;AACd,UAAQ,KAAK,MAAM;AACf,cAAU;AAAA,EACd,GAAG,CAAC,QAAQ;AACR,kBAAc,OAAO,EAAE;AACvB,WAAO,OAAO,GAAG;AAAA,EACrB,CAAC;AACD,QAAM,SAAS,MAAM;AACjB,kBAAc,OAAO,EAAE;AACvB,WAAO,WAAW,cAAc,EAAE,WAAW,GAAG,CAAC,EAAE,MAAM,CAAC,QAAQ;AAC9D,cAAQ,MAAM,qDAAqD,GAAG;AAAA,IAC1E,CAAC;AAAA,EACL;AACA,SAAO,cAAc,MAAM;AACvB,QAAI,SAAS;AACT,aAAO,OAAO;AAAA,IAClB,OACK;AACD,aAAO,QAAQ,KAAK,MAAM;AAAA,IAC9B;AAAA,EACJ;AACA,SAAO,OAAO;AAClB;AASO,SAAS,OAAO,eAAe,MAAM;AACxC,SAAO,KAAK,EAAE,YAAY,KAAK,CAAC;AACpC;AASO,SAAS,KAAK,aAAa,MAAM;AACpC,SAAO,KAAK,EAAE,UAAU,KAAK,CAAC;AAClC;;;AGjMA;AAAA;AAAA;AAAA;AAAA;AAUA,IAAMC,QAAO,iBAAiB,YAAY,SAAS;AACnD,IAAM,mBAAmB;AACzB,IAAM,gBAAgB;AAOf,SAAS,QAAQ,MAAM;AAC1B,SAAOA,MAAK,kBAAkB,EAAE,KAAK,CAAC;AAC1C;AAMO,SAAS,OAAO;AACnB,SAAOA,MAAK,aAAa;AAC7B;;;AC7BA;AAAA;AAAA;AAAA,eAAAC;AAAA,EAAA;AAAA,aAAAC;AAAA,EAAA;AAAA;AAAA;AAYO,SAAS,IAAI,QAAQ;AACxB,SAAO;AACX;AAKO,SAAS,UAAU,QAAQ;AAC9B,SAAS,UAAU,OAAQ,KAAK;AACpC;AAMO,SAASD,OAAM,SAAS;AAC3B,MAAI,YAAY,KAAK;AACjB,WAAO,CAAC,WAAY,WAAW,OAAO,CAAC,IAAI;AAAA,EAC/C;AACA,SAAO,CAAC,WAAW;AACf,QAAI,WAAW,MAAM;AACjB,aAAO,CAAC;AAAA,IACZ;AACA,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,aAAO,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC;AAAA,IACjC;AACA,WAAO;AAAA,EACX;AACJ;AAMO,SAASC,KAAI,KAAK,OAAO;AAC5B,MAAI,UAAU,KAAK;AACf,WAAO,CAAC,WAAY,WAAW,OAAO,CAAC,IAAI;AAAA,EAC/C;AACA,SAAO,CAAC,WAAW;AACf,QAAI,WAAW,MAAM;AACjB,aAAO,CAAC;AAAA,IACZ;AACA,eAAWC,QAAO,QAAQ;AACtB,aAAOA,IAAG,IAAI,MAAM,OAAOA,IAAG,CAAC;AAAA,IACnC;AACA,WAAO;AAAA,EACX;AACJ;AAKO,SAAS,SAAS,SAAS;AAC9B,MAAI,YAAY,KAAK;AACjB,WAAO;AAAA,EACX;AACA,SAAO,CAAC,WAAY,WAAW,OAAO,OAAO,QAAQ,MAAM;AAC/D;AAKO,SAAS,OAAO,aAAa;AAChC,MAAI,SAAS;AACb,aAAW,QAAQ,aAAa;AAC5B,QAAI,YAAY,IAAI,MAAM,KAAK;AAC3B,eAAS;AACT;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,QAAQ;AACR,WAAO;AAAA,EACX;AACA,SAAO,CAAC,WAAW;AACf,eAAW,QAAQ,aAAa;AAC5B,UAAI,QAAQ,QAAQ;AAChB,eAAO,IAAI,IAAI,YAAY,IAAI,EAAE,OAAO,IAAI,CAAC;AAAA,MACjD;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;;;AC7FA;AAAA;AAAA,eAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,OAAO,SAAS,OAAO,UAAU,CAAC;AAClC,OAAO,OAAO,sBAAsB;AACpC,OAAO,OAAO,uBAAuB;AACrC,IAAMC,QAAO,iBAAiB,YAAY,MAAM;AAChD,IAAM,kBAAkB,oBAAI,IAAI;AAEhC,IAAM,aAAa;AACnB,IAAM,gBAAgB;AACtB,IAAM,cAAc;AACpB,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;AAQvB,SAAS,qBAAqB,IAAI,MAAM,QAAQ;AAC5C,MAAI,YAAYC,sBAAqB,EAAE;AACvC,MAAI,CAAC,WAAW;AACZ;AAAA,EACJ;AACA,MAAI,QAAQ;AACR,QAAI;AACA,gBAAU,QAAQ,KAAK,MAAM,IAAI,CAAC;AAAA,IACtC,SACO,KAAK;AACR,gBAAU,OAAO,IAAI,UAAU,6BAA6B,IAAI,SAAS,EAAE,OAAO,IAAI,CAAC,CAAC;AAAA,IAC5F;AAAA,EACJ,OACK;AACD,cAAU,QAAQ,IAAI;AAAA,EAC1B;AACJ;AAOA,SAAS,oBAAoB,IAAI,SAAS;AACtC,MAAIC;AACJ,GAACA,MAAKD,sBAAqB,EAAE,OAAO,QAAQC,QAAO,SAAS,SAASA,IAAG,OAAO,IAAI,OAAO,MAAM,OAAO,CAAC;AAC5G;AAOA,SAASD,sBAAqB,IAAI;AAC9B,QAAM,WAAW,gBAAgB,IAAI,EAAE;AACvC,kBAAgB,OAAO,EAAE;AACzB,SAAO;AACX;AAMA,SAASE,cAAa;AAClB,MAAI;AACJ,KAAG;AACC,aAAS,OAAO;AAAA,EACpB,SAAS,gBAAgB,IAAI,MAAM;AACnC,SAAO;AACX;AAQA,SAAS,OAAO,MAAM,UAAU,CAAC,GAAG;AAChC,QAAM,KAAKA,YAAW;AACtB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,oBAAgB,IAAI,IAAI,EAAE,SAAS,OAAO,CAAC;AAC3C,IAAAH,MAAK,MAAM,OAAO,OAAO,EAAE,aAAa,GAAG,GAAG,OAAO,CAAC,EAAE,MAAM,CAAC,QAAQ;AACnE,sBAAgB,OAAO,EAAE;AACzB,aAAO,GAAG;AAAA,IACd,CAAC;AAAA,EACL,CAAC;AACL;AAOO,SAAS,KAAK,SAAS;AAAE,SAAO,OAAO,YAAY,OAAO;AAAG;AAO7D,SAAS,QAAQ,SAAS;AAAE,SAAO,OAAO,eAAe,OAAO;AAAG;AAOnE,SAASI,OAAM,SAAS;AAAE,SAAO,OAAO,aAAa,OAAO;AAAG;AAO/D,SAAS,SAAS,SAAS;AAAE,SAAO,OAAO,gBAAgB,OAAO;AAAG;AACrE,SAAS,SAAS,SAAS;AAAE,MAAIF;AAAI,UAAQA,MAAK,OAAO,gBAAgB,OAAO,OAAO,QAAQA,QAAO,SAASA,MAAK,CAAC;AAAG;AAOxH,SAAS,SAAS,SAAS;AAAE,SAAO,OAAO,gBAAgB,OAAO;AAAG;;;ACrI5E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACWO,IAAM,iBAAiB,oBAAI,IAAI;AAC/B,IAAM,WAAN,MAAe;AAAA,EAClB,YAAY,WAAW,UAAU,cAAc;AAC3C,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,eAAe,gBAAgB;AAAA,EACxC;AAAA,EACA,SAAS,MAAM;AACX,QAAI;AACA,WAAK,SAAS,IAAI;AAAA,IACtB,SACO,KAAK;AACR,cAAQ,MAAM,GAAG;AAAA,IACrB;AACA,QAAI,KAAK,iBAAiB;AACtB,aAAO;AACX,SAAK,gBAAgB;AACrB,WAAO,KAAK,iBAAiB;AAAA,EACjC;AACJ;AACO,SAAS,YAAY,UAAU;AAClC,MAAI,YAAY,eAAe,IAAI,SAAS,SAAS;AACrD,MAAI,CAAC,WAAW;AACZ;AAAA,EACJ;AACA,cAAY,UAAU,OAAO,OAAK,MAAM,QAAQ;AAChD,MAAI,UAAU,WAAW,GAAG;AACxB,mBAAe,OAAO,SAAS,SAAS;AAAA,EAC5C,OACK;AACD,mBAAe,IAAI,SAAS,WAAW,SAAS;AAAA,EACpD;AACJ;;;AChCO,IAAM,QAAQ,OAAO,OAAO;AAAA,EAC/B,SAAS,OAAO,OAAO;AAAA,IACnB,uBAAuB;AAAA,IACvB,sBAAsB;AAAA,IACtB,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,cAAc;AAAA,IACd,uBAAuB;AAAA,IACvB,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,0BAA0B;AAAA,IAC1B,wBAAwB;AAAA,IACxB,aAAa;AAAA,IACb,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,EACtB,CAAC;AAAA,EACD,KAAK,OAAO,OAAO;AAAA,IACf,4BAA4B;AAAA,IAC5B,uCAAuC;AAAA,IACvC,yCAAyC;AAAA,IACzC,0BAA0B;AAAA,IAC1B,oCAAoC;AAAA,IACpC,sCAAsC;AAAA,IACtC,oCAAoC;AAAA,IACpC,0CAA0C;AAAA,IAC1C,2BAA2B;AAAA,IAC3B,+BAA+B;AAAA,IAC/B,oBAAoB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,sBAAsB;AAAA,IACtB,sBAAsB;AAAA,IACtB,+BAA+B;AAAA,IAC/B,6BAA6B;AAAA,IAC7B,gCAAgC;AAAA,IAChC,qBAAqB;AAAA,IACrB,6BAA6B;AAAA,IAC7B,0BAA0B;AAAA,IAC1B,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,aAAa;AAAA,IACb,cAAc;AAAA,IACd,mBAAmB;AAAA,IACnB,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,uBAAuB;AAAA,IACvB,cAAc;AAAA,IACd,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,0BAA0B;AAAA,IAC1B,gBAAgB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yDAAyD;AAAA,IACzD,sCAAsC;AAAA,IACtC,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,gCAAgC;AAAA,IAChC,kCAAkC;AAAA,IAClC,mCAAmC;AAAA,IACnC,oCAAoC;AAAA,IACpC,+BAA+B;AAAA,IAC/B,6BAA6B;AAAA,IAC7B,uBAAuB;AAAA,IACvB,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,4BAA4B;AAAA,IAC5B,sCAAsC;AAAA,IACtC,4BAA4B;AAAA,IAC5B,sBAAsB;AAAA,IACtB,kCAAkC;AAAA,IAClC,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,IACxB,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,IAC1B,8BAA8B;AAAA,IAC9B,yBAAyB;AAAA,IACzB,6BAA6B;AAAA,IAC7B,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,sBAAsB;AAAA,IACtB,eAAe;AAAA,IACf,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,sBAAsB;AAAA,IACtB,mCAAmC;AAAA,IACnC,qCAAqC;AAAA,IACrC,uBAAuB;AAAA,IACvB,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IACxB,eAAe;AAAA,IACf,2BAA2B;AAAA,IAC3B,0BAA0B;AAAA,IAC1B,6BAA6B;AAAA,IAC7B,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,qBAAqB;AAAA,IACrB,sBAAsB;AAAA,IACtB,sBAAsB;AAAA,IACtB,8BAA8B;AAAA,IAC9B,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,IACzB,2BAA2B;AAAA,IAC3B,+BAA+B;AAAA,IAC/B,0BAA0B;AAAA,IAC1B,8BAA8B;AAAA,IAC9B,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,sBAAsB;AAAA,IACtB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,uBAAuB;AAAA,IACvB,oCAAoC;AAAA,IACpC,sCAAsC;AAAA,IACtC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,yBAAyB;AAAA,IACzB,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,cAAc;AAAA,IACd,eAAe;AAAA,IACf,iBAAiB;AAAA,EACrB,CAAC;AAAA,EACD,OAAO,OAAO,OAAO;AAAA,IACjB,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,EACvB,CAAC;AAAA,EACD,QAAQ,OAAO,OAAO;AAAA,IAClB,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,cAAc;AAAA,IACd,eAAe;AAAA,IACf,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,YAAY;AAAA,IACZ,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,eAAe;AAAA,IACf,iBAAiB;AAAA,EACrB,CAAC;AACL,CAAC;;;AFxND,OAAO,SAAS,OAAO,UAAU,CAAC;AAClC,OAAO,OAAO,qBAAqB;AACnC,IAAMG,QAAO,iBAAiB,YAAY,MAAM;AAChD,IAAM,aAAa;AAKZ,IAAM,aAAN,MAAiB;AAAA,EACpB,YAAY,MAAM,OAAO,MAAM;AAC3B,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,SAAS,mBAAmB,OAAO;AAC/B,MAAI,YAAY,eAAe,IAAI,MAAM,IAAI;AAC7C,MAAI,CAAC,WAAW;AACZ;AAAA,EACJ;AACA,MAAI,aAAa,IAAI,WAAW,MAAM,MAAM,MAAM,IAAI;AACtD,MAAI,YAAY,OAAO;AACnB,eAAW,SAAS,MAAM;AAAA,EAC9B;AACA,cAAY,UAAU,OAAO,cAAY,CAAC,SAAS,SAAS,UAAU,CAAC;AACvE,MAAI,UAAU,WAAW,GAAG;AACxB,mBAAe,OAAO,MAAM,IAAI;AAAA,EACpC,OACK;AACD,mBAAe,IAAI,MAAM,MAAM,SAAS;AAAA,EAC5C;AACJ;AASO,SAAS,WAAW,WAAW,UAAU,cAAc;AAC1D,MAAI,YAAY,eAAe,IAAI,SAAS,KAAK,CAAC;AAClD,QAAM,eAAe,IAAI,SAAS,WAAW,UAAU,YAAY;AACnE,YAAU,KAAK,YAAY;AAC3B,iBAAe,IAAI,WAAW,SAAS;AACvC,SAAO,MAAM,YAAY,YAAY;AACzC;AAQO,SAAS,GAAG,WAAW,UAAU;AACpC,SAAO,WAAW,WAAW,UAAU,EAAE;AAC7C;AAQO,SAAS,KAAK,WAAW,UAAU;AACtC,SAAO,WAAW,WAAW,UAAU,CAAC;AAC5C;AAMO,SAAS,OAAO,YAAY;AAC/B,aAAW,QAAQ,eAAa,eAAe,OAAO,SAAS,CAAC;AACpE;AAIO,SAAS,SAAS;AACrB,iBAAe,MAAM;AACzB;AAOO,SAAS,KAAK,OAAO;AACxB,SAAOA,MAAK,YAAY,KAAK;AACjC;;;AGpGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,IAAMC,QAAO,iBAAiB,YAAY,OAAO;AACjD,IAAM,SAAS;AACf,IAAM,aAAa;AACnB,IAAM,aAAa;AAMZ,SAAS,SAAS;AACrB,SAAOA,MAAK,MAAM;AACtB;AAMO,SAAS,aAAa;AACzB,SAAOA,MAAK,UAAU;AAC1B;AAMO,SAAS,aAAa;AACzB,SAAOA,MAAK,UAAU;AAC1B;;;AC3BA,IAAM,iBAAiB;AACvB,IAAM,eAAe;AACrB,IAAM,cAAc;AACpB,IAAM,+BAA+B;AACrC,IAAM,8BAA8B;AACpC,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,mBAAmB;AACzB,IAAM,kBAAkB;AACxB,IAAM,gBAAgB;AACtB,IAAM,eAAe;AACrB,IAAMC,cAAa;AACnB,IAAM,kBAAkB;AACxB,IAAM,qBAAqB;AAC3B,IAAM,oBAAoB;AAC1B,IAAM,oBAAoB;AAC1B,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;AACvB,IAAM,aAAa;AACnB,IAAM,qBAAqB;AAC3B,IAAM,yBAAyB;AAC/B,IAAM,eAAe;AACrB,IAAM,kBAAkB;AACxB,IAAM,gBAAgB;AACtB,IAAM,oBAAoB;AAC1B,IAAM,uBAAuB;AAC7B,IAAM,4BAA4B;AAClC,IAAM,qBAAqB;AAC3B,IAAM,mCAAmC;AACzC,IAAM,mBAAmB;AACzB,IAAM,mBAAmB;AACzB,IAAM,4BAA4B;AAClC,IAAM,qBAAqB;AAC3B,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,IAAMC,cAAa;AACnB,IAAM,aAAa;AACnB,IAAM,yBAAyB;AAC/B,IAAM,uBAAuB;AAC7B,IAAM,qBAAqB;AAC3B,IAAM,mBAAmB;AACzB,IAAM,mBAAmB;AACzB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,kBAAkB;AAExB,IAAM,YAAY,OAAO,QAAQ;AACjC,IAAM,SAAN,MAAM,QAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,YAAY,OAAO,IAAI;AACnB,SAAK,SAAS,IAAI,iBAAiB,YAAY,QAAQ,IAAI;AAE3D,eAAW,UAAU,OAAO,oBAAoB,QAAO,SAAS,GAAG;AAC/D,UAAI,WAAW,iBACR,OAAO,KAAK,MAAM,MAAM,YAAY;AACvC,aAAK,MAAM,IAAI,KAAK,MAAM,EAAE,KAAK,IAAI;AAAA,MACzC;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,MAAM;AACN,WAAO,IAAI,QAAO,IAAI;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACP,WAAO,KAAK,SAAS,EAAE,cAAc;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,WAAO,KAAK,SAAS,EAAE,YAAY;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,WAAO,KAAK,SAAS,EAAE,WAAW;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAIA,yBAAyB;AACrB,WAAO,KAAK,SAAS,EAAE,4BAA4B;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAIA,wBAAwB;AACpB,WAAO,KAAK,SAAS,EAAE,2BAA2B;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,WAAO,KAAK,SAAS,EAAE,WAAW;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACV,WAAO,KAAK,SAAS,EAAE,iBAAiB;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AACT,WAAO,KAAK,SAAS,EAAE,gBAAgB;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY;AACR,WAAO,KAAK,SAAS,EAAE,eAAe;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACN,WAAO,KAAK,SAAS,EAAE,aAAa;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACL,WAAO,KAAK,SAAS,EAAE,YAAY;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACH,WAAO,KAAK,SAAS,EAAED,WAAU;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY;AACR,WAAO,KAAK,SAAS,EAAE,eAAe;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AACX,WAAO,KAAK,SAAS,EAAE,kBAAkB;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACV,WAAO,KAAK,SAAS,EAAE,iBAAiB;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACV,WAAO,KAAK,SAAS,EAAE,iBAAiB;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,WAAO,KAAK,SAAS,EAAE,cAAc;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,WAAO,KAAK,SAAS,EAAE,cAAc;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACH,WAAO,KAAK,SAAS,EAAE,UAAU;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACX,WAAO,KAAK,SAAS,EAAE,kBAAkB;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB;AACf,WAAO,KAAK,SAAS,EAAE,sBAAsB;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,WAAO,KAAK,SAAS,EAAE,YAAY;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY;AACR,WAAO,KAAK,SAAS,EAAE,eAAe;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACN,WAAO,KAAK,SAAS,EAAE,aAAa;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,GAAG,GAAG;AACd,WAAO,KAAK,SAAS,EAAE,mBAAmB,EAAE,GAAG,EAAE,CAAC;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,aAAa;AACxB,WAAO,KAAK,SAAS,EAAE,sBAAsB,EAAE,YAAY,CAAC;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,oBAAoB,GAAG,GAAG,GAAG,GAAG;AAC5B,WAAO,KAAK,SAAS,EAAE,2BAA2B,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,WAAW;AACpB,WAAO,KAAK,SAAS,EAAE,oBAAoB,EAAE,UAAU,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,2BAA2B,SAAS;AAChC,WAAO,KAAK,SAAS,EAAE,kCAAkC,EAAE,QAAQ,CAAC;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,OAAO,QAAQ;AACtB,WAAO,KAAK,SAAS,EAAE,kBAAkB,EAAE,OAAO,OAAO,CAAC;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,OAAO,QAAQ;AACtB,WAAO,KAAK,SAAS,EAAE,kBAAkB,EAAE,OAAO,OAAO,CAAC;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,GAAG,GAAG;AACtB,WAAO,KAAK,SAAS,EAAE,2BAA2B,EAAE,GAAG,EAAE,CAAC;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAaE,YAAW;AACpB,WAAO,KAAK,SAAS,EAAE,oBAAoB,EAAE,WAAAA,WAAU,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,OAAO,QAAQ;AACnB,WAAO,KAAK,SAAS,EAAE,eAAe,EAAE,OAAO,OAAO,CAAC;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO;AACZ,WAAO,KAAK,SAAS,EAAE,gBAAgB,EAAE,MAAM,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,MAAM;AACV,WAAO,KAAK,SAAS,EAAE,eAAe,EAAE,KAAK,CAAC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACH,WAAO,KAAK,SAAS,EAAED,WAAU;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACH,WAAO,KAAK,SAAS,EAAE,UAAU;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AACf,WAAO,KAAK,SAAS,EAAE,sBAAsB;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACb,WAAO,KAAK,SAAS,EAAE,oBAAoB;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACX,WAAO,KAAK,SAAS,EAAE,kBAAkB;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AACT,WAAO,KAAK,SAAS,EAAE,gBAAgB;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AACT,WAAO,KAAK,SAAS,EAAE,gBAAgB;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACJ,WAAO,KAAK,SAAS,EAAE,WAAW;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACH,WAAO,KAAK,SAAS,EAAE,UAAU;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,WAAO,KAAK,SAAS,EAAE,YAAY;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACN,WAAO,KAAK,SAAS,EAAE,aAAa;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AACR,WAAO,KAAK,SAAS,EAAE,eAAe;AAAA,EAC1C;AACJ;AAIA,IAAM,aAAa,IAAI,OAAO,EAAE;AAChC,IAAO,iBAAQ;;;ACrbf;AAAA;AAAA;AAAA;AAAA;AAoBA,SAAS,UAAU,WAAW,OAAO,MAAM;AACvC,OAAK,IAAI,WAAW,WAAW,IAAI,CAAC;AACxC;AAOA,SAAS,iBAAiB,YAAY,YAAY;AAC9C,QAAM,eAAe,eAAO,IAAI,UAAU;AAC1C,QAAM,SAAS,aAAa,UAAU;AACtC,MAAI,OAAO,WAAW,YAAY;AAC9B,YAAQ,MAAM,kBAAkB,UAAU,aAAa;AACvD;AAAA,EACJ;AACA,MAAI;AACA,WAAO,KAAK,YAAY;AAAA,EAC5B,SACO,GAAG;AACN,YAAQ,MAAM,gCAAgC,UAAU,OAAO,CAAC;AAAA,EACpE;AACJ;AAIA,SAAS,eAAe,IAAI;AACxB,QAAM,UAAU,GAAG;AACnB,WAAS,UAAU,SAAS,OAAO;AAC/B,QAAI,WAAW;AACX;AACJ,UAAM,YAAY,QAAQ,aAAa,WAAW,KAAK,QAAQ,aAAa,gBAAgB;AAC5F,UAAM,eAAe,QAAQ,aAAa,mBAAmB,KAAK,QAAQ,aAAa,wBAAwB,KAAK;AACpH,UAAM,eAAe,QAAQ,aAAa,YAAY,KAAK,QAAQ,aAAa,iBAAiB;AACjG,UAAM,MAAM,QAAQ,aAAa,aAAa,KAAK,QAAQ,aAAa,kBAAkB;AAC1F,QAAI,cAAc;AACd,gBAAU,SAAS;AACvB,QAAI,iBAAiB;AACjB,uBAAiB,cAAc,YAAY;AAC/C,QAAI,QAAQ;AACR,WAAK,QAAQ,GAAG;AAAA,EACxB;AACA,QAAM,UAAU,QAAQ,aAAa,aAAa,KAAK,QAAQ,aAAa,kBAAkB;AAC9F,MAAI,SAAS;AACT,aAAS;AAAA,MACL,OAAO;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,QACL,EAAE,OAAO,MAAM;AAAA,QACf,EAAE,OAAO,MAAM,WAAW,KAAK;AAAA,MACnC;AAAA,IACJ,CAAC,EAAE,KAAK,SAAS;AAAA,EACrB,OACK;AACD,cAAU;AAAA,EACd;AACJ;AAEA,IAAM,gBAAgB,OAAO,YAAY;AACzC,IAAM,gBAAgB,OAAO,YAAY;AACzC,IAAM,kBAAkB,OAAO,cAAc;AAK7C,IAAM,0BAAN,MAA8B;AAAA,EAC1B,cAAc;AACV,SAAK,aAAa,IAAI,IAAI,gBAAgB;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,SAAS,UAAU;AACnB,WAAO,EAAE,QAAQ,KAAK,aAAa,EAAE,OAAO;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,SAAK,aAAa,EAAE,MAAM;AAC1B,SAAK,aAAa,IAAI,IAAI,gBAAgB;AAAA,EAC9C;AACJ;AAMA,IAAM,kBAAN,MAAsB;AAAA,EAClB,cAAc;AACV,SAAK,aAAa,IAAI,oBAAI,QAAQ;AAClC,SAAK,eAAe,IAAI;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,SAAS,UAAU;AACnB,QAAI,CAAC,KAAK,aAAa,EAAE,IAAI,OAAO,GAAG;AACnC,WAAK,eAAe;AAAA,IACxB;AACA,SAAK,aAAa,EAAE,IAAI,SAAS,QAAQ;AACzC,WAAO,CAAC;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,QAAI,KAAK,eAAe,KAAK;AACzB;AACJ,eAAW,WAAW,SAAS,KAAK,iBAAiB,GAAG,GAAG;AACvD,UAAI,KAAK,eAAe,KAAK;AACzB;AACJ,YAAM,WAAW,KAAK,aAAa,EAAE,IAAI,OAAO;AAChD,UAAI,YAAY,MAAM;AAClB,aAAK,eAAe;AAAA,MACxB;AACA,iBAAW,WAAW,YAAY,CAAC;AAC/B,gBAAQ,oBAAoB,SAAS,cAAc;AAAA,IAC3D;AACA,SAAK,aAAa,IAAI,oBAAI,QAAQ;AAClC,SAAK,eAAe,IAAI;AAAA,EAC5B;AACJ;AACA,IAAM,kBAAkB,kBAAkB,IAAI,IAAI,wBAAwB,IAAI,IAAI,gBAAgB;AAIlG,SAAS,gBAAgB,SAAS;AAC9B,QAAM,gBAAgB;AACtB,QAAM,cAAe,QAAQ,aAAa,aAAa,KAAK,QAAQ,aAAa,kBAAkB,KAAK;AACxG,QAAM,WAAW,CAAC;AAClB,MAAI;AACJ,UAAQ,QAAQ,cAAc,KAAK,WAAW,OAAO;AACjD,aAAS,KAAK,MAAM,CAAC,CAAC;AAC1B,QAAM,UAAU,gBAAgB,IAAI,SAAS,QAAQ;AACrD,aAAW,WAAW;AAClB,YAAQ,iBAAiB,SAAS,gBAAgB,OAAO;AACjE;AAIO,SAAS,SAAS;AACrB,YAAU,MAAM;AACpB;AAIO,SAAS,SAAS;AACrB,kBAAgB,MAAM;AACtB,WAAS,KAAK,iBAAiB,mGAAmG,EAAE,QAAQ,eAAe;AAC/J;;;ACxKA,OAAO,SAAS,OAAO,UAAU,CAAC;AA0BlC,OAAO,OAAO,SAAgB;AACvB,OAAO,qBAAqB;", "names": ["_a", "_a", "_a", "call", "call", "call", "isDocumentDotAll", "_a", "reason", "value", "call", "call", "Array", "Map", "key", "Error", "call", "getAndDeleteResponse", "_a", "generateID", "Error", "call", "call", "HideMethod", "ShowMethod", "resizable"]}