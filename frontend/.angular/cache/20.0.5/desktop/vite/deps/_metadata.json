{"hash": "defbc981", "configHash": "fbf79606", "lockfileHash": "9e817336", "browserHash": "c7ceb518", "optimized": {"@angular/common": {"src": "../../../../../../node_modules/@angular/common/fesm2022/common.mjs", "file": "@angular_common.js", "fileHash": "1307aba1", "needsInterop": false}, "@angular/common/http": {"src": "../../../../../../node_modules/@angular/common/fesm2022/http.mjs", "file": "@angular_common_http.js", "fileHash": "19f57239", "needsInterop": false}, "@angular/core": {"src": "../../../../../../node_modules/@angular/core/fesm2022/core.mjs", "file": "@angular_core.js", "fileHash": "59fd688b", "needsInterop": false}, "@angular/forms": {"src": "../../../../../../node_modules/@angular/forms/fesm2022/forms.mjs", "file": "@angular_forms.js", "fileHash": "4f9b49c3", "needsInterop": false}, "@angular/platform-browser": {"src": "../../../../../../node_modules/@angular/platform-browser/fesm2022/platform-browser.mjs", "file": "@angular_platform-browser.js", "fileHash": "3e0cbc9c", "needsInterop": false}, "@wailsio/runtime": {"src": "../../../../../../node_modules/@wailsio/runtime/dist/index.js", "file": "@wailsio_runtime.js", "fileHash": "490a71a3", "needsInterop": false}, "lucide-angular": {"src": "../../../../../../node_modules/lucide-angular/fesm2020/lucide-angular.mjs", "file": "lucide-angular.js", "fileHash": "b29c8e53", "needsInterop": false}, "rxjs": {"src": "../../../../../../node_modules/rxjs/dist/esm5/index.js", "file": "rxjs.js", "fileHash": "246e59f0", "needsInterop": false}, "rxjs/operators": {"src": "../../../../../../node_modules/rxjs/dist/esm5/operators/index.js", "file": "rxjs_operators.js", "fileHash": "415e57cc", "needsInterop": false}, "zone.js": {"src": "../../../../../../node_modules/zone.js/fesm2015/zone.js", "file": "zone__js.js", "fileHash": "3a120f2b", "needsInterop": true}}, "chunks": {"chunk-K7EOE762": {"file": "chunk-K7EOE762.js"}, "chunk-OUNNCSAE": {"file": "chunk-OUNNCSAE.js"}, "chunk-5KK3G4LL": {"file": "chunk-5KK3G4LL.js"}, "chunk-3O5UWTH4": {"file": "chunk-3O5UWTH4.js"}, "chunk-G6ECYYJH": {"file": "chunk-G6ECYYJH.js"}, "chunk-YVXMBCE5": {"file": "chunk-YVXMBCE5.js"}, "chunk-RTGP7ALM": {"file": "chunk-RTGP7ALM.js"}, "chunk-XWLXMCJQ": {"file": "chunk-XWLXMCJQ.js"}}}