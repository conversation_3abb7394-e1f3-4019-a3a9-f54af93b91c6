{"version": 3, "sources": ["../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/performanceTimestampProvider.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/animationFrameProvider.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/dom/animationFrames.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/util/Immediate.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/immediateProvider.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/AsapAction.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/AsapScheduler.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/asap.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/QueueAction.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/QueueScheduler.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/queue.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/AnimationFrameAction.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/AnimationFrameScheduler.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/animationFrame.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/scheduler/VirtualTimeScheduler.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/util/isObservable.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/lastValueFrom.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/firstValueFrom.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/bindCallbackInternals.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/bindCallback.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/bindNodeCallback.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/defer.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/connectable.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/forkJoin.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/fromEvent.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/fromEventPattern.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/generate.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/iif.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/merge.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/never.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/pairs.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/partition.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/range.js", "../../../../../../node_modules/rxjs/dist/esm5/internal/observable/using.js"], "sourcesContent": ["export var performanceTimestampProvider = {\n    now: function () {\n        return (performanceTimestampProvider.delegate || performance).now();\n    },\n    delegate: undefined,\n};\n", "import { __read, __spreadArray } from \"tslib\";\nimport { Subscription } from '../Subscription';\nexport var animationFrameProvider = {\n    schedule: function (callback) {\n        var request = requestAnimationFrame;\n        var cancel = cancelAnimationFrame;\n        var delegate = animationFrameProvider.delegate;\n        if (delegate) {\n            request = delegate.requestAnimationFrame;\n            cancel = delegate.cancelAnimationFrame;\n        }\n        var handle = request(function (timestamp) {\n            cancel = undefined;\n            callback(timestamp);\n        });\n        return new Subscription(function () { return cancel === null || cancel === void 0 ? void 0 : cancel(handle); });\n    },\n    requestAnimationFrame: function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var delegate = animationFrameProvider.delegate;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.requestAnimationFrame) || requestAnimationFrame).apply(void 0, __spreadArray([], __read(args)));\n    },\n    cancelAnimationFrame: function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var delegate = animationFrameProvider.delegate;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.cancelAnimationFrame) || cancelAnimationFrame).apply(void 0, __spreadArray([], __read(args)));\n    },\n    delegate: undefined,\n};\n", "import { Observable } from '../../Observable';\nimport { performanceTimestampProvider } from '../../scheduler/performanceTimestampProvider';\nimport { animationFrameProvider } from '../../scheduler/animationFrameProvider';\nexport function animationFrames(timestampProvider) {\n    return timestampProvider ? animationFramesFactory(timestampProvider) : DEFAULT_ANIMATION_FRAMES;\n}\nfunction animationFramesFactory(timestampProvider) {\n    return new Observable(function (subscriber) {\n        var provider = timestampProvider || performanceTimestampProvider;\n        var start = provider.now();\n        var id = 0;\n        var run = function () {\n            if (!subscriber.closed) {\n                id = animationFrameProvider.requestAnimationFrame(function (timestamp) {\n                    id = 0;\n                    var now = provider.now();\n                    subscriber.next({\n                        timestamp: timestampProvider ? now : timestamp,\n                        elapsed: now - start,\n                    });\n                    run();\n                });\n            }\n        };\n        run();\n        return function () {\n            if (id) {\n                animationFrameProvider.cancelAnimationFrame(id);\n            }\n        };\n    });\n}\nvar DEFAULT_ANIMATION_FRAMES = animationFramesFactory();\n", "var nextHandle = 1;\nvar resolved;\nvar activeHandles = {};\nfunction findAndClearHandle(handle) {\n    if (handle in activeHandles) {\n        delete activeHandles[handle];\n        return true;\n    }\n    return false;\n}\nexport var Immediate = {\n    setImmediate: function (cb) {\n        var handle = nextHandle++;\n        activeHandles[handle] = true;\n        if (!resolved) {\n            resolved = Promise.resolve();\n        }\n        resolved.then(function () { return findAndClearHandle(handle) && cb(); });\n        return handle;\n    },\n    clearImmediate: function (handle) {\n        findAndClearHandle(handle);\n    },\n};\nexport var TestTools = {\n    pending: function () {\n        return Object.keys(activeHandles).length;\n    }\n};\n", "import { __read, __spreadArray } from \"tslib\";\nimport { Immediate } from '../util/Immediate';\nvar setImmediate = Immediate.setImmediate, clearImmediate = Immediate.clearImmediate;\nexport var immediateProvider = {\n    setImmediate: function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var delegate = immediateProvider.delegate;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.setImmediate) || setImmediate).apply(void 0, __spreadArray([], __read(args)));\n    },\n    clearImmediate: function (handle) {\n        var delegate = immediateProvider.delegate;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearImmediate) || clearImmediate)(handle);\n    },\n    delegate: undefined,\n};\n", "import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nimport { immediateProvider } from './immediateProvider';\nvar AsapAction = (function (_super) {\n    __extends(AsapAction, _super);\n    function AsapAction(scheduler, work) {\n        var _this = _super.call(this, scheduler, work) || this;\n        _this.scheduler = scheduler;\n        _this.work = work;\n        return _this;\n    }\n    AsapAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n        if (delay === void 0) { delay = 0; }\n        if (delay !== null && delay > 0) {\n            return _super.prototype.requestAsyncId.call(this, scheduler, id, delay);\n        }\n        scheduler.actions.push(this);\n        return scheduler._scheduled || (scheduler._scheduled = immediateProvider.setImmediate(scheduler.flush.bind(scheduler, undefined)));\n    };\n    AsapAction.prototype.recycleAsyncId = function (scheduler, id, delay) {\n        var _a;\n        if (delay === void 0) { delay = 0; }\n        if (delay != null ? delay > 0 : this.delay > 0) {\n            return _super.prototype.recycleAsyncId.call(this, scheduler, id, delay);\n        }\n        var actions = scheduler.actions;\n        if (id != null && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n            immediateProvider.clearImmediate(id);\n            if (scheduler._scheduled === id) {\n                scheduler._scheduled = undefined;\n            }\n        }\n        return undefined;\n    };\n    return AsapAction;\n}(AsyncAction));\nexport { AsapAction };\n", "import { __extends } from \"tslib\";\nimport { AsyncScheduler } from './AsyncScheduler';\nvar AsapScheduler = (function (_super) {\n    __extends(AsapScheduler, _super);\n    function AsapScheduler() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    AsapScheduler.prototype.flush = function (action) {\n        this._active = true;\n        var flushId = this._scheduled;\n        this._scheduled = undefined;\n        var actions = this.actions;\n        var error;\n        action = action || actions.shift();\n        do {\n            if ((error = action.execute(action.state, action.delay))) {\n                break;\n            }\n        } while ((action = actions[0]) && action.id === flushId && actions.shift());\n        this._active = false;\n        if (error) {\n            while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    };\n    return AsapScheduler;\n}(AsyncScheduler));\nexport { AsapScheduler };\n", "import { AsapAction } from './AsapAction';\nimport { AsapScheduler } from './AsapScheduler';\nexport var asapScheduler = new AsapScheduler(AsapAction);\nexport var asap = asapScheduler;\n", "import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nvar QueueAction = (function (_super) {\n    __extends(QueueAction, _super);\n    function QueueAction(scheduler, work) {\n        var _this = _super.call(this, scheduler, work) || this;\n        _this.scheduler = scheduler;\n        _this.work = work;\n        return _this;\n    }\n    QueueAction.prototype.schedule = function (state, delay) {\n        if (delay === void 0) { delay = 0; }\n        if (delay > 0) {\n            return _super.prototype.schedule.call(this, state, delay);\n        }\n        this.delay = delay;\n        this.state = state;\n        this.scheduler.flush(this);\n        return this;\n    };\n    QueueAction.prototype.execute = function (state, delay) {\n        return delay > 0 || this.closed ? _super.prototype.execute.call(this, state, delay) : this._execute(state, delay);\n    };\n    QueueAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n        if (delay === void 0) { delay = 0; }\n        if ((delay != null && delay > 0) || (delay == null && this.delay > 0)) {\n            return _super.prototype.requestAsyncId.call(this, scheduler, id, delay);\n        }\n        scheduler.flush(this);\n        return 0;\n    };\n    return QueueAction;\n}(AsyncAction));\nexport { QueueAction };\n", "import { __extends } from \"tslib\";\nimport { AsyncScheduler } from './AsyncScheduler';\nvar QueueScheduler = (function (_super) {\n    __extends(QueueScheduler, _super);\n    function QueueScheduler() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return QueueScheduler;\n}(AsyncScheduler));\nexport { QueueScheduler };\n", "import { QueueAction } from './QueueAction';\nimport { QueueScheduler } from './QueueScheduler';\nexport var queueScheduler = new QueueScheduler(QueueAction);\nexport var queue = queueScheduler;\n", "import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nimport { animationFrameProvider } from './animationFrameProvider';\nvar AnimationFrameAction = (function (_super) {\n    __extends(AnimationFrameAction, _super);\n    function AnimationFrameAction(scheduler, work) {\n        var _this = _super.call(this, scheduler, work) || this;\n        _this.scheduler = scheduler;\n        _this.work = work;\n        return _this;\n    }\n    AnimationFrameAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n        if (delay === void 0) { delay = 0; }\n        if (delay !== null && delay > 0) {\n            return _super.prototype.requestAsyncId.call(this, scheduler, id, delay);\n        }\n        scheduler.actions.push(this);\n        return scheduler._scheduled || (scheduler._scheduled = animationFrameProvider.requestAnimationFrame(function () { return scheduler.flush(undefined); }));\n    };\n    AnimationFrameAction.prototype.recycleAsyncId = function (scheduler, id, delay) {\n        var _a;\n        if (delay === void 0) { delay = 0; }\n        if (delay != null ? delay > 0 : this.delay > 0) {\n            return _super.prototype.recycleAsyncId.call(this, scheduler, id, delay);\n        }\n        var actions = scheduler.actions;\n        if (id != null && id === scheduler._scheduled && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n            animationFrameProvider.cancelAnimationFrame(id);\n            scheduler._scheduled = undefined;\n        }\n        return undefined;\n    };\n    return AnimationFrameAction;\n}(AsyncAction));\nexport { AnimationFrameAction };\n", "import { __extends } from \"tslib\";\nimport { AsyncScheduler } from './AsyncScheduler';\nvar AnimationFrameScheduler = (function (_super) {\n    __extends(AnimationFrameScheduler, _super);\n    function AnimationFrameScheduler() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    AnimationFrameScheduler.prototype.flush = function (action) {\n        this._active = true;\n        var flushId;\n        if (action) {\n            flushId = action.id;\n        }\n        else {\n            flushId = this._scheduled;\n            this._scheduled = undefined;\n        }\n        var actions = this.actions;\n        var error;\n        action = action || actions.shift();\n        do {\n            if ((error = action.execute(action.state, action.delay))) {\n                break;\n            }\n        } while ((action = actions[0]) && action.id === flushId && actions.shift());\n        this._active = false;\n        if (error) {\n            while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    };\n    return AnimationFrameScheduler;\n}(AsyncScheduler));\nexport { AnimationFrameScheduler };\n", "import { AnimationFrameAction } from './AnimationFrameAction';\nimport { AnimationFrameScheduler } from './AnimationFrameScheduler';\nexport var animationFrameScheduler = new AnimationFrameScheduler(AnimationFrameAction);\nexport var animationFrame = animationFrameScheduler;\n", "import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nimport { Subscription } from '../Subscription';\nimport { AsyncScheduler } from './AsyncScheduler';\nvar VirtualTimeScheduler = (function (_super) {\n    __extends(VirtualTimeScheduler, _super);\n    function VirtualTimeScheduler(schedulerActionCtor, maxFrames) {\n        if (schedulerActionCtor === void 0) { schedulerActionCtor = VirtualAction; }\n        if (maxFrames === void 0) { maxFrames = Infinity; }\n        var _this = _super.call(this, schedulerActionCtor, function () { return _this.frame; }) || this;\n        _this.maxFrames = maxFrames;\n        _this.frame = 0;\n        _this.index = -1;\n        return _this;\n    }\n    VirtualTimeScheduler.prototype.flush = function () {\n        var _a = this, actions = _a.actions, maxFrames = _a.maxFrames;\n        var error;\n        var action;\n        while ((action = actions[0]) && action.delay <= maxFrames) {\n            actions.shift();\n            this.frame = action.delay;\n            if ((error = action.execute(action.state, action.delay))) {\n                break;\n            }\n        }\n        if (error) {\n            while ((action = actions.shift())) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    };\n    VirtualTimeScheduler.frameTimeFactor = 10;\n    return VirtualTimeScheduler;\n}(AsyncScheduler));\nexport { VirtualTimeScheduler };\nvar VirtualAction = (function (_super) {\n    __extends(VirtualAction, _super);\n    function VirtualAction(scheduler, work, index) {\n        if (index === void 0) { index = (scheduler.index += 1); }\n        var _this = _super.call(this, scheduler, work) || this;\n        _this.scheduler = scheduler;\n        _this.work = work;\n        _this.index = index;\n        _this.active = true;\n        _this.index = scheduler.index = index;\n        return _this;\n    }\n    VirtualAction.prototype.schedule = function (state, delay) {\n        if (delay === void 0) { delay = 0; }\n        if (Number.isFinite(delay)) {\n            if (!this.id) {\n                return _super.prototype.schedule.call(this, state, delay);\n            }\n            this.active = false;\n            var action = new VirtualAction(this.scheduler, this.work);\n            this.add(action);\n            return action.schedule(state, delay);\n        }\n        else {\n            return Subscription.EMPTY;\n        }\n    };\n    VirtualAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n        if (delay === void 0) { delay = 0; }\n        this.delay = scheduler.frame + delay;\n        var actions = scheduler.actions;\n        actions.push(this);\n        actions.sort(VirtualAction.sortActions);\n        return 1;\n    };\n    VirtualAction.prototype.recycleAsyncId = function (scheduler, id, delay) {\n        if (delay === void 0) { delay = 0; }\n        return undefined;\n    };\n    VirtualAction.prototype._execute = function (state, delay) {\n        if (this.active === true) {\n            return _super.prototype._execute.call(this, state, delay);\n        }\n    };\n    VirtualAction.sortActions = function (a, b) {\n        if (a.delay === b.delay) {\n            if (a.index === b.index) {\n                return 0;\n            }\n            else if (a.index > b.index) {\n                return 1;\n            }\n            else {\n                return -1;\n            }\n        }\n        else if (a.delay > b.delay) {\n            return 1;\n        }\n        else {\n            return -1;\n        }\n    };\n    return VirtualAction;\n}(AsyncAction));\nexport { VirtualAction };\n", "import { Observable } from '../Observable';\nimport { isFunction } from './isFunction';\nexport function isObservable(obj) {\n    return !!obj && (obj instanceof Observable || (isFunction(obj.lift) && isFunction(obj.subscribe)));\n}\n", "import { EmptyError } from './util/EmptyError';\nexport function lastValueFrom(source, config) {\n    var hasConfig = typeof config === 'object';\n    return new Promise(function (resolve, reject) {\n        var _hasValue = false;\n        var _value;\n        source.subscribe({\n            next: function (value) {\n                _value = value;\n                _hasValue = true;\n            },\n            error: reject,\n            complete: function () {\n                if (_hasValue) {\n                    resolve(_value);\n                }\n                else if (hasConfig) {\n                    resolve(config.defaultValue);\n                }\n                else {\n                    reject(new EmptyError());\n                }\n            },\n        });\n    });\n}\n", "import { EmptyError } from './util/EmptyError';\nimport { SafeSubscriber } from './Subscriber';\nexport function firstValueFrom(source, config) {\n    var hasConfig = typeof config === 'object';\n    return new Promise(function (resolve, reject) {\n        var subscriber = new SafeSubscriber({\n            next: function (value) {\n                resolve(value);\n                subscriber.unsubscribe();\n            },\n            error: reject,\n            complete: function () {\n                if (hasConfig) {\n                    resolve(config.defaultValue);\n                }\n                else {\n                    reject(new EmptyError());\n                }\n            },\n        });\n        source.subscribe(subscriber);\n    });\n}\n", "import { __read, __spreadArray } from \"tslib\";\nimport { isScheduler } from '../util/isScheduler';\nimport { Observable } from '../Observable';\nimport { subscribeOn } from '../operators/subscribeOn';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { observeOn } from '../operators/observeOn';\nimport { AsyncSubject } from '../AsyncSubject';\nexport function bindCallbackInternals(isNodeStyle, callbackFunc, resultSelector, scheduler) {\n    if (resultSelector) {\n        if (isScheduler(resultSelector)) {\n            scheduler = resultSelector;\n        }\n        else {\n            return function () {\n                var args = [];\n                for (var _i = 0; _i < arguments.length; _i++) {\n                    args[_i] = arguments[_i];\n                }\n                return bindCallbackInternals(isNodeStyle, callbackFunc, scheduler)\n                    .apply(this, args)\n                    .pipe(mapOneOrManyArgs(resultSelector));\n            };\n        }\n    }\n    if (scheduler) {\n        return function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return bindCallbackInternals(isNodeStyle, callbackFunc)\n                .apply(this, args)\n                .pipe(subscribeOn(scheduler), observeOn(scheduler));\n        };\n    }\n    return function () {\n        var _this = this;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var subject = new AsyncSubject();\n        var uninitialized = true;\n        return new Observable(function (subscriber) {\n            var subs = subject.subscribe(subscriber);\n            if (uninitialized) {\n                uninitialized = false;\n                var isAsync_1 = false;\n                var isComplete_1 = false;\n                callbackFunc.apply(_this, __spreadArray(__spreadArray([], __read(args)), [\n                    function () {\n                        var results = [];\n                        for (var _i = 0; _i < arguments.length; _i++) {\n                            results[_i] = arguments[_i];\n                        }\n                        if (isNodeStyle) {\n                            var err = results.shift();\n                            if (err != null) {\n                                subject.error(err);\n                                return;\n                            }\n                        }\n                        subject.next(1 < results.length ? results : results[0]);\n                        isComplete_1 = true;\n                        if (isAsync_1) {\n                            subject.complete();\n                        }\n                    },\n                ]));\n                if (isComplete_1) {\n                    subject.complete();\n                }\n                isAsync_1 = true;\n            }\n            return subs;\n        });\n    };\n}\n", "import { bindCallbackInternals } from './bindCallbackInternals';\nexport function bindCallback(callbackFunc, resultSelector, scheduler) {\n    return bindCallbackInternals(false, callbackFunc, resultSelector, scheduler);\n}\n", "import { bindCallbackInternals } from './bindCallbackInternals';\nexport function bindNodeCallback(callbackFunc, resultSelector, scheduler) {\n    return bindCallbackInternals(true, callbackFunc, resultSelector, scheduler);\n}\n", "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nexport function defer(observableFactory) {\n    return new Observable(function (subscriber) {\n        innerFrom(observableFactory()).subscribe(subscriber);\n    });\n}\n", "import { Subject } from '../Subject';\nimport { Observable } from '../Observable';\nimport { defer } from './defer';\nvar DEFAULT_CONFIG = {\n    connector: function () { return new Subject(); },\n    resetOnDisconnect: true,\n};\nexport function connectable(source, config) {\n    if (config === void 0) { config = DEFAULT_CONFIG; }\n    var connection = null;\n    var connector = config.connector, _a = config.resetOnDisconnect, resetOnDisconnect = _a === void 0 ? true : _a;\n    var subject = connector();\n    var result = new Observable(function (subscriber) {\n        return subject.subscribe(subscriber);\n    });\n    result.connect = function () {\n        if (!connection || connection.closed) {\n            connection = defer(function () { return source; }).subscribe(subject);\n            if (resetOnDisconnect) {\n                connection.add(function () { return (subject = connector()); });\n            }\n        }\n        return connection;\n    };\n    return result;\n}\n", "import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { innerFrom } from './innerFrom';\nimport { popResultSelector } from '../util/args';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { createObject } from '../util/createObject';\nexport function forkJoin() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var resultSelector = popResultSelector(args);\n    var _a = argsArgArrayOrObject(args), sources = _a.args, keys = _a.keys;\n    var result = new Observable(function (subscriber) {\n        var length = sources.length;\n        if (!length) {\n            subscriber.complete();\n            return;\n        }\n        var values = new Array(length);\n        var remainingCompletions = length;\n        var remainingEmissions = length;\n        var _loop_1 = function (sourceIndex) {\n            var hasValue = false;\n            innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, function (value) {\n                if (!hasValue) {\n                    hasValue = true;\n                    remainingEmissions--;\n                }\n                values[sourceIndex] = value;\n            }, function () { return remainingCompletions--; }, undefined, function () {\n                if (!remainingCompletions || !hasValue) {\n                    if (!remainingEmissions) {\n                        subscriber.next(keys ? createObject(keys, values) : values);\n                    }\n                    subscriber.complete();\n                }\n            }));\n        };\n        for (var sourceIndex = 0; sourceIndex < length; sourceIndex++) {\n            _loop_1(sourceIndex);\n        }\n    });\n    return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\n", "import { __read } from \"tslib\";\nimport { innerFrom } from '../observable/innerFrom';\nimport { Observable } from '../Observable';\nimport { mergeMap } from '../operators/mergeMap';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nvar nodeEventEmitterMethods = ['addListener', 'removeListener'];\nvar eventTargetMethods = ['addEventListener', 'removeEventListener'];\nvar jqueryMethods = ['on', 'off'];\nexport function fromEvent(target, eventName, options, resultSelector) {\n    if (isFunction(options)) {\n        resultSelector = options;\n        options = undefined;\n    }\n    if (resultSelector) {\n        return fromEvent(target, eventName, options).pipe(mapOneOrManyArgs(resultSelector));\n    }\n    var _a = __read(isEventTarget(target)\n        ? eventTargetMethods.map(function (methodName) { return function (handler) { return target[methodName](eventName, handler, options); }; })\n        :\n            isNodeStyleEventEmitter(target)\n                ? nodeEventEmitterMethods.map(toCommonHandlerRegistry(target, eventName))\n                : isJQueryStyleEventEmitter(target)\n                    ? jqueryMethods.map(toCommonHandlerRegistry(target, eventName))\n                    : [], 2), add = _a[0], remove = _a[1];\n    if (!add) {\n        if (isArrayLike(target)) {\n            return mergeMap(function (subTarget) { return fromEvent(subTarget, eventName, options); })(innerFrom(target));\n        }\n    }\n    if (!add) {\n        throw new TypeError('Invalid event target');\n    }\n    return new Observable(function (subscriber) {\n        var handler = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return subscriber.next(1 < args.length ? args : args[0]);\n        };\n        add(handler);\n        return function () { return remove(handler); };\n    });\n}\nfunction toCommonHandlerRegistry(target, eventName) {\n    return function (methodName) { return function (handler) { return target[methodName](eventName, handler); }; };\n}\nfunction isNodeStyleEventEmitter(target) {\n    return isFunction(target.addListener) && isFunction(target.removeListener);\n}\nfunction isJQueryStyleEventEmitter(target) {\n    return isFunction(target.on) && isFunction(target.off);\n}\nfunction isEventTarget(target) {\n    return isFunction(target.addEventListener) && isFunction(target.removeEventListener);\n}\n", "import { Observable } from '../Observable';\nimport { isFunction } from '../util/isFunction';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nexport function fromEventPattern(add<PERSON><PERSON><PERSON>, remove<PERSON><PERSON><PERSON>, resultSelector) {\n    if (resultSelector) {\n        return fromEventPattern(addH<PERSON><PERSON>, removeHandler).pipe(mapOneOrManyArgs(resultSelector));\n    }\n    return new Observable(function (subscriber) {\n        var handler = function () {\n            var e = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                e[_i] = arguments[_i];\n            }\n            return subscriber.next(e.length === 1 ? e[0] : e);\n        };\n        var retValue = addHandler(handler);\n        return isFunction(removeHandler) ? function () { return removeHandler(handler, retValue); } : undefined;\n    });\n}\n", "import { __generator } from \"tslib\";\nimport { identity } from '../util/identity';\nimport { isScheduler } from '../util/isScheduler';\nimport { defer } from './defer';\nimport { scheduleIterable } from '../scheduled/scheduleIterable';\nexport function generate(initialStateOrOptions, condition, iterate, resultSelectorOrScheduler, scheduler) {\n    var _a, _b;\n    var resultSelector;\n    var initialState;\n    if (arguments.length === 1) {\n        (_a = initialStateOrOptions, initialState = _a.initialState, condition = _a.condition, iterate = _a.iterate, _b = _a.resultSelector, resultSelector = _b === void 0 ? identity : _b, scheduler = _a.scheduler);\n    }\n    else {\n        initialState = initialStateOrOptions;\n        if (!resultSelectorOrScheduler || isScheduler(resultSelectorOrScheduler)) {\n            resultSelector = identity;\n            scheduler = resultSelectorOrScheduler;\n        }\n        else {\n            resultSelector = resultSelectorOrScheduler;\n        }\n    }\n    function gen() {\n        var state;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    state = initialState;\n                    _a.label = 1;\n                case 1:\n                    if (!(!condition || condition(state))) return [3, 4];\n                    return [4, resultSelector(state)];\n                case 2:\n                    _a.sent();\n                    _a.label = 3;\n                case 3:\n                    state = iterate(state);\n                    return [3, 1];\n                case 4: return [2];\n            }\n        });\n    }\n    return defer((scheduler\n        ?\n            function () { return scheduleIterable(gen(), scheduler); }\n        :\n            gen));\n}\n", "import { defer } from './defer';\nexport function iif(condition, trueResult, falseResult) {\n    return defer(function () { return (condition() ? trueResult : falseResult); });\n}\n", "import { mergeAll } from '../operators/mergeAll';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nimport { popNumber, popScheduler } from '../util/args';\nimport { from } from './from';\nexport function merge() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var scheduler = popScheduler(args);\n    var concurrent = popNumber(args, Infinity);\n    var sources = args;\n    return !sources.length\n        ?\n            EMPTY\n        : sources.length === 1\n            ?\n                innerFrom(sources[0])\n            :\n                mergeAll(concurrent)(from(sources, scheduler));\n}\n", "import { Observable } from '../Observable';\nimport { noop } from '../util/noop';\nexport var NEVER = new Observable(noop);\nexport function never() {\n    return NEVER;\n}\n", "import { from } from './from';\nexport function pairs(obj, scheduler) {\n    return from(Object.entries(obj), scheduler);\n}\n", "import { not } from '../util/not';\nimport { filter } from '../operators/filter';\nimport { innerFrom } from './innerFrom';\nexport function partition(source, predicate, thisArg) {\n    return [filter(predicate, thisArg)(innerFrom(source)), filter(not(predicate, thisArg))(innerFrom(source))];\n}\n", "import { Observable } from '../Observable';\nimport { EMPTY } from './empty';\nexport function range(start, count, scheduler) {\n    if (count == null) {\n        count = start;\n        start = 0;\n    }\n    if (count <= 0) {\n        return EMPTY;\n    }\n    var end = count + start;\n    return new Observable(scheduler\n        ?\n            function (subscriber) {\n                var n = start;\n                return scheduler.schedule(function () {\n                    if (n < end) {\n                        subscriber.next(n++);\n                        this.schedule();\n                    }\n                    else {\n                        subscriber.complete();\n                    }\n                });\n            }\n        :\n            function (subscriber) {\n                var n = start;\n                while (n < end && !subscriber.closed) {\n                    subscriber.next(n++);\n                }\n                subscriber.complete();\n            });\n}\n", "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nexport function using(resourceFactory, observableFactory) {\n    return new Observable(function (subscriber) {\n        var resource = resourceFactory();\n        var result = observableFactory(resource);\n        var source = result ? innerFrom(result) : EMPTY;\n        source.subscribe(subscriber);\n        return function () {\n            if (resource) {\n                resource.unsubscribe();\n            }\n        };\n    });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,IAAI,+BAA+B;AAAA,EACtC,KAAK,WAAY;AACb,YAAQ,6BAA6B,YAAY,aAAa,IAAI;AAAA,EACtE;AAAA,EACA,UAAU;AACd;;;ACHO,IAAI,yBAAyB;AAAA,EAChC,UAAU,SAAU,UAAU;AAC1B,QAAI,UAAU;AACd,QAAI,SAAS;AACb,QAAI,WAAW,uBAAuB;AACtC,QAAI,UAAU;AACV,gBAAU,SAAS;AACnB,eAAS,SAAS;AAAA,IACtB;AACA,QAAI,SAAS,QAAQ,SAAUA,YAAW;AACtC,eAAS;AACT,eAASA,UAAS;AAAA,IACtB,CAAC;AACD,WAAO,IAAI,aAAa,WAAY;AAAE,aAAO,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,MAAM;AAAA,IAAG,CAAC;AAAA,EAClH;AAAA,EACA,uBAAuB,WAAY;AAC/B,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,QAAI,WAAW,uBAAuB;AACtC,aAAS,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,0BAA0B,uBAAuB,MAAM,QAAQ,cAAc,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AAAA,EACxK;AAAA,EACA,sBAAsB,WAAY;AAC9B,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,QAAI,WAAW,uBAAuB;AACtC,aAAS,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,yBAAyB,sBAAsB,MAAM,QAAQ,cAAc,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AAAA,EACtK;AAAA,EACA,UAAU;AACd;;;AC/BO,SAAS,gBAAgB,mBAAmB;AAC/C,SAAO,oBAAoB,uBAAuB,iBAAiB,IAAI;AAC3E;AACA,SAAS,uBAAuB,mBAAmB;AAC/C,SAAO,IAAI,WAAW,SAAU,YAAY;AACxC,QAAI,WAAW,qBAAqB;AACpC,QAAI,QAAQ,SAAS,IAAI;AACzB,QAAI,KAAK;AACT,QAAI,MAAM,WAAY;AAClB,UAAI,CAAC,WAAW,QAAQ;AACpB,aAAK,uBAAuB,sBAAsB,SAAUC,YAAW;AACnE,eAAK;AACL,cAAI,MAAM,SAAS,IAAI;AACvB,qBAAW,KAAK;AAAA,YACZ,WAAW,oBAAoB,MAAMA;AAAA,YACrC,SAAS,MAAM;AAAA,UACnB,CAAC;AACD,cAAI;AAAA,QACR,CAAC;AAAA,MACL;AAAA,IACJ;AACA,QAAI;AACJ,WAAO,WAAY;AACf,UAAI,IAAI;AACJ,+BAAuB,qBAAqB,EAAE;AAAA,MAClD;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AACA,IAAI,2BAA2B,uBAAuB;;;AChCtD,IAAI,aAAa;AACjB,IAAI;AACJ,IAAI,gBAAgB,CAAC;AACrB,SAAS,mBAAmB,QAAQ;AAChC,MAAI,UAAU,eAAe;AACzB,WAAO,cAAc,MAAM;AAC3B,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACO,IAAI,YAAY;AAAA,EACnB,cAAc,SAAU,IAAI;AACxB,QAAI,SAAS;AACb,kBAAc,MAAM,IAAI;AACxB,QAAI,CAAC,UAAU;AACX,iBAAW,QAAQ,QAAQ;AAAA,IAC/B;AACA,aAAS,KAAK,WAAY;AAAE,aAAO,mBAAmB,MAAM,KAAK,GAAG;AAAA,IAAG,CAAC;AACxE,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,SAAU,QAAQ;AAC9B,uBAAmB,MAAM;AAAA,EAC7B;AACJ;;;ACrBA,IAAI,eAAe,UAAU;AAA7B,IAA2C,iBAAiB,UAAU;AAC/D,IAAI,oBAAoB;AAAA,EAC3B,cAAc,WAAY;AACtB,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,QAAI,WAAW,kBAAkB;AACjC,aAAS,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,iBAAiB,cAAc,MAAM,QAAQ,cAAc,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AAAA,EACtJ;AAAA,EACA,gBAAgB,SAAU,QAAQ;AAC9B,QAAI,WAAW,kBAAkB;AACjC,aAAS,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,mBAAmB,gBAAgB,MAAM;AAAA,EACnH;AAAA,EACA,UAAU;AACd;;;ACdA,IAAI,aAAc,SAAU,QAAQ;AAChC,YAAUC,aAAY,MAAM;AAC5B,WAASA,YAAW,WAAW,MAAM;AACjC,QAAI,QAAQ,OAAO,KAAK,MAAM,WAAW,IAAI,KAAK;AAClD,UAAM,YAAY;AAClB,UAAM,OAAO;AACb,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,UAAU,iBAAiB,SAAU,WAAW,IAAIC,QAAO;AAClE,QAAIA,WAAU,QAAQ;AAAE,MAAAA,SAAQ;AAAA,IAAG;AACnC,QAAIA,WAAU,QAAQA,SAAQ,GAAG;AAC7B,aAAO,OAAO,UAAU,eAAe,KAAK,MAAM,WAAW,IAAIA,MAAK;AAAA,IAC1E;AACA,cAAU,QAAQ,KAAK,IAAI;AAC3B,WAAO,UAAU,eAAe,UAAU,aAAa,kBAAkB,aAAa,UAAU,MAAM,KAAK,WAAW,MAAS,CAAC;AAAA,EACpI;AACA,EAAAD,YAAW,UAAU,iBAAiB,SAAU,WAAW,IAAIC,QAAO;AAClE,QAAI;AACJ,QAAIA,WAAU,QAAQ;AAAE,MAAAA,SAAQ;AAAA,IAAG;AACnC,QAAIA,UAAS,OAAOA,SAAQ,IAAI,KAAK,QAAQ,GAAG;AAC5C,aAAO,OAAO,UAAU,eAAe,KAAK,MAAM,WAAW,IAAIA,MAAK;AAAA,IAC1E;AACA,QAAI,UAAU,UAAU;AACxB,QAAI,MAAM,UAAU,KAAK,QAAQ,QAAQ,SAAS,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,IAAI;AACtG,wBAAkB,eAAe,EAAE;AACnC,UAAI,UAAU,eAAe,IAAI;AAC7B,kBAAU,aAAa;AAAA,MAC3B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,SAAOD;AACX,EAAE,WAAW;;;ACjCb,IAAI,gBAAiB,SAAU,QAAQ;AACnC,YAAUE,gBAAe,MAAM;AAC/B,WAASA,iBAAgB;AACrB,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,EAC/D;AACA,EAAAA,eAAc,UAAU,QAAQ,SAAU,QAAQ;AAC9C,SAAK,UAAU;AACf,QAAI,UAAU,KAAK;AACnB,SAAK,aAAa;AAClB,QAAI,UAAU,KAAK;AACnB,QAAI;AACJ,aAAS,UAAU,QAAQ,MAAM;AACjC,OAAG;AACC,UAAK,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,GAAI;AACtD;AAAA,MACJ;AAAA,IACJ,UAAU,SAAS,QAAQ,CAAC,MAAM,OAAO,OAAO,WAAW,QAAQ,MAAM;AACzE,SAAK,UAAU;AACf,QAAI,OAAO;AACP,cAAQ,SAAS,QAAQ,CAAC,MAAM,OAAO,OAAO,WAAW,QAAQ,MAAM,GAAG;AACtE,eAAO,YAAY;AAAA,MACvB;AACA,YAAM;AAAA,IACV;AAAA,EACJ;AACA,SAAOA;AACX,EAAE,cAAc;;;AC1BT,IAAI,gBAAgB,IAAI,cAAc,UAAU;AAChD,IAAI,OAAO;;;ACDlB,IAAI,cAAe,SAAU,QAAQ;AACjC,YAAUC,cAAa,MAAM;AAC7B,WAASA,aAAY,WAAW,MAAM;AAClC,QAAI,QAAQ,OAAO,KAAK,MAAM,WAAW,IAAI,KAAK;AAClD,UAAM,YAAY;AAClB,UAAM,OAAO;AACb,WAAO;AAAA,EACX;AACA,EAAAA,aAAY,UAAU,WAAW,SAAU,OAAOC,QAAO;AACrD,QAAIA,WAAU,QAAQ;AAAE,MAAAA,SAAQ;AAAA,IAAG;AACnC,QAAIA,SAAQ,GAAG;AACX,aAAO,OAAO,UAAU,SAAS,KAAK,MAAM,OAAOA,MAAK;AAAA,IAC5D;AACA,SAAK,QAAQA;AACb,SAAK,QAAQ;AACb,SAAK,UAAU,MAAM,IAAI;AACzB,WAAO;AAAA,EACX;AACA,EAAAD,aAAY,UAAU,UAAU,SAAU,OAAOC,QAAO;AACpD,WAAOA,SAAQ,KAAK,KAAK,SAAS,OAAO,UAAU,QAAQ,KAAK,MAAM,OAAOA,MAAK,IAAI,KAAK,SAAS,OAAOA,MAAK;AAAA,EACpH;AACA,EAAAD,aAAY,UAAU,iBAAiB,SAAU,WAAW,IAAIC,QAAO;AACnE,QAAIA,WAAU,QAAQ;AAAE,MAAAA,SAAQ;AAAA,IAAG;AACnC,QAAKA,UAAS,QAAQA,SAAQ,KAAOA,UAAS,QAAQ,KAAK,QAAQ,GAAI;AACnE,aAAO,OAAO,UAAU,eAAe,KAAK,MAAM,WAAW,IAAIA,MAAK;AAAA,IAC1E;AACA,cAAU,MAAM,IAAI;AACpB,WAAO;AAAA,EACX;AACA,SAAOD;AACX,EAAE,WAAW;;;AC9Bb,IAAI,iBAAkB,SAAU,QAAQ;AACpC,YAAUE,iBAAgB,MAAM;AAChC,WAASA,kBAAiB;AACtB,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,EAC/D;AACA,SAAOA;AACX,EAAE,cAAc;;;ACNT,IAAI,iBAAiB,IAAI,eAAe,WAAW;AACnD,IAAI,QAAQ;;;ACAnB,IAAI,uBAAwB,SAAU,QAAQ;AAC1C,YAAUC,uBAAsB,MAAM;AACtC,WAASA,sBAAqB,WAAW,MAAM;AAC3C,QAAI,QAAQ,OAAO,KAAK,MAAM,WAAW,IAAI,KAAK;AAClD,UAAM,YAAY;AAClB,UAAM,OAAO;AACb,WAAO;AAAA,EACX;AACA,EAAAA,sBAAqB,UAAU,iBAAiB,SAAU,WAAW,IAAIC,QAAO;AAC5E,QAAIA,WAAU,QAAQ;AAAE,MAAAA,SAAQ;AAAA,IAAG;AACnC,QAAIA,WAAU,QAAQA,SAAQ,GAAG;AAC7B,aAAO,OAAO,UAAU,eAAe,KAAK,MAAM,WAAW,IAAIA,MAAK;AAAA,IAC1E;AACA,cAAU,QAAQ,KAAK,IAAI;AAC3B,WAAO,UAAU,eAAe,UAAU,aAAa,uBAAuB,sBAAsB,WAAY;AAAE,aAAO,UAAU,MAAM,MAAS;AAAA,IAAG,CAAC;AAAA,EAC1J;AACA,EAAAD,sBAAqB,UAAU,iBAAiB,SAAU,WAAW,IAAIC,QAAO;AAC5E,QAAI;AACJ,QAAIA,WAAU,QAAQ;AAAE,MAAAA,SAAQ;AAAA,IAAG;AACnC,QAAIA,UAAS,OAAOA,SAAQ,IAAI,KAAK,QAAQ,GAAG;AAC5C,aAAO,OAAO,UAAU,eAAe,KAAK,MAAM,WAAW,IAAIA,MAAK;AAAA,IAC1E;AACA,QAAI,UAAU,UAAU;AACxB,QAAI,MAAM,QAAQ,OAAO,UAAU,gBAAgB,KAAK,QAAQ,QAAQ,SAAS,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,IAAI;AACrI,6BAAuB,qBAAqB,EAAE;AAC9C,gBAAU,aAAa;AAAA,IAC3B;AACA,WAAO;AAAA,EACX;AACA,SAAOD;AACX,EAAE,WAAW;;;AC/Bb,IAAI,0BAA2B,SAAU,QAAQ;AAC7C,YAAUE,0BAAyB,MAAM;AACzC,WAASA,2BAA0B;AAC/B,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAAA,EAC/D;AACA,EAAAA,yBAAwB,UAAU,QAAQ,SAAU,QAAQ;AACxD,SAAK,UAAU;AACf,QAAI;AACJ,QAAI,QAAQ;AACR,gBAAU,OAAO;AAAA,IACrB,OACK;AACD,gBAAU,KAAK;AACf,WAAK,aAAa;AAAA,IACtB;AACA,QAAI,UAAU,KAAK;AACnB,QAAI;AACJ,aAAS,UAAU,QAAQ,MAAM;AACjC,OAAG;AACC,UAAK,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,GAAI;AACtD;AAAA,MACJ;AAAA,IACJ,UAAU,SAAS,QAAQ,CAAC,MAAM,OAAO,OAAO,WAAW,QAAQ,MAAM;AACzE,SAAK,UAAU;AACf,QAAI,OAAO;AACP,cAAQ,SAAS,QAAQ,CAAC,MAAM,OAAO,OAAO,WAAW,QAAQ,MAAM,GAAG;AACtE,eAAO,YAAY;AAAA,MACvB;AACA,YAAM;AAAA,IACV;AAAA,EACJ;AACA,SAAOA;AACX,EAAE,cAAc;;;AChCT,IAAI,0BAA0B,IAAI,wBAAwB,oBAAoB;AAC9E,IAAI,iBAAiB;;;ACC5B,IAAI,uBAAwB,SAAU,QAAQ;AAC1C,YAAUC,uBAAsB,MAAM;AACtC,WAASA,sBAAqB,qBAAqB,WAAW;AAC1D,QAAI,wBAAwB,QAAQ;AAAE,4BAAsB;AAAA,IAAe;AAC3E,QAAI,cAAc,QAAQ;AAAE,kBAAY;AAAA,IAAU;AAClD,QAAI,QAAQ,OAAO,KAAK,MAAM,qBAAqB,WAAY;AAAE,aAAO,MAAM;AAAA,IAAO,CAAC,KAAK;AAC3F,UAAM,YAAY;AAClB,UAAM,QAAQ;AACd,UAAM,QAAQ;AACd,WAAO;AAAA,EACX;AACA,EAAAA,sBAAqB,UAAU,QAAQ,WAAY;AAC/C,QAAI,KAAK,MAAM,UAAU,GAAG,SAAS,YAAY,GAAG;AACpD,QAAI;AACJ,QAAI;AACJ,YAAQ,SAAS,QAAQ,CAAC,MAAM,OAAO,SAAS,WAAW;AACvD,cAAQ,MAAM;AACd,WAAK,QAAQ,OAAO;AACpB,UAAK,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,KAAK,GAAI;AACtD;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,OAAO;AACP,aAAQ,SAAS,QAAQ,MAAM,GAAI;AAC/B,eAAO,YAAY;AAAA,MACvB;AACA,YAAM;AAAA,IACV;AAAA,EACJ;AACA,EAAAA,sBAAqB,kBAAkB;AACvC,SAAOA;AACX,EAAE,cAAc;AAEhB,IAAI,gBAAiB,SAAU,QAAQ;AACnC,YAAUC,gBAAe,MAAM;AAC/B,WAASA,eAAc,WAAW,MAAM,OAAO;AAC3C,QAAI,UAAU,QAAQ;AAAE,cAAS,UAAU,SAAS;AAAA,IAAI;AACxD,QAAI,QAAQ,OAAO,KAAK,MAAM,WAAW,IAAI,KAAK;AAClD,UAAM,YAAY;AAClB,UAAM,OAAO;AACb,UAAM,QAAQ;AACd,UAAM,SAAS;AACf,UAAM,QAAQ,UAAU,QAAQ;AAChC,WAAO;AAAA,EACX;AACA,EAAAA,eAAc,UAAU,WAAW,SAAU,OAAOC,QAAO;AACvD,QAAIA,WAAU,QAAQ;AAAE,MAAAA,SAAQ;AAAA,IAAG;AACnC,QAAI,OAAO,SAASA,MAAK,GAAG;AACxB,UAAI,CAAC,KAAK,IAAI;AACV,eAAO,OAAO,UAAU,SAAS,KAAK,MAAM,OAAOA,MAAK;AAAA,MAC5D;AACA,WAAK,SAAS;AACd,UAAI,SAAS,IAAID,eAAc,KAAK,WAAW,KAAK,IAAI;AACxD,WAAK,IAAI,MAAM;AACf,aAAO,OAAO,SAAS,OAAOC,MAAK;AAAA,IACvC,OACK;AACD,aAAO,aAAa;AAAA,IACxB;AAAA,EACJ;AACA,EAAAD,eAAc,UAAU,iBAAiB,SAAU,WAAW,IAAIC,QAAO;AACrE,QAAIA,WAAU,QAAQ;AAAE,MAAAA,SAAQ;AAAA,IAAG;AACnC,SAAK,QAAQ,UAAU,QAAQA;AAC/B,QAAI,UAAU,UAAU;AACxB,YAAQ,KAAK,IAAI;AACjB,YAAQ,KAAKD,eAAc,WAAW;AACtC,WAAO;AAAA,EACX;AACA,EAAAA,eAAc,UAAU,iBAAiB,SAAU,WAAW,IAAIC,QAAO;AACrE,QAAIA,WAAU,QAAQ;AAAE,MAAAA,SAAQ;AAAA,IAAG;AACnC,WAAO;AAAA,EACX;AACA,EAAAD,eAAc,UAAU,WAAW,SAAU,OAAOC,QAAO;AACvD,QAAI,KAAK,WAAW,MAAM;AACtB,aAAO,OAAO,UAAU,SAAS,KAAK,MAAM,OAAOA,MAAK;AAAA,IAC5D;AAAA,EACJ;AACA,EAAAD,eAAc,cAAc,SAAU,GAAG,GAAG;AACxC,QAAI,EAAE,UAAU,EAAE,OAAO;AACrB,UAAI,EAAE,UAAU,EAAE,OAAO;AACrB,eAAO;AAAA,MACX,WACS,EAAE,QAAQ,EAAE,OAAO;AACxB,eAAO;AAAA,MACX,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ,WACS,EAAE,QAAQ,EAAE,OAAO;AACxB,aAAO;AAAA,IACX,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAOA;AACX,EAAE,WAAW;;;ACnGN,SAAS,aAAa,KAAK;AAC9B,SAAO,CAAC,CAAC,QAAQ,eAAe,cAAe,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,SAAS;AACnG;;;ACHO,SAAS,cAAc,QAAQE,SAAQ;AAC1C,MAAI,YAAY,OAAOA,YAAW;AAClC,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC1C,QAAI,YAAY;AAChB,QAAI;AACJ,WAAO,UAAU;AAAA,MACb,MAAM,SAAU,OAAO;AACnB,iBAAS;AACT,oBAAY;AAAA,MAChB;AAAA,MACA,OAAO;AAAA,MACP,UAAU,WAAY;AAClB,YAAI,WAAW;AACX,kBAAQ,MAAM;AAAA,QAClB,WACS,WAAW;AAChB,kBAAQA,QAAO,YAAY;AAAA,QAC/B,OACK;AACD,iBAAO,IAAI,WAAW,CAAC;AAAA,QAC3B;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;;;ACvBO,SAAS,eAAe,QAAQC,SAAQ;AAC3C,MAAI,YAAY,OAAOA,YAAW;AAClC,SAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC1C,QAAI,aAAa,IAAI,eAAe;AAAA,MAChC,MAAM,SAAU,OAAO;AACnB,gBAAQ,KAAK;AACb,mBAAW,YAAY;AAAA,MAC3B;AAAA,MACA,OAAO;AAAA,MACP,UAAU,WAAY;AAClB,YAAI,WAAW;AACX,kBAAQA,QAAO,YAAY;AAAA,QAC/B,OACK;AACD,iBAAO,IAAI,WAAW,CAAC;AAAA,QAC3B;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,WAAO,UAAU,UAAU;AAAA,EAC/B,CAAC;AACL;;;ACfO,SAAS,sBAAsB,aAAa,cAAc,gBAAgB,WAAW;AACxF,MAAI,gBAAgB;AAChB,QAAI,YAAY,cAAc,GAAG;AAC7B,kBAAY;AAAA,IAChB,OACK;AACD,aAAO,WAAY;AACf,YAAI,OAAO,CAAC;AACZ,iBAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,eAAK,EAAE,IAAI,UAAU,EAAE;AAAA,QAC3B;AACA,eAAO,sBAAsB,aAAa,cAAc,SAAS,EAC5D,MAAM,MAAM,IAAI,EAChB,KAAK,iBAAiB,cAAc,CAAC;AAAA,MAC9C;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,WAAW;AACX,WAAO,WAAY;AACf,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,sBAAsB,aAAa,YAAY,EACjD,MAAM,MAAM,IAAI,EAChB,KAAK,YAAY,SAAS,GAAG,UAAU,SAAS,CAAC;AAAA,IAC1D;AAAA,EACJ;AACA,SAAO,WAAY;AACf,QAAI,QAAQ;AACZ,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,QAAI,UAAU,IAAI,aAAa;AAC/B,QAAI,gBAAgB;AACpB,WAAO,IAAI,WAAW,SAAU,YAAY;AACxC,UAAI,OAAO,QAAQ,UAAU,UAAU;AACvC,UAAI,eAAe;AACf,wBAAgB;AAChB,YAAI,YAAY;AAChB,YAAI,eAAe;AACnB,qBAAa,MAAM,OAAO,cAAc,cAAc,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG;AAAA,UACrE,WAAY;AACR,gBAAI,UAAU,CAAC;AACf,qBAASC,MAAK,GAAGA,MAAK,UAAU,QAAQA,OAAM;AAC1C,sBAAQA,GAAE,IAAI,UAAUA,GAAE;AAAA,YAC9B;AACA,gBAAI,aAAa;AACb,kBAAI,MAAM,QAAQ,MAAM;AACxB,kBAAI,OAAO,MAAM;AACb,wBAAQ,MAAM,GAAG;AACjB;AAAA,cACJ;AAAA,YACJ;AACA,oBAAQ,KAAK,IAAI,QAAQ,SAAS,UAAU,QAAQ,CAAC,CAAC;AACtD,2BAAe;AACf,gBAAI,WAAW;AACX,sBAAQ,SAAS;AAAA,YACrB;AAAA,UACJ;AAAA,QACJ,CAAC,CAAC;AACF,YAAI,cAAc;AACd,kBAAQ,SAAS;AAAA,QACrB;AACA,oBAAY;AAAA,MAChB;AACA,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AACJ;;;AC5EO,SAAS,aAAa,cAAc,gBAAgB,WAAW;AAClE,SAAO,sBAAsB,OAAO,cAAc,gBAAgB,SAAS;AAC/E;;;ACFO,SAAS,iBAAiB,cAAc,gBAAgB,WAAW;AACtE,SAAO,sBAAsB,MAAM,cAAc,gBAAgB,SAAS;AAC9E;;;ACDO,SAAS,MAAM,mBAAmB;AACrC,SAAO,IAAI,WAAW,SAAU,YAAY;AACxC,cAAU,kBAAkB,CAAC,EAAE,UAAU,UAAU;AAAA,EACvD,CAAC;AACL;;;ACHA,IAAI,iBAAiB;AAAA,EACjB,WAAW,WAAY;AAAE,WAAO,IAAI,QAAQ;AAAA,EAAG;AAAA,EAC/C,mBAAmB;AACvB;AACO,SAAS,YAAY,QAAQC,SAAQ;AACxC,MAAIA,YAAW,QAAQ;AAAE,IAAAA,UAAS;AAAA,EAAgB;AAClD,MAAI,aAAa;AACjB,MAAI,YAAYA,QAAO,WAAW,KAAKA,QAAO,mBAAmB,oBAAoB,OAAO,SAAS,OAAO;AAC5G,MAAI,UAAU,UAAU;AACxB,MAAI,SAAS,IAAI,WAAW,SAAU,YAAY;AAC9C,WAAO,QAAQ,UAAU,UAAU;AAAA,EACvC,CAAC;AACD,SAAO,UAAU,WAAY;AACzB,QAAI,CAAC,cAAc,WAAW,QAAQ;AAClC,mBAAa,MAAM,WAAY;AAAE,eAAO;AAAA,MAAQ,CAAC,EAAE,UAAU,OAAO;AACpE,UAAI,mBAAmB;AACnB,mBAAW,IAAI,WAAY;AAAE,iBAAQ,UAAU,UAAU;AAAA,QAAI,CAAC;AAAA,MAClE;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;AClBO,SAAS,WAAW;AACvB,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC3B;AACA,MAAI,iBAAiB,kBAAkB,IAAI;AAC3C,MAAI,KAAK,qBAAqB,IAAI,GAAG,UAAU,GAAG,MAAM,OAAO,GAAG;AAClE,MAAI,SAAS,IAAI,WAAW,SAAU,YAAY;AAC9C,QAAI,SAAS,QAAQ;AACrB,QAAI,CAAC,QAAQ;AACT,iBAAW,SAAS;AACpB;AAAA,IACJ;AACA,QAAI,SAAS,IAAI,MAAM,MAAM;AAC7B,QAAI,uBAAuB;AAC3B,QAAI,qBAAqB;AACzB,QAAI,UAAU,SAAUC,cAAa;AACjC,UAAI,WAAW;AACf,gBAAU,QAAQA,YAAW,CAAC,EAAE,UAAU,yBAAyB,YAAY,SAAU,OAAO;AAC5F,YAAI,CAAC,UAAU;AACX,qBAAW;AACX;AAAA,QACJ;AACA,eAAOA,YAAW,IAAI;AAAA,MAC1B,GAAG,WAAY;AAAE,eAAO;AAAA,MAAwB,GAAG,QAAW,WAAY;AACtE,YAAI,CAAC,wBAAwB,CAAC,UAAU;AACpC,cAAI,CAAC,oBAAoB;AACrB,uBAAW,KAAK,OAAO,aAAa,MAAM,MAAM,IAAI,MAAM;AAAA,UAC9D;AACA,qBAAW,SAAS;AAAA,QACxB;AAAA,MACJ,CAAC,CAAC;AAAA,IACN;AACA,aAAS,cAAc,GAAG,cAAc,QAAQ,eAAe;AAC3D,cAAQ,WAAW;AAAA,IACvB;AAAA,EACJ,CAAC;AACD,SAAO,iBAAiB,OAAO,KAAK,iBAAiB,cAAc,CAAC,IAAI;AAC5E;;;ACtCA,IAAI,0BAA0B,CAAC,eAAe,gBAAgB;AAC9D,IAAI,qBAAqB,CAAC,oBAAoB,qBAAqB;AACnE,IAAI,gBAAgB,CAAC,MAAM,KAAK;AACzB,SAAS,UAAU,QAAQ,WAAW,SAAS,gBAAgB;AAClE,MAAI,WAAW,OAAO,GAAG;AACrB,qBAAiB;AACjB,cAAU;AAAA,EACd;AACA,MAAI,gBAAgB;AAChB,WAAO,UAAU,QAAQ,WAAW,OAAO,EAAE,KAAK,iBAAiB,cAAc,CAAC;AAAA,EACtF;AACA,MAAI,KAAK,OAAO,cAAc,MAAM,IAC9B,mBAAmB,IAAI,SAAU,YAAY;AAAE,WAAO,SAAU,SAAS;AAAE,aAAO,OAAO,UAAU,EAAE,WAAW,SAAS,OAAO;AAAA,IAAG;AAAA,EAAG,CAAC,IAErI,wBAAwB,MAAM,IACxB,wBAAwB,IAAI,wBAAwB,QAAQ,SAAS,CAAC,IACtE,0BAA0B,MAAM,IAC5B,cAAc,IAAI,wBAAwB,QAAQ,SAAS,CAAC,IAC5D,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC;AACpD,MAAI,CAAC,KAAK;AACN,QAAI,YAAY,MAAM,GAAG;AACrB,aAAO,SAAS,SAAU,WAAW;AAAE,eAAO,UAAU,WAAW,WAAW,OAAO;AAAA,MAAG,CAAC,EAAE,UAAU,MAAM,CAAC;AAAA,IAChH;AAAA,EACJ;AACA,MAAI,CAAC,KAAK;AACN,UAAM,IAAI,UAAU,sBAAsB;AAAA,EAC9C;AACA,SAAO,IAAI,WAAW,SAAU,YAAY;AACxC,QAAI,UAAU,WAAY;AACtB,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,WAAW,KAAK,IAAI,KAAK,SAAS,OAAO,KAAK,CAAC,CAAC;AAAA,IAC3D;AACA,QAAI,OAAO;AACX,WAAO,WAAY;AAAE,aAAO,OAAO,OAAO;AAAA,IAAG;AAAA,EACjD,CAAC;AACL;AACA,SAAS,wBAAwB,QAAQ,WAAW;AAChD,SAAO,SAAU,YAAY;AAAE,WAAO,SAAU,SAAS;AAAE,aAAO,OAAO,UAAU,EAAE,WAAW,OAAO;AAAA,IAAG;AAAA,EAAG;AACjH;AACA,SAAS,wBAAwB,QAAQ;AACrC,SAAO,WAAW,OAAO,WAAW,KAAK,WAAW,OAAO,cAAc;AAC7E;AACA,SAAS,0BAA0B,QAAQ;AACvC,SAAO,WAAW,OAAO,EAAE,KAAK,WAAW,OAAO,GAAG;AACzD;AACA,SAAS,cAAc,QAAQ;AAC3B,SAAO,WAAW,OAAO,gBAAgB,KAAK,WAAW,OAAO,mBAAmB;AACvF;;;ACtDO,SAAS,iBAAiB,YAAY,eAAe,gBAAgB;AACxE,MAAI,gBAAgB;AAChB,WAAO,iBAAiB,YAAY,aAAa,EAAE,KAAK,iBAAiB,cAAc,CAAC;AAAA,EAC5F;AACA,SAAO,IAAI,WAAW,SAAU,YAAY;AACxC,QAAI,UAAU,WAAY;AACtB,UAAI,IAAI,CAAC;AACT,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,UAAE,EAAE,IAAI,UAAU,EAAE;AAAA,MACxB;AACA,aAAO,WAAW,KAAK,EAAE,WAAW,IAAI,EAAE,CAAC,IAAI,CAAC;AAAA,IACpD;AACA,QAAI,WAAW,WAAW,OAAO;AACjC,WAAO,WAAW,aAAa,IAAI,WAAY;AAAE,aAAO,cAAc,SAAS,QAAQ;AAAA,IAAG,IAAI;AAAA,EAClG,CAAC;AACL;;;ACbO,SAAS,SAAS,uBAAuB,WAAW,SAAS,2BAA2B,WAAW;AACtG,MAAI,IAAI;AACR,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU,WAAW,GAAG;AACxB,IAAC,KAAK,uBAAuB,eAAe,GAAG,cAAc,YAAY,GAAG,WAAW,UAAU,GAAG,SAAS,KAAK,GAAG,gBAAgB,iBAAiB,OAAO,SAAS,WAAW,IAAI,YAAY,GAAG;AAAA,EACxM,OACK;AACD,mBAAe;AACf,QAAI,CAAC,6BAA6B,YAAY,yBAAyB,GAAG;AACtE,uBAAiB;AACjB,kBAAY;AAAA,IAChB,OACK;AACD,uBAAiB;AAAA,IACrB;AAAA,EACJ;AACA,WAAS,MAAM;AACX,QAAI;AACJ,WAAO,YAAY,MAAM,SAAUC,KAAI;AACnC,cAAQA,IAAG,OAAO;AAAA,QACd,KAAK;AACD,kBAAQ;AACR,UAAAA,IAAG,QAAQ;AAAA,QACf,KAAK;AACD,cAAI,EAAE,CAAC,aAAa,UAAU,KAAK,GAAI,QAAO,CAAC,GAAG,CAAC;AACnD,iBAAO,CAAC,GAAG,eAAe,KAAK,CAAC;AAAA,QACpC,KAAK;AACD,UAAAA,IAAG,KAAK;AACR,UAAAA,IAAG,QAAQ;AAAA,QACf,KAAK;AACD,kBAAQ,QAAQ,KAAK;AACrB,iBAAO,CAAC,GAAG,CAAC;AAAA,QAChB,KAAK;AAAG,iBAAO,CAAC,CAAC;AAAA,MACrB;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO,MAAO,YAEN,WAAY;AAAE,WAAO,iBAAiB,IAAI,GAAG,SAAS;AAAA,EAAG,IAEzD,GAAI;AAChB;;;AC9CO,SAAS,IAAI,WAAW,YAAY,aAAa;AACpD,SAAO,MAAM,WAAY;AAAE,WAAQ,UAAU,IAAI,aAAa;AAAA,EAAc,CAAC;AACjF;;;ACEO,SAAS,QAAQ;AACpB,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,EAAE,IAAI,UAAU,EAAE;AAAA,EAC3B;AACA,MAAI,YAAY,aAAa,IAAI;AACjC,MAAI,aAAa,UAAU,MAAM,QAAQ;AACzC,MAAI,UAAU;AACd,SAAO,CAAC,QAAQ,SAER,QACF,QAAQ,WAAW,IAEb,UAAU,QAAQ,CAAC,CAAC,IAEpB,SAAS,UAAU,EAAE,KAAK,SAAS,SAAS,CAAC;AAC7D;;;ACnBO,IAAI,QAAQ,IAAI,WAAW,IAAI;AAC/B,SAAS,QAAQ;AACpB,SAAO;AACX;;;ACJO,SAAS,MAAM,KAAK,WAAW;AAClC,SAAO,KAAK,OAAO,QAAQ,GAAG,GAAG,SAAS;AAC9C;;;ACAO,SAAS,UAAU,QAAQ,WAAW,SAAS;AAClD,SAAO,CAAC,OAAO,WAAW,OAAO,EAAE,UAAU,MAAM,CAAC,GAAG,OAAO,IAAI,WAAW,OAAO,CAAC,EAAE,UAAU,MAAM,CAAC,CAAC;AAC7G;;;ACHO,SAAS,MAAM,OAAOC,QAAO,WAAW;AAC3C,MAAIA,UAAS,MAAM;AACf,IAAAA,SAAQ;AACR,YAAQ;AAAA,EACZ;AACA,MAAIA,UAAS,GAAG;AACZ,WAAO;AAAA,EACX;AACA,MAAI,MAAMA,SAAQ;AAClB,SAAO,IAAI,WAAW,YAEd,SAAU,YAAY;AAClB,QAAI,IAAI;AACR,WAAO,UAAU,SAAS,WAAY;AAClC,UAAI,IAAI,KAAK;AACT,mBAAW,KAAK,GAAG;AACnB,aAAK,SAAS;AAAA,MAClB,OACK;AACD,mBAAW,SAAS;AAAA,MACxB;AAAA,IACJ,CAAC;AAAA,EACL,IAEA,SAAU,YAAY;AAClB,QAAI,IAAI;AACR,WAAO,IAAI,OAAO,CAAC,WAAW,QAAQ;AAClC,iBAAW,KAAK,GAAG;AAAA,IACvB;AACA,eAAW,SAAS;AAAA,EACxB,CAAC;AACb;;;AC9BO,SAAS,MAAM,iBAAiB,mBAAmB;AACtD,SAAO,IAAI,WAAW,SAAU,YAAY;AACxC,QAAI,WAAW,gBAAgB;AAC/B,QAAI,SAAS,kBAAkB,QAAQ;AACvC,QAAI,SAAS,SAAS,UAAU,MAAM,IAAI;AAC1C,WAAO,UAAU,UAAU;AAC3B,WAAO,WAAY;AACf,UAAI,UAAU;AACV,iBAAS,YAAY;AAAA,MACzB;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;", "names": ["timestamp", "timestamp", "AsapAction", "delay", "AsapScheduler", "QueueAction", "delay", "QueueScheduler", "AnimationFrameAction", "delay", "AnimationFrameScheduler", "VirtualTimeScheduler", "VirtualAction", "delay", "config", "config", "_i", "config", "sourceIndex", "_a", "count"]}