// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import {Create as $Create} from "@wailsio/runtime";

export class Config {
    "DebugMode": boolean;
    "ProfileFilePath": string;
    "ResyncFilePath": string;
    "RcloneFilePath": string;

    /** Creates a new Config instance. */
    constructor($$source: Partial<Config> = {}) {
        if (!("DebugMode" in $$source)) {
            this["DebugMode"] = false;
        }
        if (!("ProfileFilePath" in $$source)) {
            this["ProfileFilePath"] = "";
        }
        if (!("ResyncFilePath" in $$source)) {
            this["ResyncFilePath"] = "";
        }
        if (!("RcloneFilePath" in $$source)) {
            this["RcloneFilePath"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new Config instance from a string or object.
     */
    static createFrom($$source: any = {}): Config {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new Config($$parsedSource as Partial<Config>);
    }
}
