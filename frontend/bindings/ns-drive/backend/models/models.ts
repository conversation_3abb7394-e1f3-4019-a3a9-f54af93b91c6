// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import {Create as $Create} from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as config$0 from "../config/models.js";

export class ConfigInfo {
    "working_dir": string;
    "selected_profile_index": number;
    "profiles": Profiles;
    "env_config": config$0.Config;

    /** Creates a new ConfigInfo instance. */
    constructor($$source: Partial<ConfigInfo> = {}) {
        if (!("working_dir" in $$source)) {
            this["working_dir"] = "";
        }
        if (!("selected_profile_index" in $$source)) {
            this["selected_profile_index"] = 0;
        }
        if (!("profiles" in $$source)) {
            this["profiles"] = ([] as Profiles);
        }
        if (!("env_config" in $$source)) {
            this["env_config"] = (new config$0.Config());
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new ConfigInfo instance from a string or object.
     */
    static createFrom($$source: any = {}): ConfigInfo {
        const $$createField2_0 = $$createType0;
        const $$createField3_0 = $$createType3;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("profiles" in $$parsedSource) {
            $$parsedSource["profiles"] = $$createField2_0($$parsedSource["profiles"]);
        }
        if ("env_config" in $$parsedSource) {
            $$parsedSource["env_config"] = $$createField3_0($$parsedSource["env_config"]);
        }
        return new ConfigInfo($$parsedSource as Partial<ConfigInfo>);
    }
}

export class Profile {
    "name": string;
    "from": string;
    "to": string;
    "included_paths": string[];
    "excluded_paths": string[];
    "bandwidth": number;
    "parallel": number;
    "backup_path": string;
    "cache_path": string;

    /** Creates a new Profile instance. */
    constructor($$source: Partial<Profile> = {}) {
        if (!("name" in $$source)) {
            this["name"] = "";
        }
        if (!("from" in $$source)) {
            this["from"] = "";
        }
        if (!("to" in $$source)) {
            this["to"] = "";
        }
        if (!("included_paths" in $$source)) {
            this["included_paths"] = [];
        }
        if (!("excluded_paths" in $$source)) {
            this["excluded_paths"] = [];
        }
        if (!("bandwidth" in $$source)) {
            this["bandwidth"] = 0;
        }
        if (!("parallel" in $$source)) {
            this["parallel"] = 0;
        }
        if (!("backup_path" in $$source)) {
            this["backup_path"] = "";
        }
        if (!("cache_path" in $$source)) {
            this["cache_path"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new Profile instance from a string or object.
     */
    static createFrom($$source: any = {}): Profile {
        const $$createField3_0 = $$createType4;
        const $$createField4_0 = $$createType4;
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        if ("included_paths" in $$parsedSource) {
            $$parsedSource["included_paths"] = $$createField3_0($$parsedSource["included_paths"]);
        }
        if ("excluded_paths" in $$parsedSource) {
            $$parsedSource["excluded_paths"] = $$createField4_0($$parsedSource["excluded_paths"]);
        }
        return new Profile($$parsedSource as Partial<Profile>);
    }
}

export type Profiles = Profile[];

// Private type creation functions
var $$createType0 = (function $$initCreateType0(...args): any {
    if ($$createType0 === $$initCreateType0) {
        $$createType0 = $$createType2;
    }
    return $$createType0(...args);
});
const $$createType1 = Profile.createFrom;
const $$createType2 = $Create.Array($$createType1);
const $$createType3 = config$0.Config.createFrom;
const $$createType4 = $Create.Array($Create.Any);
