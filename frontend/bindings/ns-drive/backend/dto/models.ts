// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import {Create as $Create} from "@wailsio/runtime";

export class AppError {
    "message": string;

    /** Creates a new AppError instance. */
    constructor($$source: Partial<AppError> = {}) {
        if (!("message" in $$source)) {
            this["message"] = "";
        }

        Object.assign(this, $$source);
    }

    /**
     * Creates a new AppError instance from a string or object.
     */
    static createFrom($$source: any = {}): AppError {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new AppError($$parsedSource as Partial<AppError>);
    }
}
