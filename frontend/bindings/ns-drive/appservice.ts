// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * AppService wraps the backend App to implement the Wails v3 service interface
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import {Call as $Call, Create as $Create} from "@wailsio/runtime";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as application$0 from "../github.com/wailsapp/wails/v3/pkg/application/models.js";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as dto$0 from "./backend/dto/models.js";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import * as models$0 from "./backend/models/models.js";

export function AddRemote(remoteName: string, remoteType: string, remoteConfig: { [_: string]: string }): Promise<dto$0.AppError | null> & { cancel(): void } {
    let $resultPromise = $Call.ByID(2277393767, remoteName, remoteType, remoteConfig) as any;
    let $typingPromise = $resultPromise.then(($result: any) => {
        return $$createType1($result);
    }) as any;
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

export function DeleteRemote(name: string): Promise<void> & { cancel(): void } {
    let $resultPromise = $Call.ByID(2918485859, name) as any;
    return $resultPromise;
}

export function GetConfigInfo(): Promise<models$0.ConfigInfo> & { cancel(): void } {
    let $resultPromise = $Call.ByID(3026244572) as any;
    let $typingPromise = $resultPromise.then(($result: any) => {
        return $$createType2($result);
    }) as any;
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

export function GetProfile(id: string): Promise<models$0.Profile> & { cancel(): void } {
    let $resultPromise = $Call.ByID(3217990731, id) as any;
    let $typingPromise = $resultPromise.then(($result: any) => {
        return $$createType3($result);
    }) as any;
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

export function GetProfiles(): Promise<models$0.Profile[]> & { cancel(): void } {
    let $resultPromise = $Call.ByID(709657640) as any;
    let $typingPromise = $resultPromise.then(($result: any) => {
        return $$createType4($result);
    }) as any;
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

export function GetRemotes(): Promise<any> & { cancel(): void } {
    let $resultPromise = $Call.ByID(2087566501) as any;
    return $resultPromise;
}

/**
 * ServiceStartup is called when the service starts
 */
export function ServiceStartup(options: application$0.ServiceOptions): Promise<void> & { cancel(): void } {
    let $resultPromise = $Call.ByID(480989366, options) as any;
    return $resultPromise;
}

/**
 * SetApp sets the application reference for events
 */
export function SetApp(app: application$0.App | null): Promise<void> & { cancel(): void } {
    let $resultPromise = $Call.ByID(3487267257, app) as any;
    return $resultPromise;
}

export function StopCommand(id: number): Promise<void> & { cancel(): void } {
    let $resultPromise = $Call.ByID(4133508531, id) as any;
    return $resultPromise;
}

export function Sync(task: string, profile: models$0.Profile): Promise<number> & { cancel(): void } {
    let $resultPromise = $Call.ByID(198789601, task, profile) as any;
    return $resultPromise;
}

export function SyncWithTab(task: string, profile: models$0.Profile, tabId: string): Promise<number> & { cancel(): void } {
    let $resultPromise = $Call.ByID(1460828732, task, profile, tabId) as any;
    return $resultPromise;
}

export function UpdateProfiles(profiles: models$0.Profiles): Promise<dto$0.AppError | null> & { cancel(): void } {
    let $resultPromise = $Call.ByID(2143894669, profiles) as any;
    let $typingPromise = $resultPromise.then(($result: any) => {
        return $$createType1($result);
    }) as any;
    $typingPromise.cancel = $resultPromise.cancel.bind($resultPromise);
    return $typingPromise;
}

// Private type creation functions
const $$createType0 = dto$0.AppError.createFrom;
const $$createType1 = $Create.Nullable($$createType0);
const $$createType2 = models$0.ConfigInfo.createFrom;
const $$createType3 = models$0.Profile.createFrom;
const $$createType4 = $Create.Array($$createType3);
