// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import {Create as $Create} from "@wailsio/runtime";

/**
 * A Logger records structured information about each call to its
 * Log, Debug, Info, Warn, and Error methods.
 * For each call, it creates a [Record] and passes it to a [Handler].
 * 
 * To create a new Logger, call [New] or a Logger method
 * that begins "With".
 */
export class Logger {

    /** Creates a new Logger instance. */
    constructor($$source: Partial<Logger> = {}) {

        Object.assign(this, $$source);
    }

    /**
     * Creates a new Logger instance from a string or object.
     */
    static createFrom($$source: any = {}): Logger {
        let $$parsedSource = typeof $$source === 'string' ? JSON.parse($$source) : $$source;
        return new Logger($$parsedSource as Partial<Logger>);
    }
}
