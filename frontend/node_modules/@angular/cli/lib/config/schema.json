{"$schema": "http://json-schema.org/draft-07/schema", "$id": "ng-cli://config/schema.json", "title": "Angular CLI Workspace Configuration", "type": "object", "properties": {"$schema": {"type": "string"}, "version": {"$ref": "#/definitions/fileVersion"}, "cli": {"$ref": "#/definitions/cliOptions"}, "schematics": {"$ref": "#/definitions/schematicOptions"}, "newProjectRoot": {"type": "string", "description": "Path where new projects will be created."}, "projects": {"type": "object", "patternProperties": {"^(?:@[a-zA-Z0-9._-]+/)?[a-zA-Z0-9._-]+$": {"$ref": "#/definitions/project"}}, "additionalProperties": false}}, "additionalProperties": false, "required": ["version"], "definitions": {"cliOptions": {"type": "object", "properties": {"schematicCollections": {"type": "array", "description": "The list of schematic collections to use.", "items": {"type": "string", "uniqueItems": true}}, "packageManager": {"description": "Specify which package manager tool to use.", "type": "string", "enum": ["npm", "cnpm", "yarn", "pnpm", "bun"]}, "warnings": {"description": "Control CLI specific console warnings", "type": "object", "properties": {"versionMismatch": {"description": "Show a warning when the global version is newer than the local one.", "type": "boolean"}}, "additionalProperties": false}, "analytics": {"type": ["boolean", "string"], "description": "Share pseudonymous usage data with the Angular Team at Google."}, "cache": {"description": "Control disk cache.", "type": "object", "properties": {"environment": {"description": "Configure in which environment disk cache is enabled.", "type": "string", "enum": ["local", "ci", "all"]}, "enabled": {"description": "Configure whether disk caching is enabled.", "type": "boolean"}, "path": {"description": "Cache base path.", "type": "string"}}, "additionalProperties": false}}, "additionalProperties": false}, "cliGlobalOptions": {"type": "object", "properties": {"schematicCollections": {"type": "array", "description": "The list of schematic collections to use.", "items": {"type": "string", "uniqueItems": true}}, "packageManager": {"description": "Specify which package manager tool to use.", "type": "string", "enum": ["npm", "cnpm", "yarn", "pnpm", "bun"]}, "warnings": {"description": "Control CLI specific console warnings", "type": "object", "properties": {"versionMismatch": {"description": "Show a warning when the global version is newer than the local one.", "type": "boolean"}}, "additionalProperties": false}, "analytics": {"type": ["boolean", "string"], "description": "Share pseudonymous usage data with the Angular Team at Google."}, "completion": {"type": "object", "description": "Angular CLI completion settings.", "properties": {"prompted": {"type": "boolean", "description": "Whether the user has been prompted to add completion command prompt."}}, "additionalProperties": false}}, "additionalProperties": false}, "schematicOptions": {"type": "object", "properties": {"@schematics/angular:application": {"$ref": "#/definitions/SchematicsAngularApplicationSchema"}, "@schematics/angular:class": {"$ref": "#/definitions/SchematicsAngularClassSchema"}, "@schematics/angular:component": {"$ref": "#/definitions/SchematicsAngularComponentSchema"}, "@schematics/angular:directive": {"$ref": "#/definitions/SchematicsAngularDirectiveSchema"}, "@schematics/angular:enum": {"$ref": "#/definitions/SchematicsAngularEnumSchema"}, "@schematics/angular:guard": {"$ref": "#/definitions/SchematicsAngularGuardSchema"}, "@schematics/angular:interceptor": {"$ref": "#/definitions/SchematicsAngularInterceptorSchema"}, "@schematics/angular:interface": {"$ref": "#/definitions/SchematicsAngularInterfaceSchema"}, "@schematics/angular:library": {"$ref": "#/definitions/SchematicsAngularLibrarySchema"}, "@schematics/angular:pipe": {"$ref": "#/definitions/SchematicsAngularPipeSchema"}, "@schematics/angular:ng-new": {"$ref": "#/definitions/SchematicsAngularNgNewSchema"}, "@schematics/angular:resolver": {"$ref": "#/definitions/SchematicsAngularResolverSchema"}, "@schematics/angular:service": {"$ref": "#/definitions/SchematicsAngularServiceSchema"}, "@schematics/angular:web-worker": {"$ref": "#/definitions/SchematicsAngularWebWorkerSchema"}}, "additionalProperties": true}, "fileVersion": {"type": "integer", "description": "File format version", "minimum": 1}, "project": {"type": "object", "properties": {"cli": {"schematicCollections": {"type": "array", "description": "The list of schematic collections to use.", "items": {"type": "string", "uniqueItems": true}}}, "schematics": {"$ref": "#/definitions/schematicOptions"}, "prefix": {"type": "string", "format": "html-selector", "description": "The prefix to apply to generated selectors."}, "root": {"type": "string", "description": "Root of the project files."}, "i18n": {"$ref": "#/definitions/project/definitions/i18n"}, "sourceRoot": {"type": "string", "description": "The root of the source files, assets and index.html file structure."}, "projectType": {"type": "string", "description": "Project type.", "enum": ["application", "library"]}, "architect": {"type": "object", "additionalProperties": {"$ref": "#/definitions/project/definitions/target"}}, "targets": {"type": "object", "additionalProperties": {"$ref": "#/definitions/project/definitions/target"}}}, "required": ["root", "projectType"], "anyOf": [{"required": ["architect"], "not": {"required": ["targets"]}}, {"required": ["targets"], "not": {"required": ["architect"]}}, {"not": {"required": ["targets", "architect"]}}], "additionalProperties": false, "patternProperties": {"^[a-z]{1,3}-.*": {}}, "definitions": {"i18n": {"description": "Project i18n options", "type": "object", "properties": {"sourceLocale": {"oneOf": [{"type": "string", "description": "Specifies the source locale of the application.", "default": "en-US", "$comment": "IETF BCP 47 language tag (simplified)", "pattern": "^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$"}, {"type": "object", "description": "Localization options to use for the source locale.", "properties": {"code": {"type": "string", "description": "Specifies the locale code of the source locale.", "pattern": "^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$"}, "baseHref": {"type": "string", "description": "Specifies the HTML base HREF for the locale. Defaults to the locale code if not provided."}, "subPath": {"type": "string", "description": "Defines the subpath for accessing this locale. It serves as the HTML base HREF and the directory name for the output. Defaults to the locale code if not specified.", "pattern": "^[\\w-]*$"}}, "anyOf": [{"required": ["subPath"], "not": {"required": ["baseHref"]}}, {"required": ["baseHref"], "not": {"required": ["subPath"]}}, {"not": {"required": ["baseHref", "subPath"]}}], "additionalProperties": false}]}, "locales": {"type": "object", "additionalProperties": false, "patternProperties": {"^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$": {"oneOf": [{"type": "string", "description": "Localization file to use for i18n."}, {"type": "array", "description": "Localization files to use for i18n.", "items": {"type": "string", "uniqueItems": true}}, {"type": "object", "description": "Localization options to use for the locale.", "properties": {"translation": {"oneOf": [{"type": "string", "description": "Localization file to use for i18n."}, {"type": "array", "description": "Localization files to use for i18n.", "items": {"type": "string", "uniqueItems": true}}]}, "baseHref": {"type": "string", "description": "Specifies the HTML base HREF for the locale. Defaults to the locale code if not provided."}, "subPath": {"type": "string", "description": "Defines the URL segment for accessing this locale. It serves as the HTML base HREF and the directory name for the output. Defaults to the locale code if not specified.", "pattern": "^[\\w-]*$"}}, "anyOf": [{"required": ["subPath"], "not": {"required": ["baseHref"]}}, {"required": ["baseHref"], "not": {"required": ["subPath"]}}, {"not": {"required": ["baseHref", "subPath"]}}], "additionalProperties": false}]}}}}, "additionalProperties": false}, "target": {"oneOf": [{"$comment": "Extendable target with custom builder", "type": "object", "properties": {"builder": {"type": "string", "description": "The builder used for this package.", "not": {"enum": ["@angular/build:application", "@angular/build:dev-server", "@angular/build:extract-i18n", "@angular/build:karma", "@angular/build:ng-packagr", "@angular/build:unit-test", "@angular-devkit/build-angular:application", "@angular-devkit/build-angular:app-shell", "@angular-devkit/build-angular:browser", "@angular-devkit/build-angular:browser-esbuild", "@angular-devkit/build-angular:dev-server", "@angular-devkit/build-angular:extract-i18n", "@angular-devkit/build-angular:karma", "@angular-devkit/build-angular:ng-packagr", "@angular-devkit/build-angular:prerender", "@angular-devkit/build-angular:jest", "@angular-devkit/build-angular:web-test-runner", "@angular-devkit/build-angular:server", "@angular-devkit/build-angular:ssr-dev-server"]}}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"type": "object"}, "configurations": {"type": "object", "description": "A map of alternative target options.", "additionalProperties": {"type": "object"}}}, "additionalProperties": false, "required": ["builder"]}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular/build:application"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularBuildBuildersApplicationSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularBuildBuildersApplicationSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:application"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularBuildBuildersApplicationSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularBuildBuildersApplicationSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:app-shell"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersAppShellSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersAppShellSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:browser"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:browser-esbuild"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserEsbuildSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserEsbuildSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular/build:dev-server"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularBuildBuildersDevServerSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularBuildBuildersDevServerSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:dev-server"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersDevServerSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersDevServerSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular/build:extract-i18n"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularBuildBuildersExtractI18nSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularBuildBuildersExtractI18nSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:extract-i18n"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersExtractI18nSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersExtractI18nSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular/build:unit-test"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularBuildBuildersUnitTestSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularBuildBuildersUnitTestSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular/build:karma"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularBuildBuildersKarmaSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularBuildBuildersKarmaSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:karma"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersKarmaSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersKarmaSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:jest"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersJestSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersJestSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:web-test-runner"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersWebTestRunnerSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersWebTestRunnerSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:prerender"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersPrerenderSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersPrerenderSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:ssr-dev-server"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersSsrDevServerSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersSsrDevServerSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:server"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersServerSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersServerSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular-devkit/build-angular:ng-packagr"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersNgPackagrSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersNgPackagrSchema"}}}}, {"type": "object", "additionalProperties": false, "properties": {"builder": {"const": "@angular/build:ng-packagr"}, "defaultConfiguration": {"type": "string", "description": "A default named configuration to use when a target configuration is not provided."}, "options": {"$ref": "#/definitions/AngularBuildBuildersNgPackagrSchema"}, "configurations": {"type": "object", "additionalProperties": {"$ref": "#/definitions/AngularBuildBuildersNgPackagrSchema"}}}}]}}}, "global": {"type": "object", "properties": {"$schema": {"type": "string"}, "version": {"$ref": "#/definitions/fileVersion"}, "cli": {"$ref": "#/definitions/cliGlobalOptions"}, "schematics": {"$ref": "#/definitions/schematicOptions"}}, "required": ["version"]}, "SchematicsAngularApplicationSchema": {"title": "Angular Application Options Schema", "type": "object", "description": "Generates a new Angular application within your workspace. This schematic sets up the foundational structure of your project, including the root component, module, and configuration files. You can customize various aspects of the application, such as routing, styling, and testing.", "additionalProperties": false, "properties": {"projectRoot": {"description": "The directory where the new application's files will be created, relative to the workspace root. If not specified, the application will be created in a subfolder within the `projects` directory, using the application's name.", "type": "string"}, "name": {"description": "The name for the new application. This name will be used for the project directory and various identifiers throughout the application's code.", "type": "string", "pattern": "^(?:@[a-zA-Z0-9-*~][a-zA-Z0-9-*._~]*/)?[a-zA-Z0-9-~][a-zA-Z0-9-._~]*$", "$default": {"$source": "argv", "index": 0}}, "inlineStyle": {"description": "Include the styles for the root component directly within the `app.component.ts` file. Only CSS styles can be included inline. By default, a separate stylesheet file (e.g., `app.component.css`) is created.", "type": "boolean", "alias": "s"}, "inlineTemplate": {"description": "Include the HTML template for the root component directly within the `app.component.ts` file. By default, a separate template file (e.g., `app.component.html`) is created.", "type": "boolean", "alias": "t"}, "viewEncapsulation": {"description": "Sets the view encapsulation mode for the application's components. This determines how component styles are scoped and applied.", "enum": ["Emulated", "None", "ShadowDom"], "type": "string"}, "routing": {"type": "boolean", "description": "Generate an application with routing already configured. This sets up the necessary files and modules for managing navigation between different views in your application.", "default": true}, "prefix": {"type": "string", "format": "html-selector", "description": "A prefix to be added to the selectors of components generated within this application. For example, if the prefix is `my-app` and you generate a component named `my-component`, the selector will be `my-app-my-component`.", "default": "app", "alias": "p"}, "style": {"description": "The type of stylesheet files to be created for components in the application.", "type": "string", "default": "css", "enum": ["css", "scss", "sass", "less"]}, "skipTests": {"description": "Skip the generation of a unit test files `spec.ts`.", "type": "boolean", "default": false, "alias": "S"}, "skipPackageJson": {"type": "boolean", "default": false, "description": "Do not add dependencies to the `package.json` file."}, "minimal": {"description": "Generate a minimal project without any testing frameworks. This is intended for learning purposes and simple experimentation, not for production applications.", "type": "boolean", "default": false}, "skipInstall": {"description": "Skip the automatic installation of packages. You will need to manually install the dependencies later.", "type": "boolean", "default": false}, "strict": {"description": "Enable stricter bundle budget settings for the application. This helps to keep your application's bundle size small and improve performance. For more information, see https://angular.dev/tools/cli/template-typecheck#strict-mode", "type": "boolean", "default": true}, "standalone": {"description": "Create an application that utilizes the standalone API, eliminating the need for NgModules. This can simplify the structure of your application.", "type": "boolean", "default": true}, "ssr": {"description": "Configure the application for Server-Side Rendering (SSR) and Static Site Generation (SSG/Prerendering).", "type": "boolean", "default": false}, "zoneless": {"description": "Generate an application that does not use `zone.js`.", "type": "boolean", "default": false}}}, "SchematicsAngularClassSchema": {"title": "Angular Class Options Schema", "type": "object", "description": "Creates a new class in your project. Classes are the fundamental building blocks for object-oriented programming in TypeScript. They provide a blueprint for creating objects with properties and methods. This schematic helps you generate a new class with the basic structure and optional test files.", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name for the new class. This will be used to create the class file (e.g., `my-class.ts`) and, if enabled, the corresponding test file `my-class.spec.ts`.", "$default": {"$source": "argv", "index": 0}}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path where the class file should be created, relative to the workspace root. If not specified, the class will be created in the current directory.", "visible": false}, "project": {"type": "string", "description": "The name of the project where the class should be added. If not specified, the CLI will determine the project from the current directory.", "$default": {"$source": "projectName"}}, "skipTests": {"type": "boolean", "description": "Skip the generation of a unit test file `spec.ts` for the new class.", "default": false}, "type": {"type": "string", "description": "Adds a custom type to the filename, allowing you to create more descriptive class names. For example, if you set the type to `helper`, the filename will be `my-class.helper.ts`."}}}, "SchematicsAngularComponentSchema": {"title": "Angular Component Options Schema", "type": "object", "description": "Creates a new Angular component. Components are the basic building blocks of Angular applications. Each component consists of a TypeScript class, an HTML template, and an optional CSS stylesheet. Use this schematic to generate a new component in your project.", "additionalProperties": false, "properties": {"path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path where the component files should be created, relative to the current workspace. If not provided, a folder with the same name as the component will be created in the project's `src/app` directory.", "visible": false}, "project": {"type": "string", "description": "The name of the project where the component should be added. If not specified, the CLI will determine the project from the current directory.", "$default": {"$source": "projectName"}}, "name": {"type": "string", "description": "The name for the new component. This will be used to create the component's class, template, and stylesheet files. For example, if you provide `my-component`, the files will be named `my-component.ts`, `my-component.html`, and `my-component.css`.", "$default": {"$source": "argv", "index": 0}}, "displayBlock": {"description": "Adds `:host { display: block; }` to the component's stylesheet, ensuring the component renders as a block-level element. This is useful for layout purposes.", "type": "boolean", "default": false, "alias": "b"}, "inlineStyle": {"description": "Include the component's styles directly in the `component.ts` file. By default, a separate stylesheet file (e.g., `my-component.css`) is created.", "type": "boolean", "default": false, "alias": "s"}, "inlineTemplate": {"description": "Include the component's HTML template directly in the `component.ts` file. By default, a separate template file (e.g., `my-component.html`) is created.", "type": "boolean", "default": false, "alias": "t"}, "standalone": {"description": "Generate a standalone component. Standalone components are self-contained and don't need to be declared in an NgModule. They can be used independently or imported directly into other standalone components.", "type": "boolean", "default": true}, "viewEncapsulation": {"description": "Sets the view encapsulation mode for the component. This determines how the component's styles are scoped and applied.", "enum": ["Emulated", "None", "ShadowDom"], "type": "string", "alias": "v"}, "changeDetection": {"description": "Configures the change detection strategy for the component.", "enum": ["<PERSON><PERSON><PERSON>", "OnPush"], "type": "string", "default": "<PERSON><PERSON><PERSON>", "alias": "c"}, "prefix": {"type": "string", "description": "A prefix to be added to the component's selector. For example, if the prefix is `app` and the component name is `my-component`, the selector will be `app-my-component`.", "alias": "p", "oneOf": [{"maxLength": 0}, {"minLength": 1, "format": "html-selector"}]}, "style": {"description": "Specify the type of stylesheet to be created for the component, or `none` to skip creating a stylesheet.", "type": "string", "default": "css", "enum": ["css", "scss", "sass", "less", "none"]}, "type": {"type": "string", "description": "Append a custom type to the component's filename. For example, if you set the type to `container`, the file will be named `my-component.container.ts`."}, "skipTests": {"type": "boolean", "description": "Skip the generation of unit test files `spec.ts`.", "default": false}, "flat": {"type": "boolean", "description": "Create the component files directly in the project's `src/app` directory instead of creating a new folder for them.", "default": false}, "skipImport": {"type": "boolean", "description": "Do not automatically import the new component into its closest NgModule.", "default": false}, "selector": {"type": "string", "format": "html-selector", "description": "The HTML selector to use for this component. If not provided, a selector will be generated based on the component name (e.g., `app-my-component`)."}, "skipSelector": {"type": "boolean", "default": false, "description": "Skip the generation of an HTML selector for the component."}, "module": {"type": "string", "description": "Specify the NgModule where the component should be declared. If not provided, the CLI will attempt to find the closest NgModule in the component's path.", "alias": "m"}, "export": {"type": "boolean", "default": false, "description": "Automatically export the component from the specified NgModule, making it accessible to other modules in the application."}, "exportDefault": {"type": "boolean", "default": false, "description": "Use a default export for the component in its TypeScript file instead of a named export."}, "ngHtml": {"type": "boolean", "default": false, "description": "Generate component template files with an '.ng.html' file extension instead of '.html'."}}}, "SchematicsAngularDirectiveSchema": {"title": "Angular Directive Options Schema", "type": "object", "description": "Creates a new directive in your project. Directives are used to extend the behavior or appearance of HTML elements and components. They allow you to manipulate the DOM, add custom attributes, and respond to events. This schematic generates the necessary files and boilerplate code for a new directive.", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name for the new directive. This will be used to create the directive's class and spec files (e.g., `my-directive.directive.ts` and `my-directive.directive.spec.ts`).", "$default": {"$source": "argv", "index": 0}}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path where the directive files should be created, relative to the workspace root. If not provided, the directive will be created in the current directory.", "visible": false}, "project": {"type": "string", "description": "The name of the project where the directive should be added. If not specified, the CLI will determine the project from the current directory.", "$default": {"$source": "projectName"}}, "prefix": {"type": "string", "description": "A prefix to be added to the directive's selector. For example, if the prefix is `app` and the directive name is `highlight`, the selector will be `appHighlight`.", "alias": "p", "oneOf": [{"maxLength": 0}, {"minLength": 1, "format": "html-selector"}]}, "skipTests": {"type": "boolean", "description": "Skip the generation of a unit test file `spec.ts` for the new directive.", "default": false}, "skipImport": {"type": "boolean", "description": "Do not automatically import the new directive into its closest NgModule.", "default": false}, "selector": {"type": "string", "format": "html-selector", "description": "The HTML selector to use for this directive. If not provided, a selector will be generated based on the directive's name (e.g., `appHighlight`)."}, "standalone": {"description": "Generate a standalone directive. Standalone directives are self-contained and don't need to be declared in an NgModule. They can be used independently or imported directly into other standalone components or directives.", "type": "boolean", "default": true}, "flat": {"type": "boolean", "description": "Creates the new directive files at the top level of the current project. If set to false, a new folder with the directive's name will be created to contain the files.", "default": true}, "module": {"type": "string", "description": "Specify the NgModule where the directive should be declared. If not provided, the CLI will attempt to find the closest NgModule in the directive's path.", "alias": "m"}, "export": {"type": "boolean", "default": false, "description": "Automatically export the directive from the specified NgModule, making it accessible to other modules in the application."}, "type": {"type": "string", "description": "Append a custom type to the directive's filename. For example, if you set the type to `directive`, the file will be named `example.directive.ts`."}}}, "SchematicsAngularEnumSchema": {"title": "Angular Enum Options Schema", "type": "object", "description": "Creates a new enum in your project. Enums (enumerations) are a way to define a set of named constants, making your code more readable and maintainable. This schematic generates a new enum with the specified name and type.", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name for the new enum. This will be used to create the enum file (e.g., `my-enum.enum.ts`).", "$default": {"$source": "argv", "index": 0}}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path where the enum file should be created, relative to the current workspace. If not specified, the enum will be created in the current directory.", "visible": false}, "project": {"type": "string", "description": "The name of the project where the enum should be created. If not specified, the CLI will determine the project from the current directory.", "$default": {"$source": "projectName"}}, "type": {"type": "string", "description": "Adds a custom type to the filename, allowing you to create more descriptive enum names. For example, if you set the type to `status`, the filename will be `my-enum.status.ts`."}}}, "SchematicsAngularGuardSchema": {"title": "Angular Guard Options Schema", "type": "object", "description": "Creates a new route guard in your project. Route guards are used to control access to parts of your application by checking certain conditions before a route is activated. This schematic generates a new guard with the specified name, type, and options.", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name for the new route guard. This will be used to create the guard's class and spec files (e.g., `my-guard.guard.ts` and `my-guard.guard.spec.ts`).", "$default": {"$source": "argv", "index": 0}}, "skipTests": {"type": "boolean", "description": "Skip the generation of a unit test file `spec.ts` for the new guard.", "default": false}, "flat": {"type": "boolean", "description": "Creates the new guard files at the top level of the current project. If set to false, a new folder with the guard's name will be created to contain the files.", "default": true}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path where the guard files should be created, relative to the current workspace. If not provided, the guard will be created in the current directory.", "visible": false}, "project": {"type": "string", "description": "The name of the project where the guard should be created. If not specified, the CLI will determine the project from the current directory.", "$default": {"$source": "projectName"}}, "functional": {"type": "boolean", "description": "Generate the guard as a function instead of a class. Functional guards can be simpler for basic scenarios.", "default": true}, "implements": {"alias": "guardType", "type": "array", "description": "Specifies the type(s) of guard to create. You can choose one or more of the following: `CanActivate` (controls access to a route), `CanActivateChild` (controls access to child routes), `CanDeactivate` (asks for confirmation before leaving a route), `CanMatch` (determines if a route can be matched).", "uniqueItems": true, "minItems": 1, "items": {"enum": ["CanActivate", "CanActivateChild", "CanDeactivate", "CanMatch"], "type": "string"}, "default": ["CanActivate"]}, "typeSeparator": {"type": "string", "default": "-", "enum": ["-", "."], "description": "The separator character to use before the type within the generated file's name. For example, if you set the option to `.`, the file will be named `example.guard.ts`."}}}, "SchematicsAngularInterceptorSchema": {"title": "Angular Interceptor Options Schema", "type": "object", "additionalProperties": false, "description": "Creates a new interceptor in your project. Interceptors are used to intercept and modify HTTP requests and responses before they reach their destination. This allows you to perform tasks like adding authentication headers, handling errors, or logging requests. This schematic generates the necessary files and boilerplate code for a new interceptor.", "properties": {"name": {"type": "string", "description": "The name for the new interceptor. This will be used to create the interceptor's class and spec files (e.g., `my-interceptor.interceptor.ts` and `my-interceptor.interceptor.spec.ts`).", "$default": {"$source": "argv", "index": 0}}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path where the interceptor files should be created, relative to the workspace root. If not provided, the interceptor will be created in the current directory.", "visible": false}, "project": {"type": "string", "description": "The name of the project where the interceptor should be created. If not specified, the CLI will determine the project from the current directory.", "$default": {"$source": "projectName"}}, "flat": {"type": "boolean", "default": true, "description": "Creates the new interceptor files at the top level of the current project. If set to false, a new folder with the interceptor's name will be created to contain the files."}, "skipTests": {"type": "boolean", "description": "Skip the generation of a unit test file `spec.ts` for the new interceptor.", "default": false}, "functional": {"type": "boolean", "description": "Creates the interceptor as a function `HttpInterceptorFn` instead of a class. Functional interceptors can be simpler for basic scenarios.", "default": true}, "typeSeparator": {"type": "string", "default": "-", "enum": ["-", "."], "description": "The separator character to use before the type within the generated file's name. For example, if you set the option to `.`, the file will be named `example.interceptor.ts`."}}}, "SchematicsAngularInterfaceSchema": {"title": "Angular Interface Options Schema", "type": "object", "additionalProperties": false, "description": "Creates a new interface in your project. Interfaces define the structure of objects in TypeScript, ensuring type safety and code clarity. This schematic generates a new interface with the specified name and type.", "properties": {"name": {"type": "string", "description": "The name for the new interface. This will be used to create the interface file (e.g., `my-interface.interface.ts`).", "$default": {"$source": "argv", "index": 0}}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path where the interface file should be created, relative to the workspace root. If not provided, the interface will be created in the current directory.", "visible": false}, "project": {"type": "string", "description": "The name of the project where the interface should be created. If not specified, the CLI will determine the project from the current directory.", "$default": {"$source": "projectName"}}, "prefix": {"type": "string", "description": "A prefix to be added to the interface name. This is typically not used for interfaces, as they don't have selectors like components or directives."}, "type": {"type": "string", "description": "Adds a custom type to the filename, allowing you to create more descriptive interface names. For example, if you set the type to `data`, the filename will be `my-interface.data.ts`.", "$default": {"$source": "argv", "index": 1}}}}, "SchematicsAngularLibrarySchema": {"title": "Library Options Schema", "type": "object", "description": "Creates a new library project in your Angular workspace. Libraries are reusable collections of components, services, and other Angular artifacts that can be shared across multiple applications. This schematic simplifies the process of generating a new library with the necessary files and configurations.", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name for the new library. This name will be used for the project directory and various identifiers within the library's code.", "pattern": "^(?:@[a-zA-Z0-9-*~][a-zA-Z0-9-*._~]*/)?[a-zA-Z0-9-~][a-zA-Z0-9-._~]*$", "$default": {"$source": "argv", "index": 0}}, "entryFile": {"type": "string", "format": "path", "description": "The path to the library's public API file, relative to the workspace root. This file defines what parts of the library are accessible to applications that import it.", "default": "public-api"}, "prefix": {"type": "string", "format": "html-selector", "description": "A prefix to be added to the selectors of components generated within this library. For example, if the prefix is `my-lib` and you generate a component named `my-component`, the selector will be `my-lib-my-component`.", "default": "lib", "alias": "p"}, "skipPackageJson": {"type": "boolean", "default": false, "description": "Do not automatically add dependencies to the `package.json` file."}, "skipInstall": {"description": "Skip the automatic installation of packages. You will need to manually install the dependencies later.", "type": "boolean", "default": false}, "skipTsConfig": {"type": "boolean", "default": false, "description": "Do not update the workspace `tsconfig.json` file to add a path mapping for the new library. The path mapping is needed to use the library in an application, but can be disabled here to simplify development."}, "projectRoot": {"type": "string", "description": "The root directory for the new library, relative to the workspace root. If not specified, the library will be created in a subfolder within the `projects` directory, using the library's name."}, "standalone": {"description": "Create a library that utilizes the standalone API, eliminating the need for NgModules. This can simplify the structure of your library and its usage in applications.", "type": "boolean", "default": true}}}, "SchematicsAngularPipeSchema": {"title": "Angular Pipe Options Schema", "type": "object", "additionalProperties": false, "description": "Creates a new pipe in your project. Pipes are used to transform data for display in templates. They take input values and apply a specific transformation, such as formatting dates, currency, or filtering arrays. This schematic generates the necessary files and boilerplate code for a new pipe.", "properties": {"name": {"type": "string", "description": "The name for the new pipe. This will be used to create the pipe's class and spec files (e.g., `my-pipe.pipe.ts` and `my-pipe.pipe.spec.ts`).", "$default": {"$source": "argv", "index": 0}}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path where the pipe files should be created, relative to the workspace root. If not provided, the pipe will be created in the current directory.", "visible": false}, "project": {"type": "string", "description": "The name of the project where the pipe should be created. If not specified, the CLI will determine the project from the current directory.", "$default": {"$source": "projectName"}}, "flat": {"type": "boolean", "default": true, "description": "Creates the new pipe files at the top level of the current project. If set to false, a new folder with the pipe's name will be created to contain the files."}, "skipTests": {"type": "boolean", "description": "Prevent the generation of a unit test file `spec.ts` for the new pipe.", "default": false}, "skipImport": {"type": "boolean", "default": false, "description": "Do not automatically import the new pipe into its closest NgModule."}, "standalone": {"description": "Generate a standalone pipe. Standalone pipes are self-contained and don't need to be declared in an NgModule. They can be used independently or imported directly into other standalone components, directives, or pipes.", "type": "boolean", "default": true}, "module": {"type": "string", "description": "Specify the NgModule where the pipe should be declared. If not provided, the CLI will attempt to find the closest NgModule in the pipe's path.", "alias": "m"}, "export": {"type": "boolean", "default": false, "description": "Automatically export the pipe from the specified NgModule, making it accessible to other modules in the application."}, "typeSeparator": {"type": "string", "default": "-", "enum": ["-", "."], "description": "The separator character to use before the type within the generated file's name. For example, if you set the option to `.`, the file will be named `example.pipe.ts`."}}}, "SchematicsAngularNgNewSchema": {"title": "Angular Ng New Options Schema", "type": "object", "description": "Creates a new Angular workspace and an initial project. This schematic sets up the foundation for your Angular development, generating the workspace configuration files and an optional starter application. You can customize various aspects of the workspace and the initial project, such as routing, styling, and testing.", "additionalProperties": false, "properties": {"directory": {"type": "string", "description": "The directory where the new workspace and project should be created. If not specified, the workspace will be created in the current directory."}, "name": {"description": "The name for the new workspace and the initial project. This name will be used for the root directory and various identifiers throughout the project.", "type": "string", "$default": {"$source": "argv", "index": 0}}, "skipInstall": {"description": "Skip the automatic installation of packages. You will need to manually install the dependencies later.", "type": "boolean", "default": false}, "skipGit": {"description": "Do not initialize a Git repository in the new workspace. By default, a Git repository is initialized to help you track changes to your project.", "type": "boolean", "default": false, "alias": "g"}, "commit": {"description": "Configure the initial Git commit for the new repository.", "oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "message": {"type": "string"}}}], "default": true}, "newProjectRoot": {"description": "The path where new projects will be created within the workspace, relative to the workspace root. By default, new projects are created in the `projects` directory.", "type": "string", "default": "projects"}, "inlineStyle": {"description": "Include the styles for the initial application's root component directly within the `app.component.ts` file. By default, a separate stylesheet file (e.g., `app.component.css`) is created.", "type": "boolean", "alias": "s"}, "inlineTemplate": {"description": "Include the HTML template for the initial application's root component directly within the `app.component.ts` file. By default, a separate template file (e.g., `app.component.html`) is created.", "type": "boolean", "alias": "t"}, "viewEncapsulation": {"description": "Sets the view encapsulation mode for components in the initial project. This determines how component styles are scoped and applied. Options include: `Emulated` (default, styles are scoped to the component), `None` (styles are global), and `ShadowDom` (styles are encapsulated using Shadow DOM).", "enum": ["Emulated", "None", "ShadowDom"], "type": "string"}, "version": {"type": "string", "description": "The version of the Angular CLI to use.", "visible": false, "$default": {"$source": "ng-cli-version"}}, "routing": {"type": "boolean", "description": "Enable routing in the initial application project. This sets up the necessary files and modules for managing navigation between different views in your application."}, "prefix": {"type": "string", "format": "html-selector", "description": "The prefix to apply to generated selectors for the initial project. For example, if the prefix is `my-app` and you generate a component named `my-component`, the selector will be `my-app-my-component`.", "minLength": 1, "default": "app", "alias": "p"}, "style": {"description": "The type of stylesheet files to be created for components in the initial project.", "type": "string", "enum": ["css", "scss", "sass", "less"]}, "skipTests": {"description": "Skip the generation of unit test files `spec.ts`.", "type": "boolean", "default": false, "alias": "S"}, "createApplication": {"description": "Create a new initial application project in the new workspace. When false, creates an empty workspace with no initial application. You can then use the `ng generate application` command to create applications in the `projects` directory.", "type": "boolean", "default": true}, "minimal": {"description": "Generate a minimal Angular workspace without any testing frameworks. This is intended for learning purposes and simple experimentation, not for production applications.", "type": "boolean", "default": false}, "strict": {"description": "Enable stricter type checking and stricter bundle budgets settings. This setting helps improve maintainability and catch bugs ahead of time. For more information, see https://angular.dev/tools/cli/template-typecheck#strict-mode", "type": "boolean", "default": true}, "packageManager": {"description": "The package manager used to install dependencies.", "type": "string", "enum": ["npm", "yarn", "pnpm", "cnpm", "bun"]}, "standalone": {"description": "Creates an application based upon the standalone API, without NgModules.", "type": "boolean", "default": true}, "ssr": {"description": "Configure the initial application for Server-Side Rendering (SSR) and Static Site Generation (SSG/Prerendering).", "type": "boolean"}, "zoneless": {"description": "Create an initial application that does not utilize `zone.js`.", "type": "boolean", "default": false}}}, "SchematicsAngularResolverSchema": {"title": "Angular Resolver Options Schema", "type": "object", "additionalProperties": false, "description": "Creates a new resolver in your project. Resolvers are used to pre-fetch data before a route is activated, ensuring that the necessary data is available before the component is displayed. This can improve the user experience by preventing delays and loading states. This schematic generates a new resolver with the specified name and options.", "properties": {"name": {"type": "string", "description": "The name for the new resolver. This will be used to create the resolver's class and spec files (e.g., `my-resolver.resolver.ts` and `my-resolver.resolver.spec.ts`).", "$default": {"$source": "argv", "index": 0}}, "skipTests": {"type": "boolean", "description": "Skip the generation of a unit test file `spec.ts` for the new resolver.", "default": false}, "flat": {"type": "boolean", "description": "Creates the new resolver files at the top level of the current project. If set to false, a new folder with the resolver's name will be created to contain the files.", "default": true}, "functional": {"type": "boolean", "description": "Creates the resolver as a function `ResolveFn` instead of a class. Functional resolvers can be simpler for basic scenarios.", "default": true}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path where the resolver files should be created, relative to the current workspace. If not provided, the resolver will be created in the current directory.", "visible": false}, "project": {"type": "string", "description": "The name of the project where the resolver should be created. If not specified, the CLI will determine the project from the current directory.", "$default": {"$source": "projectName"}}, "typeSeparator": {"type": "string", "default": "-", "enum": ["-", "."], "description": "The separator character to use before the type within the generated file's name. For example, if you set the option to `.`, the file will be named `example.resolver.ts`."}}}, "SchematicsAngularServiceSchema": {"title": "Angular Service Options Schema", "type": "object", "additionalProperties": false, "description": "Creates a new service in your project. Services are used to encapsulate reusable logic, such as data access, API calls, or utility functions. This schematic simplifies the process of generating a new service with the necessary files and boilerplate code.", "properties": {"name": {"type": "string", "description": "The name for the new service. This will be used to create the service's class and spec files (e.g., `my-service.service.ts` and `my-service.service.spec.ts`).", "$default": {"$source": "argv", "index": 0}}, "path": {"type": "string", "$default": {"$source": "workingDirectory"}, "description": "The path where the service files should be created, relative to the workspace root. If not provided, the service will be created in the project's `src/app` directory.", "visible": false}, "project": {"type": "string", "description": "The name of the project where the service should be added. If not specified, the CLI will determine the project from the current directory.", "$default": {"$source": "projectName"}}, "flat": {"type": "boolean", "default": true, "description": "Creates files at the top level of the project or the given path. If set to false, a new folder with the service's name will be created to contain the files."}, "skipTests": {"type": "boolean", "description": "Skip the generation of a unit test file `spec.ts` for the service.", "default": false}, "type": {"type": "string", "description": "Append a custom type to the service's filename. For example, if you set the type to `service`, the file will be named `my-service.service.ts`."}}}, "SchematicsAngularWebWorkerSchema": {"title": "Angular Web Worker Options Schema", "type": "object", "additionalProperties": false, "description": "Creates a new web worker in your project. Web workers allow you to run JavaScript code in the background, improving the performance and responsiveness of your application by offloading computationally intensive tasks. This schematic generates the necessary files for a new web worker and provides an optional code snippet to demonstrate its usage.", "properties": {"path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path where the web worker file should be created, relative to the current workspace. If not specified, the worker will be created in the current directory.", "visible": false}, "project": {"type": "string", "description": "The name of the project where the web worker should be created. If not specified, the CLI will determine the project from the current directory.", "$default": {"$source": "projectName"}}, "name": {"type": "string", "description": "The name for the new web worker. This will be used to create the worker file (e.g., `my-worker.worker.ts`).", "$default": {"$source": "argv", "index": 0}}, "snippet": {"type": "boolean", "default": true, "description": "Generate a code snippet that demonstrates how to create and use the new web worker."}}}, "AngularBuildBuildersApplicationSchema": {"title": "Application schema for Build Facade.", "description": "Application builder target options", "type": "object", "properties": {"assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/AngularBuildBuildersApplicationSchema/definitions/assetPattern"}}, "browser": {"type": "string", "description": "The full path for the browser entry point to the application, relative to the current workspace."}, "server": {"description": "The full path for the server entry point to the application, relative to the current workspace.", "oneOf": [{"type": "string", "description": "The full path for the server entry point to the application, relative to the current workspace."}, {"const": false, "type": "boolean", "description": "Indicates that a server entry point is not provided."}]}, "polyfills": {"description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "type": "array", "items": {"type": "string", "uniqueItems": true}, "default": []}, "tsConfig": {"type": "string", "description": "The full path for the TypeScript configuration file, relative to the current workspace."}, "deployUrl": {"type": "string", "description": "Customize the base path for the URLs of resources in 'index.html' and component stylesheets. This option is only necessary for specific deployment scenarios, such as with Angular Elements or when utilizing different CDN locations."}, "security": {"description": "Security features to protect against XSS and other common attacks", "type": "object", "additionalProperties": false, "properties": {"autoCsp": {"description": "Enables automatic generation of a hash-based Strict Content Security Policy (https://web.dev/articles/strict-csp#choose-hash) based on scripts in index.html. Will default to true once we are out of experimental/preview phases.", "default": false, "oneOf": [{"type": "object", "properties": {"unsafeEval": {"type": "boolean", "description": "Include the `unsafe-eval` directive (https://web.dev/articles/strict-csp#remove-eval) in the auto-CSP. Please only enable this if you are absolutely sure that you need to, as allowing calls to eval will weaken the XSS defenses provided by the auto-CSP.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}}}, "scripts": {"description": "Global scripts to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The JavaScript/TypeScript file or package containing the file to include."}]}}, "styles": {"description": "Global styles to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}]}}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors.", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}, "sass": {"description": "Options to pass to the sass preprocessor.", "type": "object", "properties": {"fatalDeprecations": {"description": "A set of deprecations to treat as fatal. If a deprecation warning of any provided type is encountered during compilation, the compiler will error instead. If a Version is provided, then all deprecations that were active in that compiler version will be treated as fatal.", "type": "array", "items": {"type": "string"}}, "silenceDeprecations": {"description": " A set of active deprecations to ignore. If a deprecation warning of any provided type is encountered during compilation, the compiler will ignore it instead.", "type": "array", "items": {"type": "string"}}, "futureDeprecations": {"description": "A set of future deprecations to opt into early. Future deprecations passed here will be treated as active by the compiler, emitting warnings as necessary.", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "additionalProperties": false}, "externalDependencies": {"description": "Exclude the listed external dependencies from being bundled into the bundle. Instead, the created bundle relies on these dependencies to be available during runtime. Note: `@foo/bar` marks all paths within the `@foo/bar` package as external, including sub-paths like `@foo/bar/baz`.", "type": "array", "items": {"type": "string"}, "default": []}, "clearScreen": {"type": "boolean", "default": false, "description": "Automatically clear the terminal screen during rebuilds."}, "optimization": {"description": "Enables optimization of the build output. Including minification of scripts and styles, tree-shaking, dead-code elimination, inlining of critical CSS and fonts inlining. For more information, see https://angular.dev/reference/configs/workspace-config#optimization-configuration.", "default": true, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Enables optimization of the scripts output.", "default": true}, "styles": {"description": "Enables optimization of the styles output.", "default": true, "oneOf": [{"type": "object", "properties": {"minify": {"type": "boolean", "description": "Minify CSS definitions by removing extraneous whitespace and comments, merging identifiers and minimizing values.", "default": true}, "inlineCritical": {"type": "boolean", "description": "Extract and inline critical CSS definitions to improve first paint time.", "default": true}, "removeSpecialComments": {"type": "boolean", "description": "Remove comments in global CSS that contains '@license' or '@preserve' or that starts with '//!' or '/*!'.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}, "fonts": {"description": "Enables optimization for fonts. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true, "oneOf": [{"type": "object", "properties": {"inline": {"type": "boolean", "description": "Reduce render blocking requests by inlining external Google Fonts and Adobe Fonts CSS definitions in the application's HTML index file. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}}, "additionalProperties": false}, {"type": "boolean"}]}, "loader": {"description": "Defines the type of loader to use with a specified file extension when used with a JavaScript `import`. `text` inlines the content as a string; `binary` inlines the content as a Uint8Array; `file` emits the file and provides the runtime location of the file; `empty` considers the content to be empty and not include it in bundles.", "type": "object", "patternProperties": {"^\\.\\S+$": {"enum": ["text", "binary", "file", "empty"]}}}, "define": {"description": "Defines global identifiers that will be replaced with a specified constant value when found in any JavaScript or TypeScript code including libraries. The value will be used directly. String values must be put in quotes. Identifiers within Angular metadata such as Component Decorators will not be replaced.", "type": "object", "additionalProperties": {"type": "string"}}, "conditions": {"description": "Custom package resolution conditions used to resolve conditional exports/imports. Defaults to ['module', 'development'/'production']. The following special conditions are always present if the requirements are satisfied: 'default', 'import', 'require', 'browser', 'node'.", "type": "array", "items": {"type": "string"}}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"$ref": "#/definitions/AngularBuildBuildersApplicationSchema/definitions/fileReplacement"}, "default": []}, "outputPath": {"description": "Specify the output path relative to workspace root.", "oneOf": [{"type": "object", "properties": {"base": {"type": "string", "description": "Specify the output path relative to workspace root."}, "browser": {"type": "string", "pattern": "^[-\\w\\.]*$", "default": "browser", "description": "The output directory name of your browser build within the output path base. Defaults to 'browser'."}, "server": {"type": "string", "pattern": "^[-\\w\\.]*$", "default": "server", "description": "The output directory name of your server build within the output path base. Defaults to 'server'."}, "media": {"type": "string", "pattern": "^[-\\w\\.]+$", "default": "media", "description": "The output directory name of your media files within the output browser directory. Defaults to 'media'."}}, "additionalProperties": false}, {"type": "string"}]}, "aot": {"type": "boolean", "description": "Build using Ahead of Time compilation.", "default": true}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.dev/reference/configs/workspace-config#source-map-configuration.", "default": false, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "hidden": {"type": "boolean", "description": "Output source maps used for error reporting tools.", "default": false}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}, "sourcesContent": {"type": "boolean", "description": "Output original source content for files within the source map.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}, "baseHref": {"type": "string", "description": "Base url for the application being built."}, "verbose": {"type": "boolean", "description": "Adds more details to output logging.", "default": false}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "i18nMissingTranslation": {"type": "string", "description": "How to handle missing translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "i18nDuplicateTranslation": {"type": "string", "description": "How to handle duplicate translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "localize": {"description": "Translate the bundles in one or more locales.", "oneOf": [{"type": "boolean", "description": "Translate all locales."}, {"type": "array", "description": "List of locales ID's to translate.", "minItems": 1, "items": {"type": "string", "pattern": "^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$"}}]}, "watch": {"type": "boolean", "description": "Run build when files change.", "default": false}, "outputHashing": {"type": "string", "description": "Define the output filename cache-busting hashing mode.", "default": "none", "enum": ["none", "all", "media", "bundles"]}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "deleteOutputPath": {"type": "boolean", "description": "Delete the output path before building.", "default": true}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "extractLicenses": {"type": "boolean", "description": "Extract all licenses in a separate file.", "default": true}, "namedChunks": {"type": "boolean", "description": "Use file name for lazy loaded chunks.", "default": false}, "subresourceIntegrity": {"type": "boolean", "description": "Enables the use of subresource integrity validation.", "default": false}, "serviceWorker": {"description": "Generates a service worker configuration.", "default": false, "oneOf": [{"type": "string", "description": "Path to ngsw-config.json."}, {"const": false, "type": "boolean", "description": "Does not generate a service worker configuration."}]}, "index": {"description": "Configures the generation of the application's HTML index.", "oneOf": [{"type": "string", "description": "The path of a file to use for the application's HTML index. The filename of the specified path will be used for the generated file and will be created in the root of the application's configured output path."}, {"type": "object", "description": "", "properties": {"input": {"type": "string", "minLength": 1, "description": "The path of a file to use for the application's generated HTML index."}, "output": {"type": "string", "minLength": 1, "default": "index.html", "description": "The output path of the application's generated HTML index file. The full provided path will be used and will be considered relative to the application's configured output path."}, "preloadInitial": {"type": "boolean", "default": true, "description": "Generates 'preload', 'modulepreload', and 'preconnect' link elements for initial application files and resources."}}}, {"const": false, "type": "boolean", "description": "Does not generate an `index.html` file."}]}, "statsJson": {"type": "boolean", "description": "Generates a 'stats.json' file which can be analyzed with https://esbuild.github.io/analyze/.", "default": false}, "budgets": {"description": "Budget thresholds to ensure parts of your application stay within boundaries which you set.", "type": "array", "items": {"$ref": "#/definitions/AngularBuildBuildersApplicationSchema/definitions/budget"}, "default": []}, "webWorkerTsConfig": {"type": "string", "description": "TypeScript configuration for Web Worker modules."}, "crossOrigin": {"type": "string", "description": "Define the crossorigin attribute setting of elements that provide CORS support.", "default": "none", "enum": ["none", "anonymous", "use-credentials"]}, "allowedCommonJsDependencies": {"description": "A list of CommonJS or AMD packages that are allowed to be used without a build time warning. Use `'*'` to allow all.", "type": "array", "items": {"type": "string"}, "default": []}, "prerender": {"description": "Prerender (SSG) pages of your application during build time.", "oneOf": [{"type": "boolean", "description": "Enable prerending of pages of your application during build time."}, {"type": "object", "properties": {"routesFile": {"type": "string", "description": "The path to a file that contains a list of all routes to prerender, separated by newlines. This option is useful if you want to prerender routes with parameterized URLs."}, "discoverRoutes": {"type": "boolean", "description": "Whether the builder should process the Angular Router configuration to find all unparameterized routes and prerender them.", "default": true}}, "additionalProperties": false}]}, "ssr": {"description": "Server side render (SSR) pages of your application during runtime.", "default": false, "oneOf": [{"type": "boolean", "description": "Enable the server bundles to be written to disk."}, {"type": "object", "properties": {"entry": {"type": "string", "description": "The server entry-point that when executed will spawn the web server."}, "experimentalPlatform": {"description": "Specifies the platform for which the server bundle is generated. This affects the APIs and modules available in the server-side code. \n\n- `node`:  (<PERSON><PERSON><PERSON>) Generates a bundle optimized for Node.js environments. \n- `neutral`: Generates a platform-neutral bundle suitable for environments like edge workers, and other serverless platforms. This option avoids using Node.js-specific APIs, making the bundle more portable. \n\nPlease note that this feature does not provide polyfills for Node.js modules. Additionally, it is experimental, and the schematics may undergo changes in future versions.", "default": "node", "enum": ["node", "neutral"]}}, "additionalProperties": false}]}, "appShell": {"type": "boolean", "description": "Generates an application shell during build time."}, "outputMode": {"type": "string", "description": "Defines the build output target. 'static': Generates a static site for deployment on any static hosting service. 'server': Produces an application designed for deployment on a server that supports server-side rendering (SSR).", "enum": ["static", "server"]}}, "additionalProperties": false, "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"followSymlinks": {"type": "boolean", "default": false, "description": "Allow glob patterns to follow symlink directories. This allows subdirectories of the symlink to be searched."}, "glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}, "output": {"type": "string", "default": "", "description": "Absolute path within the output."}}, "additionalProperties": false}, {"type": "string"}]}, "fileReplacement": {"type": "object", "properties": {"replace": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "with": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false}, "budget": {"type": "object", "properties": {"type": {"type": "string", "description": "The type of budget.", "enum": ["all", "allScript", "any", "anyScript", "anyComponentStyle", "bundle", "initial"]}, "name": {"type": "string", "description": "The name of the bundle."}, "baseline": {"type": "string", "description": "The baseline size for comparison."}, "maximumWarning": {"type": "string", "description": "The maximum threshold for warning relative to the baseline."}, "maximumError": {"type": "string", "description": "The maximum threshold for error relative to the baseline."}, "minimumWarning": {"type": "string", "description": "The minimum threshold for warning relative to the baseline."}, "minimumError": {"type": "string", "description": "The minimum threshold for error relative to the baseline."}, "warning": {"type": "string", "description": "The threshold for warning relative to the baseline (min & max)."}, "error": {"type": "string", "description": "The threshold for error relative to the baseline (min & max)."}}, "additionalProperties": false}}}, "AngularDevkitBuildAngularBuildersAppShellSchema": {"title": "App Shell Target", "description": "App Shell target options for Build Facade.", "type": "object", "properties": {"browserTarget": {"type": "string", "description": "A browser builder target use for rendering the application shell in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^[^:\\s]+:[^:\\s]+(:[^\\s]+)?$"}, "serverTarget": {"type": "string", "description": "A server builder target use for rendering the application shell in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^[^:\\s]+:[^:\\s]+(:[^\\s]+)?$"}, "appModuleBundle": {"type": "string", "description": "Script that exports the Server AppModule to render. This should be the main JavaScript outputted by the server target. By default we will resolve the outputPath of the serverTarget and find a bundle named 'main' in it (whether or not there's a hash tag)."}, "route": {"type": "string", "description": "The route to render.", "default": "/"}, "inputIndexPath": {"type": "string", "description": "The input path for the index.html file. By default uses the output index.html of the browser target."}, "outputIndexPath": {"type": "string", "description": "The output path of the index.html file. By default will overwrite the input file."}}, "additionalProperties": false}, "AngularDevkitBuildAngularBuildersBrowserSchema": {"title": "Webpack browser schema for Build Facade.", "description": "Browser target options", "type": "object", "properties": {"assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserSchema/definitions/assetPattern"}}, "main": {"type": "string", "description": "The full path for the main entry point to the app, relative to the current workspace."}, "polyfills": {"description": "Polyfills to be included in the build.", "oneOf": [{"type": "array", "description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "items": {"type": "string", "uniqueItems": true}, "default": []}, {"type": "string", "description": "The full path for the polyfills file, relative to the current workspace or a module specifier. Example: 'zone.js'."}]}, "tsConfig": {"type": "string", "description": "The full path for the TypeScript configuration file, relative to the current workspace."}, "scripts": {"description": "Global scripts to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}]}}, "styles": {"description": "Global styles to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}]}}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors.", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false}, "optimization": {"description": "Enables optimization of the build output. Including minification of scripts and styles, tree-shaking, dead-code elimination, inlining of critical CSS and fonts inlining. For more information, see https://angular.dev/reference/configs/workspace-config#optimization-configuration.", "default": true, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Enables optimization of the scripts output.", "default": true}, "styles": {"description": "Enables optimization of the styles output.", "default": true, "oneOf": [{"type": "object", "properties": {"minify": {"type": "boolean", "description": "Minify CSS definitions by removing extraneous whitespace and comments, merging identifiers and minimizing values.", "default": true}, "inlineCritical": {"type": "boolean", "description": "Extract and inline critical CSS definitions to improve first paint time.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}, "fonts": {"description": "Enables optimization for fonts. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true, "oneOf": [{"type": "object", "properties": {"inline": {"type": "boolean", "description": "Reduce render blocking requests by inlining external Google Fonts and Adobe Fonts CSS definitions in the application's HTML index file. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}}, "additionalProperties": false}, {"type": "boolean"}]}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserSchema/definitions/fileReplacement"}, "default": []}, "outputPath": {"type": "string", "description": "The full path for the new output directory, relative to the current workspace."}, "resourcesOutputPath": {"type": "string", "description": "The path where style resources will be placed, relative to outputPath."}, "aot": {"type": "boolean", "description": "Build using Ahead of Time compilation.", "default": true}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.dev/reference/configs/workspace-config#source-map-configuration.", "default": false, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "hidden": {"type": "boolean", "description": "Output source maps used for error reporting tools.", "default": false}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "vendorChunk": {"type": "boolean", "description": "Generate a seperate bundle containing only vendor libraries. This option should only be used for development to reduce the incremental compilation time.", "default": false}, "commonChunk": {"type": "boolean", "description": "Generate a seperate bundle containing code used across multiple bundles.", "default": true}, "baseHref": {"type": "string", "description": "Base url for the application being built."}, "deployUrl": {"type": "string", "description": "Customize the base path for the URLs of resources in 'index.html' and component stylesheets. This option is only necessary for specific deployment scenarios, such as with Angular Elements or when utilizing different CDN locations."}, "verbose": {"type": "boolean", "description": "Adds more details to output logging.", "default": false}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "i18nMissingTranslation": {"type": "string", "description": "How to handle missing translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "i18nDuplicateTranslation": {"type": "string", "description": "How to handle duplicate translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "localize": {"description": "Translate the bundles in one or more locales.", "oneOf": [{"type": "boolean", "description": "Translate all locales."}, {"type": "array", "description": "List of locales ID's to translate.", "minItems": 1, "items": {"type": "string", "pattern": "^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$"}}]}, "watch": {"type": "boolean", "description": "Run build when files change.", "default": false}, "outputHashing": {"type": "string", "description": "Define the output filename cache-busting hashing mode.", "default": "none", "enum": ["none", "all", "media", "bundles"]}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "deleteOutputPath": {"type": "boolean", "description": "Delete the output path before building.", "default": true}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "extractLicenses": {"type": "boolean", "description": "Extract all licenses in a separate file.", "default": true}, "buildOptimizer": {"type": "boolean", "description": "Enables advanced build optimizations when using the 'aot' option.", "default": true}, "namedChunks": {"type": "boolean", "description": "Use file name for lazy loaded chunks.", "default": false}, "subresourceIntegrity": {"type": "boolean", "description": "Enables the use of subresource integrity validation.", "default": false}, "serviceWorker": {"type": "boolean", "description": "Generates a service worker config for production builds.", "default": false}, "ngswConfigPath": {"type": "string", "description": "Path to ngsw-config.json."}, "index": {"description": "Configures the generation of the application's HTML index.", "oneOf": [{"type": "string", "description": "The path of a file to use for the application's HTML index. The filename of the specified path will be used for the generated file and will be created in the root of the application's configured output path."}, {"type": "object", "description": "", "properties": {"input": {"type": "string", "minLength": 1, "description": "The path of a file to use for the application's generated HTML index."}, "output": {"type": "string", "minLength": 1, "default": "index.html", "description": "The output path of the application's generated HTML index file. The full provided path will be used and will be considered relative to the application's configured output path."}}}]}, "statsJson": {"type": "boolean", "description": "Generates a 'stats.json' file which can be analyzed using tools such as 'webpack-bundle-analyzer'.", "default": false}, "budgets": {"description": "Budget thresholds to ensure parts of your application stay within boundaries which you set.", "type": "array", "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserSchema/definitions/budget"}, "default": []}, "webWorkerTsConfig": {"type": "string", "description": "TypeScript configuration for Web Worker modules."}, "crossOrigin": {"type": "string", "description": "Define the crossorigin attribute setting of elements that provide CORS support.", "default": "none", "enum": ["none", "anonymous", "use-credentials"]}, "allowedCommonJsDependencies": {"description": "A list of CommonJS or AMD packages that are allowed to be used without a build time warning. Use `'*'` to allow all.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false, "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"followSymlinks": {"type": "boolean", "default": false, "description": "Allow glob patterns to follow symlink directories. This allows subdirectories of the symlink to be searched."}, "glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}, "output": {"type": "string", "default": "", "description": "Absolute path within the output."}}, "additionalProperties": false}, {"type": "string"}]}, "fileReplacement": {"oneOf": [{"type": "object", "properties": {"src": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "replaceWith": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false}, {"type": "object", "properties": {"replace": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "with": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false}]}, "budget": {"type": "object", "properties": {"type": {"type": "string", "description": "The type of budget.", "enum": ["all", "allScript", "any", "anyScript", "anyComponentStyle", "bundle", "initial"]}, "name": {"type": "string", "description": "The name of the bundle."}, "baseline": {"type": "string", "description": "The baseline size for comparison."}, "maximumWarning": {"type": "string", "description": "The maximum threshold for warning relative to the baseline."}, "maximumError": {"type": "string", "description": "The maximum threshold for error relative to the baseline."}, "minimumWarning": {"type": "string", "description": "The minimum threshold for warning relative to the baseline."}, "minimumError": {"type": "string", "description": "The minimum threshold for error relative to the baseline."}, "warning": {"type": "string", "description": "The threshold for warning relative to the baseline (min & max)."}, "error": {"type": "string", "description": "The threshold for error relative to the baseline (min & max)."}}, "additionalProperties": false}}}, "AngularDevkitBuildAngularBuildersBrowserEsbuildSchema": {"title": "Esbuild browser schema for Build Facade.", "description": "Browser target options", "type": "object", "properties": {"assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserEsbuildSchema/definitions/assetPattern"}}, "main": {"type": "string", "description": "The full path for the main entry point to the app, relative to the current workspace."}, "polyfills": {"description": "Polyfills to be included in the build.", "oneOf": [{"type": "array", "description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "items": {"type": "string", "uniqueItems": true}, "default": []}, {"type": "string", "description": "The full path for the polyfills file, relative to the current workspace or a module specifier. Example: 'zone.js'."}]}, "tsConfig": {"type": "string", "description": "The full path for the TypeScript configuration file, relative to the current workspace."}, "scripts": {"description": "Global scripts to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The JavaScript/TypeScript file or package containing the file to include."}]}}, "styles": {"description": "Global styles to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}]}}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors.", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false}, "externalDependencies": {"description": "Exclude the listed external dependencies from being bundled into the bundle. Instead, the created bundle relies on these dependencies to be available during runtime.", "type": "array", "items": {"type": "string"}, "default": []}, "optimization": {"description": "Enables optimization of the build output. Including minification of scripts and styles, tree-shaking, dead-code elimination, inlining of critical CSS and fonts inlining. For more information, see https://angular.dev/reference/configs/workspace-config#optimization-configuration.", "default": true, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Enables optimization of the scripts output.", "default": true}, "styles": {"description": "Enables optimization of the styles output.", "default": true, "oneOf": [{"type": "object", "properties": {"minify": {"type": "boolean", "description": "Minify CSS definitions by removing extraneous whitespace and comments, merging identifiers and minimizing values.", "default": true}, "inlineCritical": {"type": "boolean", "description": "Extract and inline critical CSS definitions to improve first paint time.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}, "fonts": {"description": "Enables optimization for fonts. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true, "oneOf": [{"type": "object", "properties": {"inline": {"type": "boolean", "description": "Reduce render blocking requests by inlining external Google Fonts and Adobe Fonts CSS definitions in the application's HTML index file. This option requires internet access. `HTTPS_PROXY` environment variable can be used to specify a proxy server.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}}, "additionalProperties": false}, {"type": "boolean"}]}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserEsbuildSchema/definitions/fileReplacement"}, "default": []}, "outputPath": {"type": "string", "description": "The full path for the new output directory, relative to the current workspace."}, "resourcesOutputPath": {"type": "string", "description": "The path where style resources will be placed, relative to outputPath."}, "aot": {"type": "boolean", "description": "Build using Ahead of Time compilation.", "default": true}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.dev/reference/configs/workspace-config#source-map-configuration.", "default": false, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "hidden": {"type": "boolean", "description": "Output source maps used for error reporting tools.", "default": false}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "vendorChunk": {"type": "boolean", "description": "Generate a seperate bundle containing only vendor libraries. This option should only be used for development to reduce the incremental compilation time.", "default": false}, "commonChunk": {"type": "boolean", "description": "Generate a seperate bundle containing code used across multiple bundles.", "default": true}, "baseHref": {"type": "string", "description": "Base url for the application being built."}, "deployUrl": {"type": "string", "description": "Customize the base path for the URLs of resources in 'index.html' and component stylesheets. This option is only necessary for specific deployment scenarios, such as with Angular Elements or when utilizing different CDN locations."}, "verbose": {"type": "boolean", "description": "Adds more details to output logging.", "default": false}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "i18nMissingTranslation": {"type": "string", "description": "How to handle missing translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "i18nDuplicateTranslation": {"type": "string", "description": "How to handle duplicate translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "localize": {"description": "Translate the bundles in one or more locales.", "oneOf": [{"type": "boolean", "description": "Translate all locales."}, {"type": "array", "description": "List of locales ID's to translate.", "minItems": 1, "items": {"type": "string", "pattern": "^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$"}}]}, "watch": {"type": "boolean", "description": "Run build when files change.", "default": false}, "outputHashing": {"type": "string", "description": "Define the output filename cache-busting hashing mode.", "default": "none", "enum": ["none", "all", "media", "bundles"]}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "deleteOutputPath": {"type": "boolean", "description": "Delete the output path before building.", "default": true}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "extractLicenses": {"type": "boolean", "description": "Extract all licenses in a separate file.", "default": true}, "buildOptimizer": {"type": "boolean", "description": "Enables advanced build optimizations when using the 'aot' option.", "default": true}, "namedChunks": {"type": "boolean", "description": "Use file name for lazy loaded chunks.", "default": false}, "subresourceIntegrity": {"type": "boolean", "description": "Enables the use of subresource integrity validation.", "default": false}, "serviceWorker": {"type": "boolean", "description": "Generates a service worker config for production builds.", "default": false}, "ngswConfigPath": {"type": "string", "description": "Path to ngsw-config.json."}, "index": {"description": "Configures the generation of the application's HTML index.", "oneOf": [{"type": "string", "description": "The path of a file to use for the application's HTML index. The filename of the specified path will be used for the generated file and will be created in the root of the application's configured output path."}, {"type": "object", "description": "", "properties": {"input": {"type": "string", "minLength": 1, "description": "The path of a file to use for the application's generated HTML index."}, "output": {"type": "string", "minLength": 1, "default": "index.html", "description": "The output path of the application's generated HTML index file. The full provided path will be used and will be considered relative to the application's configured output path."}}}, {"const": false, "type": "boolean", "description": "Does not generate an `index.html` file."}]}, "statsJson": {"type": "boolean", "description": "Generates a 'stats.json' file which can be analyzed using tools such as 'webpack-bundle-analyzer'.", "default": false}, "budgets": {"description": "Budget thresholds to ensure parts of your application stay within boundaries which you set.", "type": "array", "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersBrowserEsbuildSchema/definitions/budget"}, "default": []}, "webWorkerTsConfig": {"type": "string", "description": "TypeScript configuration for Web Worker modules."}, "crossOrigin": {"type": "string", "description": "Define the crossorigin attribute setting of elements that provide CORS support.", "default": "none", "enum": ["none", "anonymous", "use-credentials"]}, "allowedCommonJsDependencies": {"description": "A list of CommonJS or AMD packages that are allowed to be used without a build time warning. Use `'*'` to allow all.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false, "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"followSymlinks": {"type": "boolean", "default": false, "description": "Allow glob patterns to follow symlink directories. This allows subdirectories of the symlink to be searched."}, "glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}, "output": {"type": "string", "default": "", "description": "Absolute path within the output."}}, "additionalProperties": false}, {"type": "string"}]}, "fileReplacement": {"type": "object", "properties": {"replace": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "with": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false}, "budget": {"type": "object", "properties": {"type": {"type": "string", "description": "The type of budget.", "enum": ["all", "allScript", "any", "anyScript", "anyComponentStyle", "bundle", "initial"]}, "name": {"type": "string", "description": "The name of the bundle."}, "baseline": {"type": "string", "description": "The baseline size for comparison."}, "maximumWarning": {"type": "string", "description": "The maximum threshold for warning relative to the baseline."}, "maximumError": {"type": "string", "description": "The maximum threshold for error relative to the baseline."}, "minimumWarning": {"type": "string", "description": "The minimum threshold for warning relative to the baseline."}, "minimumError": {"type": "string", "description": "The minimum threshold for error relative to the baseline."}, "warning": {"type": "string", "description": "The threshold for warning relative to the baseline (min & max)."}, "error": {"type": "string", "description": "The threshold for error relative to the baseline (min & max)."}}, "additionalProperties": false}}}, "AngularBuildBuildersDevServerSchema": {"title": "Dev Server Target", "description": "Dev Server target options for Build Facade.", "type": "object", "properties": {"buildTarget": {"type": "string", "description": "A build builder target to serve in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^[^:\\s]*:[^:\\s]*(:[^\\s]+)?$"}, "port": {"type": "number", "description": "Port to listen on.", "default": 4200}, "host": {"type": "string", "description": "Host to listen on.", "default": "localhost"}, "proxyConfig": {"type": "string", "description": "Proxy configuration file. For more information, see https://angular.dev/tools/cli/serve#proxying-to-a-backend-server."}, "ssl": {"type": "boolean", "description": "Serve using HTTPS.", "default": false}, "sslKey": {"type": "string", "description": "SSL key to use for serving HTTPS."}, "sslCert": {"type": "string", "description": "SSL certificate to use for serving HTTPS."}, "allowedHosts": {"description": "The hosts that the development server will respond to. This option sets the Vite option of the same name. For further details: https://vite.dev/config/server-options.html#server-allowedhosts", "default": [], "oneOf": [{"type": "array", "description": "A list of hosts that the development server will respond to.", "items": {"type": "string"}}, {"type": "boolean", "description": "Indicates that all hosts are allowed. This is not recommended and a security risk."}]}, "headers": {"type": "object", "description": "Custom HTTP headers to be added to all responses.", "propertyNames": {"pattern": "^[-_A-Za-z0-9]+$"}, "additionalProperties": {"type": "string"}}, "open": {"type": "boolean", "description": "Opens the url in default browser.", "default": false, "alias": "o"}, "verbose": {"type": "boolean", "description": "Adds more details to output logging."}, "liveReload": {"type": "boolean", "description": "Whether to reload the page on change, using live-reload.", "default": true}, "servePath": {"type": "string", "description": "The pathname where the application will be served."}, "hmr": {"type": "boolean", "description": "Enable hot module replacement. Defaults to the value of 'liveReload'. Currently, only global and component stylesheets are supported."}, "watch": {"type": "boolean", "description": "Rebuild on change.", "default": true}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "inspect": {"default": false, "description": "Activate debugging inspector. This option only has an effect when 'SSR' or 'SSG' are enabled.", "oneOf": [{"type": "string", "description": "Activate the inspector on host and port in the format of `[[host:]port]`. See the security warning in https://nodejs.org/docs/latest-v22.x/api/cli.html#warning-binding-inspector-to-a-public-ipport-combination-is-insecure regarding the host parameter usage."}, {"type": "boolean"}]}, "prebundle": {"description": "Enable and control the Vite-based development server's prebundling capabilities. To enable prebundling, the Angular CLI cache must also be enabled.", "default": true, "oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"exclude": {"description": "List of package imports that should not be prebundled by the development server. The packages will be bundled into the application code itself. Note: specifying `@foo/bar` marks all paths within the `@foo/bar` package as excluded, including sub-paths like `@foo/bar/baz`.", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}]}}, "additionalProperties": false}, "AngularDevkitBuildAngularBuildersDevServerSchema": {"title": "Dev Server Target", "description": "Dev Server target options for Build Facade.", "type": "object", "properties": {"buildTarget": {"type": "string", "description": "A build builder target to serve in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^[^:\\s]*:[^:\\s]*(:[^\\s]+)?$"}, "port": {"type": "number", "description": "Port to listen on.", "default": 4200}, "host": {"type": "string", "description": "Host to listen on.", "default": "localhost"}, "proxyConfig": {"type": "string", "description": "Proxy configuration file. For more information, see https://angular.dev/tools/cli/serve#proxying-to-a-backend-server."}, "ssl": {"type": "boolean", "description": "Serve using HTTPS.", "default": false}, "sslKey": {"type": "string", "description": "SSL key to use for serving HTTPS."}, "sslCert": {"type": "string", "description": "SSL certificate to use for serving HTTPS."}, "headers": {"type": "object", "description": "Custom HTTP headers to be added to all responses.", "propertyNames": {"pattern": "^[-_A-Za-z0-9]+$"}, "additionalProperties": {"type": "string"}}, "open": {"type": "boolean", "description": "Opens the url in default browser.", "default": false, "alias": "o"}, "verbose": {"type": "boolean", "description": "Adds more details to output logging."}, "liveReload": {"type": "boolean", "description": "Whether to reload the page on change, using live-reload.", "default": true}, "publicHost": {"type": "string", "description": "The URL that the browser client (or live-reload client, if enabled) should use to connect to the development server. Use for a complex dev server setup, such as one with reverse proxies. This option has no effect when using the 'application' or other esbuild-based builders."}, "allowedHosts": {"type": "array", "description": "List of hosts that are allowed to access the dev server.", "default": [], "items": {"type": "string"}}, "servePath": {"type": "string", "description": "The pathname where the application will be served."}, "disableHostCheck": {"type": "boolean", "description": "Don't verify connected clients are part of allowed hosts.", "default": false}, "hmr": {"type": "boolean", "description": "Enable hot module replacement."}, "watch": {"type": "boolean", "description": "Rebuild on change.", "default": true}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "inspect": {"default": false, "description": "Activate debugging inspector. This option only has an effect when 'SSR' or 'SSG' are enabled.", "oneOf": [{"type": "string", "description": "Activate the inspector on host and port in the format of `[[host:]port]`. See the security warning in https://nodejs.org/docs/latest-v22.x/api/cli.html#warning-binding-inspector-to-a-public-ipport-combination-is-insecure regarding the host parameter usage."}, {"type": "boolean"}]}, "forceEsbuild": {"type": "boolean", "description": "Force the development server to use the 'browser-esbuild' builder when building.", "default": false}, "prebundle": {"description": "Enable and control the Vite-based development server's prebundling capabilities. To enable prebundling, the Angular CLI cache must also be enabled. This option has no effect when using the 'browser' or other Webpack-based builders.", "oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"exclude": {"description": "List of package imports that should not be prebundled by the development server. The packages will be bundled into the application code itself.", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}]}}, "additionalProperties": false}, "AngularBuildBuildersExtractI18nSchema": {"title": "Extract i18n Target", "description": "Extract i18n target options for Build Facade.", "type": "object", "properties": {"buildTarget": {"type": "string", "description": "A builder target to extract i18n messages in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^[^:\\s]*:[^:\\s]*(:[^\\s]+)?$"}, "format": {"type": "string", "description": "Output format for the generated file.", "default": "xlf", "enum": ["xmb", "xlf", "xlif", "xliff", "xlf2", "xliff2", "json", "arb", "legacy-migrate"]}, "progress": {"type": "boolean", "description": "Log progress to the console.", "default": true}, "outputPath": {"type": "string", "description": "Path where output will be placed."}, "outFile": {"type": "string", "description": "Name of the file to output."}, "i18nDuplicateTranslation": {"type": "string", "description": "How to handle duplicate translations.", "enum": ["error", "warning", "ignore"]}}, "additionalProperties": false}, "AngularDevkitBuildAngularBuildersExtractI18nSchema": {"title": "Extract i18n Target", "description": "Extract i18n target options for Build Facade.", "type": "object", "properties": {"buildTarget": {"type": "string", "description": "A builder target to extract i18n messages in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^[^:\\s]*:[^:\\s]*(:[^\\s]+)?$"}, "format": {"type": "string", "description": "Output format for the generated file.", "default": "xlf", "enum": ["xmb", "xlf", "xlif", "xliff", "xlf2", "xliff2", "json", "arb", "legacy-migrate"]}, "progress": {"type": "boolean", "description": "Log progress to the console.", "default": true}, "outputPath": {"type": "string", "description": "Path where output will be placed."}, "outFile": {"type": "string", "description": "Name of the file to output."}, "i18nDuplicateTranslation": {"type": "string", "description": "How to handle duplicate translations.", "enum": ["error", "warning", "ignore"]}}, "additionalProperties": false}, "AngularBuildBuildersUnitTestSchema": {"title": "Unit testing", "description": "Unit testing options for Angular applications.", "type": "object", "properties": {"buildTarget": {"type": "string", "description": "A build builder target to serve in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^[^:\\s]*:[^:\\s]*(:[^\\s]+)?$"}, "tsConfig": {"type": "string", "description": "The name of the TypeScript configuration file."}, "runner": {"type": "string", "description": "The name of the test runner to use for test execution.", "enum": ["karma", "vitest"]}, "browsers": {"description": "A list of browsers to use for test execution. If undefined, jsdom on Node.js will be used instead of a browser.", "type": "array", "items": {"type": "string"}, "minItems": 1}, "include": {"type": "array", "items": {"type": "string"}, "default": ["**/*.spec.ts"], "description": "Globs of files to include, relative to project root. \nThere are 2 special cases:\n - when a path to directory is provided, all spec files ending \".spec.@(ts|tsx)\" will be included\n - when a path to a file is provided, and a matching spec file exists it will be included instead."}, "exclude": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Globs of files to exclude, relative to the project root."}, "watch": {"type": "boolean", "description": "Run build when files change."}, "debug": {"type": "boolean", "description": "Initialize the test runner to support using the Node Inspector for test debugging.", "default": false}, "codeCoverage": {"type": "boolean", "description": "Output a code coverage report.", "default": false}, "codeCoverageExclude": {"type": "array", "description": "Globs to exclude from code coverage.", "items": {"type": "string"}, "default": []}, "reporters": {"type": "array", "description": "Test runner reporters to use. Directly passed to the test runner.", "items": {"type": "string"}}, "providersFile": {"type": "string", "description": "TypeScript file that exports an array of Angular providers to use during test execution. The array must be a default export.", "minLength": 1}}, "additionalProperties": false}, "AngularBuildBuildersKarmaSchema": {"title": "Karma Target", "description": "Karma target options for Build Facade.", "type": "object", "properties": {"main": {"type": "string", "description": "The name of the main entry-point file."}, "tsConfig": {"type": "string", "description": "The name of the TypeScript configuration file."}, "karmaConfig": {"type": "string", "description": "The name of the Karma configuration file."}, "polyfills": {"description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "type": "array", "items": {"type": "string", "uniqueItems": true}, "default": []}, "assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/AngularBuildBuildersKarmaSchema/definitions/assetPattern"}}, "scripts": {"description": "Global scripts to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}]}}, "styles": {"description": "Global styles to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}]}}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors.", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}, "sass": {"description": "Options to pass to the sass preprocessor.", "type": "object", "properties": {"fatalDeprecations": {"description": "A set of deprecations to treat as fatal. If a deprecation warning of any provided type is encountered during compilation, the compiler will error instead. If a Version is provided, then all deprecations that were active in that compiler version will be treated as fatal.", "type": "array", "items": {"type": "string"}}, "silenceDeprecations": {"description": " A set of active deprecations to ignore. If a deprecation warning of any provided type is encountered during compilation, the compiler will ignore it instead.", "type": "array", "items": {"type": "string"}}, "futureDeprecations": {"description": "A set of future deprecations to opt into early. Future deprecations passed here will be treated as active by the compiler, emitting warnings as necessary.", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "additionalProperties": false}, "externalDependencies": {"description": "Exclude the listed external dependencies from being bundled into the bundle. Instead, the created bundle relies on these dependencies to be available during runtime.", "type": "array", "items": {"type": "string"}, "default": []}, "loader": {"description": "Defines the type of loader to use with a specified file extension when used with a JavaScript `import`. `text` inlines the content as a string; `binary` inlines the content as a Uint8Array; `file` emits the file and provides the runtime location of the file; `empty` considers the content to be empty and not include it in bundles.", "type": "object", "patternProperties": {"^\\.\\S+$": {"enum": ["text", "binary", "file", "empty"]}}}, "define": {"description": "Defines global identifiers that will be replaced with a specified constant value when found in any JavaScript or TypeScript code including libraries. The value will be used directly. String values must be put in quotes. Identifiers within Angular metadata such as Component Decorators will not be replaced.", "type": "object", "additionalProperties": {"type": "string"}}, "include": {"type": "array", "items": {"type": "string"}, "default": ["**/*.spec.ts"], "description": "Globs of files to include, relative to project root. \nThere are 2 special cases:\n - when a path to directory is provided, all spec files ending \".spec.@(ts|tsx)\" will be included\n - when a path to a file is provided, and a matching spec file exists it will be included instead."}, "exclude": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Globs of files to exclude, relative to the project root."}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.dev/reference/configs/workspace-config#source-map-configuration.", "default": true, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "watch": {"type": "boolean", "description": "Run build when files change."}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "browsers": {"description": "Override which browsers tests are run against. Set to `false` to not use any browser.", "oneOf": [{"type": "string", "description": "A comma seperate list of browsers to run tests against."}, {"const": false, "type": "boolean", "description": "Does use run tests against a browser."}]}, "codeCoverage": {"type": "boolean", "description": "Output a code coverage report.", "default": false}, "codeCoverageExclude": {"type": "array", "description": "Globs to exclude from code coverage.", "items": {"type": "string"}, "default": []}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"$ref": "#/definitions/AngularBuildBuildersKarmaSchema/definitions/fileReplacement"}, "default": []}, "reporters": {"type": "array", "description": "Karma reporters to use. Directly passed to the karma runner.", "items": {"type": "string"}}, "webWorkerTsConfig": {"type": "string", "description": "TypeScript configuration for Web Worker modules."}, "aot": {"type": "boolean", "description": "Run tests using Ahead of Time compilation.", "default": false}}, "additionalProperties": false, "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "output": {"type": "string", "default": "", "description": "Absolute path within the output."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, {"type": "string"}]}, "fileReplacement": {"type": "object", "properties": {"replace": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "with": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false}}}, "AngularDevkitBuildAngularBuildersKarmaSchema": {"title": "Karma Target", "description": "Karma target options for Build Facade.", "type": "object", "properties": {"main": {"type": "string", "description": "The name of the main entry-point file."}, "tsConfig": {"type": "string", "description": "The name of the TypeScript configuration file."}, "karmaConfig": {"type": "string", "description": "The name of the Karma configuration file."}, "polyfills": {"description": "Polyfills to be included in the build.", "oneOf": [{"type": "array", "description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "items": {"type": "string", "uniqueItems": true}, "default": []}, {"type": "string", "description": "The full path for the polyfills file, relative to the current workspace or a module specifier. Example: 'zone.js'."}]}, "assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersKarmaSchema/definitions/assetPattern"}}, "scripts": {"description": "Global scripts to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}]}}, "styles": {"description": "Global styles to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}]}}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false}, "include": {"type": "array", "items": {"type": "string"}, "default": ["**/*.spec.ts"], "description": "Globs of files to include, relative to project root. \nThere are 2 special cases:\n - when a path to directory is provided, all spec files ending \".spec.@(ts|tsx)\" will be included\n - when a path to a file is provided, and a matching spec file exists it will be included instead."}, "exclude": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Globs of files to exclude, relative to the project root."}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.dev/reference/configs/workspace-config#source-map-configuration.", "default": true, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "watch": {"type": "boolean", "description": "Run build when files change."}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "browsers": {"description": "Override which browsers tests are run against. Set to `false` to not use any browser.", "oneOf": [{"type": "string", "description": "A comma seperate list of browsers to run tests against."}, {"const": false, "type": "boolean", "description": "Does use run tests against a browser."}]}, "codeCoverage": {"type": "boolean", "description": "Output a code coverage report.", "default": false}, "codeCoverageExclude": {"type": "array", "description": "Globs to exclude from code coverage.", "items": {"type": "string"}, "default": []}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"oneOf": [{"type": "object", "properties": {"src": {"type": "string"}, "replaceWith": {"type": "string"}}, "additionalProperties": false}, {"type": "object", "properties": {"replace": {"type": "string"}, "with": {"type": "string"}}, "additionalProperties": false}]}, "default": []}, "reporters": {"type": "array", "description": "Karma reporters to use. Directly passed to the karma runner.", "items": {"type": "string"}}, "builderMode": {"type": "string", "description": "Determines how to build the code under test. If set to 'detect', attempts to follow the development builder.", "enum": ["detect", "browser", "application"], "default": "browser"}, "webWorkerTsConfig": {"type": "string", "description": "TypeScript configuration for Web Worker modules."}, "aot": {"type": "boolean", "description": "Run tests using Ahead of Time compilation.", "default": false}}, "additionalProperties": false, "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "output": {"type": "string", "default": "", "description": "Absolute path within the output."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, {"type": "string"}]}}}, "AngularDevkitBuildAngularBuildersJestSchema": {"title": "Jest browser schema for Build Facade.", "description": "Jest target options", "type": "object", "properties": {"include": {"type": "array", "items": {"type": "string"}, "default": ["**/*.spec.ts"], "description": "Globs of files to include, relative to project root."}, "exclude": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Globs of files to exclude, relative to the project root."}, "tsConfig": {"type": "string", "description": "The name of the TypeScript configuration file."}, "polyfills": {"type": "array", "description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "items": {"type": "string", "uniqueItems": true}, "default": []}, "aot": {"type": "boolean", "description": "Run tests using Ahead of Time compilation.", "default": false}}, "additionalProperties": false}, "AngularDevkitBuildAngularBuildersWebTestRunnerSchema": {"title": "Web Test Runner Target", "description": "Web Test Runner target options for Build Facade.", "type": "object", "properties": {"main": {"type": "string", "description": "The name of the main entry-point file."}, "tsConfig": {"type": "string", "description": "The name of the TypeScript configuration file."}, "polyfills": {"description": "Polyfills to be included in the build.", "oneOf": [{"type": "array", "description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "items": {"type": "string", "uniqueItems": true}, "default": []}, {"type": "string", "description": "The full path for the polyfills file, relative to the current workspace or a module specifier. Example: 'zone.js'."}]}, "assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersWebTestRunnerSchema/definitions/assetPattern"}}, "scripts": {"description": "Global scripts to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}]}}, "styles": {"description": "Global styles to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false}, {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}]}}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false}, "include": {"type": "array", "items": {"type": "string"}, "default": ["**/*.spec.ts"], "description": "Globs of files to include, relative to project root. \nThere are 2 special cases:\n - when a path to directory is provided, all spec files ending \".spec.@(ts|tsx)\" will be included\n - when a path to a file is provided, and a matching spec file exists it will be included instead."}, "exclude": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Globs of files to exclude, relative to the project root."}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.dev/reference/configs/workspace-config#source-map-configuration.", "default": true, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "watch": {"type": "boolean", "description": "Run build when files change."}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "browsers": {"type": "string", "description": "Override which browsers tests are run against."}, "codeCoverage": {"type": "boolean", "description": "Output a code coverage report.", "default": false}, "codeCoverageExclude": {"type": "array", "description": "Globs to exclude from code coverage.", "items": {"type": "string"}, "default": []}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"oneOf": [{"type": "object", "properties": {"src": {"type": "string"}, "replaceWith": {"type": "string"}}, "additionalProperties": false}, {"type": "object", "properties": {"replace": {"type": "string"}, "with": {"type": "string"}}, "additionalProperties": false}]}, "default": []}, "webWorkerTsConfig": {"type": "string", "description": "TypeScript configuration for Web Worker modules."}, "aot": {"type": "boolean", "description": "Run tests using Ahead of Time compilation.", "default": false}}, "additionalProperties": false, "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "output": {"type": "string", "default": "", "description": "Absolute path within the output."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, {"type": "string"}]}}}, "AngularDevkitBuildAngularBuildersPrerenderSchema": {"title": "Prerender Target", "type": "object", "properties": {"browserTarget": {"type": "string", "description": "Target to build.", "pattern": "^[^:\\s]+:[^:\\s]+(:[^\\s]+)?$"}, "serverTarget": {"type": "string", "description": "Server target to use for prerendering the app.", "pattern": "^[^:\\s]+:[^:\\s]+(:[^\\s]+)?$"}, "routesFile": {"type": "string", "description": "The path to a file that contains a list of all routes to prerender, separated by newlines. This option is useful if you want to prerender routes with parameterized URLs."}, "routes": {"type": "array", "description": "The routes to render.", "items": {"minItems": 1, "type": "string", "uniqueItems": true}, "default": []}, "discoverRoutes": {"type": "boolean", "description": "Whether the builder should process the Angular Router configuration to find all unparameterized routes and prerender them.", "default": true}}, "anyOf": [{}, {}], "additionalProperties": false}, "AngularDevkitBuildAngularBuildersSsrDevServerSchema": {"title": "SSR Dev Server Target", "description": "SSR Dev Server target options for Build Facade.", "type": "object", "properties": {"browserTarget": {"type": "string", "description": "Browser target to build.", "pattern": ".+:.+(:.+)?"}, "serverTarget": {"type": "string", "description": "Server target to build.", "pattern": ".+:.+(:.+)?"}, "host": {"type": "string", "description": "Host to listen on.", "default": "localhost"}, "port": {"type": "number", "default": 4200, "description": "Port to start the development server at. Default is 4200. Pass 0 to get a dynamically assigned port."}, "watch": {"type": "boolean", "description": "Rebuild on change.", "default": true}, "publicHost": {"type": "string", "description": "The URL that the browser client should use to connect to the development server. Use for a complex dev server setup, such as one with reverse proxies."}, "open": {"type": "boolean", "description": "Opens the url in default browser.", "default": false, "alias": "o"}, "progress": {"type": "boolean", "description": "Log progress to the console while building."}, "inspect": {"type": "boolean", "description": "Launch the development server in inspector mode and listen on address and port '127.0.0.1:9229'.", "default": false}, "ssl": {"type": "boolean", "description": "Serve using HTTPS.", "default": false}, "sslKey": {"type": "string", "description": "SSL key to use for serving HTTPS."}, "sslCert": {"type": "string", "description": "SSL certificate to use for serving HTTPS."}, "proxyConfig": {"type": "string", "description": "Proxy configuration file."}, "verbose": {"type": "boolean", "description": "Adds more details to output logging."}}, "additionalProperties": false}, "AngularDevkitBuildAngularBuildersServerSchema": {"title": "Universal Target", "type": "object", "properties": {"assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersServerSchema/definitions/assetPattern"}}, "main": {"type": "string", "description": "The name of the main entry-point file."}, "tsConfig": {"type": "string", "default": "tsconfig.app.json", "description": "The name of the TypeScript configuration file."}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}}, "additionalProperties": false}, "optimization": {"description": "Enables optimization of the build output. Including minification of scripts and styles, tree-shaking and dead-code elimination. For more information, see https://angular.dev/reference/configs/workspace-config#optimization-configuration.", "default": true, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Enables optimization of the scripts output.", "default": true}, "styles": {"type": "boolean", "description": "Enables optimization of the styles output.", "default": true}}, "additionalProperties": false}, {"type": "boolean"}]}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"$ref": "#/definitions/AngularDevkitBuildAngularBuildersServerSchema/definitions/fileReplacement"}, "default": []}, "outputPath": {"type": "string", "description": "Path where output will be placed."}, "resourcesOutputPath": {"type": "string", "description": "The path where style resources will be placed, relative to outputPath."}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.dev/reference/configs/workspace-config#source-map-configuration.", "default": false, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "hidden": {"type": "boolean", "description": "Output source maps used for error reporting tools.", "default": false}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "deployUrl": {"type": "string", "description": "Customize the base path for the URLs of resources in 'index.html' and component stylesheets. This option is only necessary for specific deployment scenarios, such as with Angular Elements or when utilizing different CDN locations."}, "vendorChunk": {"type": "boolean", "description": "Generate a seperate bundle containing only vendor libraries. This option should only be used for development to reduce the incremental compilation time.", "default": false}, "verbose": {"type": "boolean", "description": "Adds more details to output logging.", "default": false}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "i18nMissingTranslation": {"type": "string", "description": "How to handle missing translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "i18nDuplicateTranslation": {"type": "string", "description": "How to handle duplicate translations for i18n.", "enum": ["warning", "error", "ignore"], "default": "warning"}, "localize": {"description": "Translate the bundles in one or more locales.", "oneOf": [{"type": "boolean", "description": "Translate all locales."}, {"type": "array", "description": "List of locales ID's to translate.", "minItems": 1, "items": {"type": "string", "pattern": "^[a-zA-Z]{2,3}(-[a-zA-Z]{4})?(-([a-zA-Z]{2}|[0-9]{3}))?(-[a-zA-Z]{5,8})?(-x(-[a-zA-Z0-9]{1,8})+)?$"}}]}, "outputHashing": {"type": "string", "description": "Define the output filename cache-busting hashing mode.", "default": "none", "enum": ["none", "all", "media", "bundles"]}, "deleteOutputPath": {"type": "boolean", "description": "Delete the output path before building.", "default": true}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "extractLicenses": {"type": "boolean", "description": "Extract all licenses in a separate file, in the case of production builds only.", "default": true}, "buildOptimizer": {"type": "boolean", "description": "Enables advanced build optimizations.", "default": true}, "namedChunks": {"type": "boolean", "description": "Use file name for lazy loaded chunks.", "default": false}, "externalDependencies": {"description": "Exclude the listed external dependencies from being bundled into the bundle. Instead, the created bundle relies on these dependencies to be available during runtime.", "type": "array", "items": {"type": "string"}, "default": []}, "statsJson": {"type": "boolean", "description": "Generates a 'stats.json' file which can be analyzed using tools such as 'webpack-bundle-analyzer'.", "default": false}, "watch": {"type": "boolean", "description": "Run build when files change.", "default": false}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}}, "additionalProperties": false, "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"followSymlinks": {"type": "boolean", "default": false, "description": "Allow glob patterns to follow symlink directories. This allows subdirectories of the symlink to be searched."}, "glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}, "output": {"type": "string", "default": "", "description": "Absolute path within the output."}}, "additionalProperties": false}, {"type": "string"}]}, "fileReplacement": {"oneOf": [{"type": "object", "properties": {"src": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "replaceWith": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false}, {"type": "object", "properties": {"replace": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "with": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false}]}}}, "AngularDevkitBuildAngularBuildersNgPackagrSchema": {"title": "ng-packagr Target", "description": "ng-packagr target options for Build Architect. Use to build library projects.", "type": "object", "properties": {"project": {"type": "string", "description": "The file path for the ng-packagr configuration file, relative to the current workspace."}, "tsConfig": {"type": "string", "description": "The full path for the TypeScript configuration file, relative to the current workspace."}, "watch": {"type": "boolean", "description": "Run build when files change.", "default": false}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}}, "additionalProperties": false}, "AngularBuildBuildersNgPackagrSchema": {"title": "ng-packagr Target", "description": "ng-packagr target options for Build Architect. Use to build library projects.", "type": "object", "properties": {"project": {"type": "string", "description": "The file path for the ng-packagr configuration file, relative to the current workspace."}, "tsConfig": {"type": "string", "description": "The full path for the TypeScript configuration file, relative to the current workspace."}, "watch": {"type": "boolean", "description": "Run build when files change.", "default": false}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}}, "additionalProperties": false}}}