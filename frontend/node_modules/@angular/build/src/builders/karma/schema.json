{"$schema": "http://json-schema.org/draft-07/schema", "title": "Karma Target", "description": "Karma target options for Build Facade.", "type": "object", "properties": {"main": {"type": "string", "description": "The name of the main entry-point file."}, "tsConfig": {"type": "string", "description": "The name of the TypeScript configuration file."}, "karmaConfig": {"type": "string", "description": "The name of the Karma configuration file."}, "polyfills": {"description": "A list of polyfills to include in the build. Can be a full path for a file, relative to the current workspace or module specifier. Example: 'zone.js'.", "type": "array", "items": {"type": "string", "uniqueItems": true}, "default": []}, "assets": {"type": "array", "description": "List of static application assets.", "default": [], "items": {"$ref": "#/definitions/assetPattern"}}, "scripts": {"description": "Global scripts to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false, "required": ["input"]}, {"type": "string", "description": "The file to include.", "pattern": "\\.[cm]?jsx?$"}]}}, "styles": {"description": "Global styles to be included in the build.", "type": "array", "default": [], "items": {"oneOf": [{"type": "object", "properties": {"input": {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}, "bundleName": {"type": "string", "pattern": "^[\\w\\-.]*$", "description": "The bundle name for this extra entry point."}, "inject": {"type": "boolean", "description": "If the bundle will be referenced in the HTML file.", "default": true}}, "additionalProperties": false, "required": ["input"]}, {"type": "string", "description": "The file to include.", "pattern": "\\.(?:css|scss|sass|less)$"}]}}, "inlineStyleLanguage": {"description": "The stylesheet language to use for the application's inline component styles.", "type": "string", "default": "css", "enum": ["css", "less", "sass", "scss"]}, "stylePreprocessorOptions": {"description": "Options to pass to style preprocessors.", "type": "object", "properties": {"includePaths": {"description": "Paths to include. Paths will be resolved to workspace root.", "type": "array", "items": {"type": "string"}, "default": []}, "sass": {"description": "Options to pass to the sass preprocessor.", "type": "object", "properties": {"fatalDeprecations": {"description": "A set of deprecations to treat as fatal. If a deprecation warning of any provided type is encountered during compilation, the compiler will error instead. If a Version is provided, then all deprecations that were active in that compiler version will be treated as fatal.", "type": "array", "items": {"type": "string"}}, "silenceDeprecations": {"description": " A set of active deprecations to ignore. If a deprecation warning of any provided type is encountered during compilation, the compiler will ignore it instead.", "type": "array", "items": {"type": "string"}}, "futureDeprecations": {"description": "A set of future deprecations to opt into early. Future deprecations passed here will be treated as active by the compiler, emitting warnings as necessary.", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}, "additionalProperties": false}, "externalDependencies": {"description": "Exclude the listed external dependencies from being bundled into the bundle. Instead, the created bundle relies on these dependencies to be available during runtime.", "type": "array", "items": {"type": "string"}, "default": []}, "loader": {"description": "Defines the type of loader to use with a specified file extension when used with a JavaScript `import`. `text` inlines the content as a string; `binary` inlines the content as a Uint8Array; `file` emits the file and provides the runtime location of the file; `empty` considers the content to be empty and not include it in bundles.", "type": "object", "patternProperties": {"^\\.\\S+$": {"enum": ["text", "binary", "file", "empty"]}}}, "define": {"description": "Defines global identifiers that will be replaced with a specified constant value when found in any JavaScript or TypeScript code including libraries. The value will be used directly. String values must be put in quotes. Identifiers within Angular metadata such as Component Decorators will not be replaced.", "type": "object", "additionalProperties": {"type": "string"}}, "include": {"type": "array", "items": {"type": "string"}, "default": ["**/*.spec.ts"], "description": "Globs of files to include, relative to project root. \nThere are 2 special cases:\n - when a path to directory is provided, all spec files ending \".spec.@(ts|tsx)\" will be included\n - when a path to a file is provided, and a matching spec file exists it will be included instead."}, "exclude": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Globs of files to exclude, relative to the project root."}, "sourceMap": {"description": "Output source maps for scripts and styles. For more information, see https://angular.dev/reference/configs/workspace-config#source-map-configuration.", "default": true, "oneOf": [{"type": "object", "properties": {"scripts": {"type": "boolean", "description": "Output source maps for all scripts.", "default": true}, "styles": {"type": "boolean", "description": "Output source maps for all styles.", "default": true}, "vendor": {"type": "boolean", "description": "Resolve vendor packages source maps.", "default": false}}, "additionalProperties": false}, {"type": "boolean"}]}, "progress": {"type": "boolean", "description": "Log progress to the console while building.", "default": true}, "watch": {"type": "boolean", "description": "Run build when files change."}, "poll": {"type": "number", "description": "Enable and define the file watching poll time period in milliseconds."}, "preserveSymlinks": {"type": "boolean", "description": "Do not use the real path when resolving modules. If unset then will default to `true` if NodeJS option --preserve-symlinks is set."}, "browsers": {"description": "Override which browsers tests are run against. Set to `false` to not use any browser.", "oneOf": [{"type": "string", "description": "A comma seperate list of browsers to run tests against."}, {"const": false, "type": "boolean", "description": "Does use run tests against a browser."}]}, "codeCoverage": {"type": "boolean", "description": "Output a code coverage report.", "default": false}, "codeCoverageExclude": {"type": "array", "description": "Globs to exclude from code coverage.", "items": {"type": "string"}, "default": []}, "fileReplacements": {"description": "Replace compilation source files with other compilation source files in the build.", "type": "array", "items": {"$ref": "#/definitions/fileReplacement"}, "default": []}, "reporters": {"type": "array", "description": "Karma reporters to use. Directly passed to the karma runner.", "items": {"type": "string"}}, "webWorkerTsConfig": {"type": "string", "description": "TypeScript configuration for Web Worker modules."}, "aot": {"type": "boolean", "description": "Run tests using Ahead of Time compilation.", "default": false}}, "additionalProperties": false, "required": ["tsConfig"], "definitions": {"assetPattern": {"oneOf": [{"type": "object", "properties": {"glob": {"type": "string", "description": "The pattern to match."}, "input": {"type": "string", "description": "The input directory path in which to apply 'glob'. Defaults to the project root."}, "output": {"type": "string", "default": "", "description": "Absolute path within the output."}, "ignore": {"description": "An array of globs to ignore.", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "required": ["glob", "input"]}, {"type": "string"}]}, "fileReplacement": {"type": "object", "properties": {"replace": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}, "with": {"type": "string", "pattern": "\\.(([cm]?[jt])sx?|json)$"}}, "additionalProperties": false, "required": ["replace", "with"]}}}